@extends('layouts.app')

@section('content')
<div class="flow-builder-container">
    <!-- Modern Header -->
    <div class="flow-builder-header">
        <h1>Create WhatsApp Flow</h1>
    </div>

    <div id="statusNotification" style="display: none;" class="alert" role="alert"></div>

    <!-- Modern Form Container -->
    <div class="flow-builder-form">
        <form method="POST" action="{{ route('whatsapp.flows.create') }}" id="createFlowForm">
                @csrf
                
                <fieldset>
                    <legend>General Information</legend>
                    <div class="row">
                        <!-- Flow Name -->
                        <div class="form-group col-md-6">
                            <label for="name">Flow Name</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   name="name" id="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Categories - Single select dropdown -->
                        <div class="form-group col-md-6">
                            <label for="category">Category</label>
                            <select class="form-control @error('categories') is-invalid @enderror" 
                                    id="category" name="categories[]" required>
                                <option value="">Select a category</option>
                                <option value="SIGN_UP" {{ in_array('SIGN_UP', old('categories', [])) ? 'selected' : '' }}>Sign Up</option>
                                <option value="SIGN_IN" {{ in_array('SIGN_IN', old('categories', [])) ? 'selected' : '' }}>Sign In</option>
                                <option value="APPOINTMENT_BOOKING" {{ in_array('APPOINTMENT_BOOKING', old('categories', [])) ? 'selected' : '' }}>Appointment Booking</option>
                                <option value="LEAD_GENERATION" {{ in_array('LEAD_GENERATION', old('categories', [])) ? 'selected' : '' }}>Lead Generation</option>
                                <option value="CONTACT_US" {{ in_array('CONTACT_US', old('categories', [])) ? 'selected' : '' }}>Contact Us</option>
                                <option value="CUSTOMER_SUPPORT" {{ in_array('CUSTOMER_SUPPORT', old('categories', [])) ? 'selected' : '' }}>Customer Support</option>
                                <option value="SURVEY" {{ in_array('SURVEY', old('categories', [])) ? 'selected' : '' }}>Survey</option>
                                <option value="OTHER" {{ in_array('OTHER', old('categories', [])) ? 'selected' : '' }}>Other</option>
                            </select>
                            <small class="form-text text-muted">Select a category for your flow</small>
                            @error('categories')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </fieldset>

                <fieldset class="mt-4">
                    <legend>Flow Configuration</legend>

                    <!-- Flow Editor Toggle -->
                    <div class="form-group mb-3">
                        <div class="btn-group" role="group" aria-label="Editor Mode">
                            <input type="radio" class="btn-check" name="editor_mode" id="visual_editor" value="visual" checked>
                            <label class="btn btn-outline-primary" for="visual_editor">
                                <i class="fa fa-paint-brush"></i> Visual Editor
                            </label>

                            <input type="radio" class="btn-check" name="editor_mode" id="json_editor" value="json">
                            <label class="btn btn-outline-primary" for="json_editor">
                                <i class="fa fa-code"></i> JSON Editor
                            </label>
                        </div>
                    </div>

                    <!-- Visual Flow Editor -->
                    <div id="visual_flow_editor" class="flow-editor-container">
                        <div class="row">
                            <!-- Component Library -->
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Components</h6>
                                    </div>
                                    <div class="card-body p-2">
                                        <div class="component-library">
                                            <div class="component-category">
                                                <h6 class="text-muted small">Layout</h6>
                                                <div class="component-item" data-component="SingleColumnLayout">
                                                    <i class="fa fa-columns"></i> Single Column
                                                </div>
                                            </div>

                                            <div class="component-category mt-3">
                                                <h6 class="text-muted small">Text</h6>
                                                <div class="component-item" data-component="TextHeading">
                                                    <i class="fa fa-heading"></i> Heading
                                                </div>
                                                <div class="component-item" data-component="TextBody">
                                                    <i class="fa fa-paragraph"></i> Body Text
                                                </div>
                                                <div class="component-item" data-component="TextCaption">
                                                    <i class="fa fa-font"></i> Caption
                                                </div>
                                            </div>

                                            <div class="component-category mt-3">
                                                <h6 class="text-muted small">Input</h6>
                                                <div class="component-item" data-component="TextInput">
                                                    <i class="fa fa-keyboard"></i> Text Input
                                                </div>
                                                <div class="component-item" data-component="TextArea">
                                                    <i class="fa fa-align-left"></i> Text Area
                                                </div>
                                                <div class="component-item" data-component="CheckboxGroup">
                                                    <i class="fa fa-check-square"></i> Checkbox
                                                </div>
                                                <div class="component-item" data-component="RadioButtonsGroup">
                                                    <i class="fa fa-dot-circle"></i> Radio Button
                                                </div>
                                                <div class="component-item" data-component="Dropdown">
                                                    <i class="fa fa-caret-down"></i> Dropdown
                                                </div>
                                                <div class="component-item" data-component="DatePicker">
                                                    <i class="fa fa-calendar"></i> Date Picker
                                                </div>
                                            </div>

                                            <div class="component-category mt-3">
                                                <h6 class="text-muted small">Actions</h6>
                                                <div class="component-item" data-component="Footer">
                                                    <i class="fa fa-hand-pointer"></i> Footer Button
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Flow Canvas -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">Flow Designer</h6>
                                        <div class="btn-group btn-group-sm">
                                            <div class="dropdown">
                                                <button class="btn btn-outline-primary dropdown-toggle" type="button" id="templatesDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fa fa-magic"></i> Templates
                                                </button>
                                                <div class="dropdown-menu">
                                                    <a class="dropdown-item" href="#" onclick="loadTemplate('contact_form')">Contact Form</a>
                                                    <a class="dropdown-item" href="#" onclick="loadTemplate('survey')">Survey</a>
                                                    <a class="dropdown-item" href="#" onclick="loadTemplate('appointment')">Appointment Booking</a>
                                                    <a class="dropdown-item" href="#" onclick="loadTemplate('lead_gen')">Lead Generation</a>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-outline-success" id="import_json_btn">
                                                <i class="fa fa-upload"></i> Import
                                            </button>
                                            <button type="button" class="btn btn-outline-info" id="export_json_btn">
                                                <i class="fa fa-download"></i> Export
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" id="add_screen_btn">
                                                <i class="fa fa-plus"></i> Add Screen
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" id="clear_flow_btn">
                                                <i class="fa fa-trash"></i> Clear
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body p-0">
                                        <div id="flow_canvas" class="flow-canvas">
                                            <!-- Screens will be dynamically added here -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Properties Panel -->
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Properties</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="properties_panel">
                                            <p class="text-muted small">Select a component or screen to edit its properties</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- JSON Editor (Hidden by default) -->
                    <div id="json_flow_editor" class="form-group" style="display: none;">
                        <label for="flow_json">Flow JSON</label>
                        <div class="position-relative">
                            <textarea class="form-control @error('flow_json') is-invalid @enderror"
                                    id="flow_json" name="flow_json" rows="12" required>{{ old('flow_json', '') }}</textarea>
                            <div class="mt-2 text-end">
                                <button type="button" class="btn btn-outline-secondary" id="format_json_btn">
                                    <i class="fa fa-indent"></i> Format JSON
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="validate_json_btn">
                                    <i class="fa fa-check"></i> Validate
                                </button>
                            </div>
                        </div>
                        @error('flow_json')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Flow configuration in JSON format</small>
                    </div>

                    <!-- Hidden file input for importing JSON -->
                    <input type="file" id="import_file_input" accept=".json" style="display: none;">

                    <!-- Publish Setting -->
                    <div class="form-group mt-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="publish" 
                                id="publish" value="1" {{ old('publish') ? 'checked' : '' }}>
                            <label class="form-check-label" for="publish">
                                Publish Flow Immediately
                            </label>
                        </div>
                    </div>
                </fieldset>

                <!-- Submit Buttons -->
                <div class="mt-4">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('whatsapp-flows.index') }}" class="btn btn-secondary">
                            <i class="fa fa-arrow-left"></i> Back to Flows
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fa fa-save"></i> Create Flow
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('appScripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createFlowForm');
    const nameInput = document.getElementById('name');
    const categorySelect = document.getElementById('category');
    const flowJsonTextarea = document.getElementById('flow_json');
    const submitBtn = document.getElementById('submitBtn');

    // Check for flash messages and display them using notification
    @if(session('error'))
        showNotification("{{ session('error') }}", 'error');
    @endif

    @if(session('success'))
        showNotification("{{ session('success') }}", 'success');
    @endif

    // Function to show notifications
    function showNotification(message, type) {
        const statusNotification = document.getElementById('statusNotification');
        statusNotification.innerText = message;
        statusNotification.style.display = 'block';

        if (type === 'error') {
            statusNotification.classList.add('alert-danger');
            statusNotification.classList.remove('alert-success');
        } else {
            statusNotification.classList.add('alert-success');
            statusNotification.classList.remove('alert-danger');
        }

        // Automatically hide after 5 seconds
        setTimeout(() => {
            statusNotification.style.display = 'none';
        }, 5000);
    }

    // Flow Editor State
    let flowData = {
        version: "5.0",
        screens: []
    };
    let selectedComponent = null;
    let screenCounter = 0;

    // Component Templates
    const componentTemplates = {
        TextHeading: {
            type: "TextHeading",
            text: "Heading Text"
        },
        TextBody: {
            type: "TextBody",
            text: "Body text content"
        },
        TextCaption: {
            type: "TextCaption",
            text: "Caption text"
        },
        TextInput: {
            type: "TextInput",
            label: "Input Label",
            "input-type": "text",
            name: "input_name",
            required: true
        },
        TextArea: {
            type: "TextArea",
            label: "Text Area Label",
            name: "textarea_name",
            required: false
        },
        CheckboxGroup: {
            type: "CheckboxGroup",
            label: "Checkbox Group",
            name: "checkbox_group",
            "data-source": [
                { id: "option1", title: "Option 1" },
                { id: "option2", title: "Option 2" }
            ]
        },
        RadioButtonsGroup: {
            type: "RadioButtonsGroup",
            label: "Radio Group",
            name: "radio_group",
            "data-source": [
                { id: "option1", title: "Option 1" },
                { id: "option2", title: "Option 2" }
            ]
        },
        Dropdown: {
            type: "Dropdown",
            label: "Dropdown Label",
            name: "dropdown_name",
            "data-source": [
                { id: "option1", title: "Option 1" },
                { id: "option2", title: "Option 2" }
            ]
        },
        DatePicker: {
            type: "DatePicker",
            label: "Date Picker",
            name: "date_picker"
        },
        Footer: {
            type: "Footer",
            label: "Continue",
            "on-click-action": {
                name: "complete",
                payload: {}
            }
        }
    };

    // Initialize Bootstrap dropdowns
    if (typeof $ !== 'undefined' && $.fn.dropdown) {
        $('.dropdown-toggle').dropdown();
    }

    // Initialize flow editor
    initializeFlowEditor();

    function initializeFlowEditor() {
        // Editor mode toggle
        const visualEditor = document.getElementById('visual_editor');
        const jsonEditor = document.getElementById('json_editor');
        const visualContainer = document.getElementById('visual_flow_editor');
        const jsonContainer = document.getElementById('json_flow_editor');

        visualEditor.addEventListener('change', function() {
            if (this.checked) {
                visualContainer.style.display = 'block';
                jsonContainer.style.display = 'none';
                updateVisualFromJson();
            }
        });

        jsonEditor.addEventListener('change', function() {
            if (this.checked) {
                visualContainer.style.display = 'none';
                jsonContainer.style.display = 'block';
                updateJsonFromVisual();
            }
        });

        // Initialize with default screen
        addScreen();

        // Setup drag and drop
        setupDragAndDrop();

        // Setup canvas events
        setupCanvasEvents();

        // Setup JSON editor buttons
        setupJsonEditorButtons();
    }

    function setupDragAndDrop() {
        const componentItems = document.querySelectorAll('.component-item');

        componentItems.forEach(item => {
            item.draggable = true;

            item.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', this.dataset.component);
                this.classList.add('dragging');
            });

            item.addEventListener('dragend', function(e) {
                this.classList.remove('dragging');
            });
        });
    }

    function setupCanvasEvents() {
        const canvas = document.getElementById('flow_canvas');
        const addScreenBtn = document.getElementById('add_screen_btn');
        const clearFlowBtn = document.getElementById('clear_flow_btn');
        const importBtn = document.getElementById('import_json_btn');
        const exportBtn = document.getElementById('export_json_btn');
        const fileInput = document.getElementById('import_file_input');

        addScreenBtn.addEventListener('click', addScreen);
        clearFlowBtn.addEventListener('click', clearFlow);
        importBtn.addEventListener('click', importJson);
        exportBtn.addEventListener('click', exportJson);

        // File input change event
        fileInput.addEventListener('change', handleFileImport);

        // Canvas drop events
        canvas.addEventListener('dragover', function(e) {
            e.preventDefault();
        });

        canvas.addEventListener('drop', function(e) {
            e.preventDefault();
            // Handle drop on canvas (for new screens)
        });
    }

    function setupJsonEditorButtons() {
        const formatBtn = document.getElementById('format_json_btn');
        const validateBtn = document.getElementById('validate_json_btn');

        formatBtn.addEventListener('click', formatJson);
        validateBtn.addEventListener('click', validateJson);
    }

    function addScreen() {
        screenCounter++;
        const screenId = `SCREEN_${screenCounter}`;

        const screen = {
            id: screenId,
            title: `Screen ${screenCounter}`,
            layout: {
                type: "SingleColumnLayout",
                children: []
            },
            data: []
        };

        flowData.screens.push(screen);
        renderScreen(screen, flowData.screens.length - 1);
        updateJsonFromVisual();
    }

    function renderScreen(screen, index) {
        const canvas = document.getElementById('flow_canvas');

        const screenElement = document.createElement('div');
        screenElement.className = 'flow-screen';
        screenElement.dataset.screenIndex = index;
        screenElement.dataset.screenId = screen.id;

        screenElement.innerHTML = `
            <div class="screen-header">
                <h6 class="screen-title">${screen.title}</h6>
                <div class="screen-actions">
                    <button type="button" class="btn btn-sm btn-outline-primary edit-screen-btn">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-screen-btn">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="screen-content">
                <div class="drop-zone" data-screen-index="${index}">
                    Drop components here or click to add
                </div>
            </div>
        `;

        canvas.appendChild(screenElement);
        setupScreenEvents(screenElement, index);
        renderScreenComponents(screen, screenElement);
    }

    function setupScreenEvents(screenElement, screenIndex) {
        const dropZone = screenElement.querySelector('.drop-zone');
        const editBtn = screenElement.querySelector('.edit-screen-btn');
        const deleteBtn = screenElement.querySelector('.delete-screen-btn');

        // Drop zone events
        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('drag-over');
        });

        dropZone.addEventListener('dragleave', function(e) {
            this.classList.remove('drag-over');
        });

        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');

            const componentType = e.dataTransfer.getData('text/plain');
            addComponentToScreen(componentType, screenIndex);
        });

        // Screen actions
        editBtn.addEventListener('click', function() {
            selectScreen(screenIndex);
        });

        deleteBtn.addEventListener('click', function() {
            deleteScreen(screenIndex);
        });

        // Screen selection
        screenElement.addEventListener('click', function(e) {
            if (e.target.closest('.screen-actions')) return;
            selectScreen(screenIndex);
        });
    }

    function addComponentToScreen(componentType, screenIndex) {
        if (!componentTemplates[componentType]) return;

        const component = JSON.parse(JSON.stringify(componentTemplates[componentType]));
        const screen = flowData.screens[screenIndex];

        screen.layout.children.push(component);

        const screenElement = document.querySelector(`[data-screen-index="${screenIndex}"]`);
        renderScreenComponents(screen, screenElement);
        updateJsonFromVisual();
    }

    function renderScreenComponents(screen, screenElement) {
        const dropZone = screenElement.querySelector('.drop-zone');
        const screenContent = screenElement.querySelector('.screen-content');

        // Clear existing components (except drop zone)
        const existingComponents = screenContent.querySelectorAll('.flow-component');
        existingComponents.forEach(comp => comp.remove());

        if (screen.layout.children.length === 0) {
            dropZone.style.display = 'block';
            return;
        }

        dropZone.style.display = 'none';

        screen.layout.children.forEach((component, componentIndex) => {
            const componentElement = createComponentElement(component, componentIndex, screenElement.dataset.screenIndex);
            screenContent.appendChild(componentElement);
        });
    }

    function createComponentElement(component, componentIndex, screenIndex) {
        const div = document.createElement('div');
        div.className = 'flow-component';
        div.dataset.componentIndex = componentIndex;
        div.dataset.screenIndex = screenIndex;

        div.innerHTML = `
            <div class="component-controls">
                <button type="button" class="btn btn-sm btn-outline-primary edit-component-btn">
                    <i class="fa fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger delete-component-btn">
                    <i class="fa fa-trash"></i>
                </button>
            </div>
            <div class="component-preview">
                ${renderComponentPreview(component)}
            </div>
        `;

        // Setup component events
        const editBtn = div.querySelector('.edit-component-btn');
        const deleteBtn = div.querySelector('.delete-component-btn');

        editBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            selectComponent(screenIndex, componentIndex);
        });

        deleteBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            deleteComponent(screenIndex, componentIndex);
        });

        div.addEventListener('click', function() {
            selectComponent(screenIndex, componentIndex);
        });

        return div;
    }

    function renderComponentPreview(component) {
        switch (component.type) {
            case 'TextHeading':
                return `<h5>${component.text}</h5>`;
            case 'TextBody':
                return `<p>${component.text}</p>`;
            case 'TextCaption':
                return `<small class="text-muted">${component.text}</small>`;
            case 'TextInput':
                return `
                    <label class="form-label">${component.label}</label>
                    <input type="${component['input-type'] || 'text'}" class="form-control" placeholder="${component.label}" readonly>
                `;
            case 'TextArea':
                return `
                    <label class="form-label">${component.label}</label>
                    <textarea class="form-control" placeholder="${component.label}" readonly></textarea>
                `;
            case 'CheckboxGroup':
                const checkboxOptions = component['data-source'] || [];
                return `
                    <label class="form-label">${component.label}</label>
                    <div>
                        ${checkboxOptions.map(option => `
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" disabled>
                                <label class="form-check-label">${option.title}</label>
                            </div>
                        `).join('')}
                    </div>
                `;
            case 'RadioButtonsGroup':
                const radioOptions = component['data-source'] || [];
                return `
                    <label class="form-label">${component.label}</label>
                    <div>
                        ${radioOptions.map(option => `
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="preview_${component.name}" disabled>
                                <label class="form-check-label">${option.title}</label>
                            </div>
                        `).join('')}
                    </div>
                `;
            case 'Dropdown':
                const dropdownOptions = component['data-source'] || [];
                return `
                    <label class="form-label">${component.label}</label>
                    <select class="form-control" disabled>
                        <option>Select an option</option>
                        ${dropdownOptions.map(option => `<option>${option.title}</option>`).join('')}
                    </select>
                `;
            case 'DatePicker':
                return `
                    <label class="form-label">${component.label}</label>
                    <input type="date" class="form-control" readonly>
                `;
            case 'Footer':
                return `<button type="button" class="btn btn-primary w-100">${component.label}</button>`;
            default:
                return `<div class="text-muted">Unknown component: ${component.type}</div>`;
        }
    }

    function selectComponent(screenIndex, componentIndex) {
        // Remove previous selections
        document.querySelectorAll('.flow-component.selected').forEach(el => {
            el.classList.remove('selected');
        });
        document.querySelectorAll('.flow-screen.active').forEach(el => {
            el.classList.remove('active');
        });

        // Select current component
        const componentElement = document.querySelector(`[data-screen-index="${screenIndex}"][data-component-index="${componentIndex}"]`);
        if (componentElement) {
            componentElement.classList.add('selected');
            selectedComponent = { screenIndex, componentIndex };
            showComponentProperties(screenIndex, componentIndex);

            // Add a subtle animation to indicate selection
            componentElement.style.animation = 'none';
            setTimeout(() => {
                componentElement.style.animation = 'pulse 0.6s ease-in-out';
            }, 10);
        }
    }

    function selectScreen(screenIndex) {
        // Remove component selections
        document.querySelectorAll('.flow-component.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // Remove screen selections
        document.querySelectorAll('.flow-screen.active').forEach(el => {
            el.classList.remove('active');
        });

        // Select screen
        const screenElement = document.querySelector(`[data-screen-index="${screenIndex}"]`);
        if (screenElement) {
            screenElement.classList.add('active');
            selectedComponent = null;
            showScreenProperties(screenIndex);
        }
    }

    function showComponentProperties(screenIndex, componentIndex) {
        const component = flowData.screens[screenIndex].layout.children[componentIndex];
        const propertiesPanel = document.getElementById('properties_panel');

        propertiesPanel.innerHTML = `
            <h6>🎨 Component Properties</h6>
            <div class="component-type-badge">
                <span class="badge">${component.type}</span>
            </div>
            <form class="properties-form">
                ${generateComponentPropertiesForm(component, screenIndex, componentIndex)}
            </form>
        `;
    }

    function showScreenProperties(screenIndex) {
        const screen = flowData.screens[screenIndex];
        const propertiesPanel = document.getElementById('properties_panel');

        propertiesPanel.innerHTML = `
            <h6>📱 Screen Properties</h6>
            <div class="screen-info-badge">
                <span class="badge">Screen ${screenIndex + 1}</span>
            </div>
            <form class="properties-form">
                <div class="form-group">
                    <label>Screen ID</label>
                    <input type="text" class="form-control" value="${screen.id}" onchange="updateScreenProperty(${screenIndex}, 'id', this.value)" placeholder="Enter screen ID">
                </div>
                <div class="form-group">
                    <label>Screen Title</label>
                    <input type="text" class="form-control" value="${screen.title}" onchange="updateScreenProperty(${screenIndex}, 'title', this.value)" placeholder="Enter screen title">
                </div>
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" ${screen.terminal ? 'checked' : ''} onchange="updateScreenProperty(${screenIndex}, 'terminal', this.checked)">
                        <label class="form-check-label">Terminal Screen</label>
                        <small class="form-text text-muted">Mark as the final screen in the flow</small>
                    </div>
                </div>
            </form>
        `;
    }

    function generateComponentPropertiesForm(component, screenIndex, componentIndex) {
        let formHtml = `
            <div class="form-group">
                <label>Component Type</label>
                <input type="text" class="form-control" value="${component.type}" readonly>
            </div>
        `;

        // Common properties
        if (component.text !== undefined) {
            formHtml += `
                <div class="form-group">
                    <label>Text</label>
                    <input type="text" class="form-control" value="${component.text}"
                           onchange="updateComponentProperty(${screenIndex}, ${componentIndex}, 'text', this.value)">
                </div>
            `;
        }

        if (component.label !== undefined) {
            formHtml += `
                <div class="form-group">
                    <label>Label</label>
                    <input type="text" class="form-control" value="${component.label}"
                           onchange="updateComponentProperty(${screenIndex}, ${componentIndex}, 'label', this.value)">
                </div>
            `;
        }

        if (component.name !== undefined) {
            formHtml += `
                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control" value="${component.name}"
                           onchange="updateComponentProperty(${screenIndex}, ${componentIndex}, 'name', this.value)">
                </div>
            `;
        }

        if (component['input-type'] !== undefined) {
            formHtml += `
                <div class="form-group">
                    <label>Input Type</label>
                    <select class="form-control" onchange="updateComponentProperty(${screenIndex}, ${componentIndex}, 'input-type', this.value)">
                        <option value="text" ${component['input-type'] === 'text' ? 'selected' : ''}>Text</option>
                        <option value="email" ${component['input-type'] === 'email' ? 'selected' : ''}>Email</option>
                        <option value="phone" ${component['input-type'] === 'phone' ? 'selected' : ''}>Phone</option>
                        <option value="number" ${component['input-type'] === 'number' ? 'selected' : ''}>Number</option>
                    </select>
                </div>
            `;
        }

        if (component.required !== undefined) {
            formHtml += `
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" ${component.required ? 'checked' : ''}
                               onchange="updateComponentProperty(${screenIndex}, ${componentIndex}, 'required', this.checked)">
                        <label class="form-check-label">Required</label>
                    </div>
                </div>
            `;
        }

        return formHtml;
    }

    // Global functions for property updates
    window.updateComponentProperty = function(screenIndex, componentIndex, property, value) {
        const component = flowData.screens[screenIndex].layout.children[componentIndex];
        component[property] = value;

        // Re-render the component
        const screenElement = document.querySelector(`[data-screen-index="${screenIndex}"]`);
        renderScreenComponents(flowData.screens[screenIndex], screenElement);

        // Re-select the component
        setTimeout(() => selectComponent(screenIndex, componentIndex), 100);

        updateJsonFromVisual();
    };

    window.updateScreenProperty = function(screenIndex, property, value) {
        const screen = flowData.screens[screenIndex];
        screen[property] = value;

        if (property === 'title') {
            const screenElement = document.querySelector(`[data-screen-index="${screenIndex}"]`);
            const titleElement = screenElement.querySelector('.screen-title');
            titleElement.textContent = value;
        }

        updateJsonFromVisual();
    };

    function deleteComponent(screenIndex, componentIndex) {
        if (confirm('Are you sure you want to delete this component?')) {
            flowData.screens[screenIndex].layout.children.splice(componentIndex, 1);

            const screenElement = document.querySelector(`[data-screen-index="${screenIndex}"]`);
            renderScreenComponents(flowData.screens[screenIndex], screenElement);

            // Clear properties panel
            document.getElementById('properties_panel').innerHTML = '<p class="text-muted small">Select a component or screen to edit its properties</p>';

            updateJsonFromVisual();
        }
    }

    function deleteScreen(screenIndex) {
        if (confirm('Are you sure you want to delete this screen?')) {
            flowData.screens.splice(screenIndex, 1);
            refreshCanvas();
            updateJsonFromVisual();
        }
    }

    function clearFlow() {
        if (confirm('Are you sure you want to clear the entire flow?')) {
            flowData.screens = [];
            screenCounter = 0;
            refreshCanvas();
            addScreen(); // Add default screen
        }
    }

    function refreshCanvas() {
        const canvas = document.getElementById('flow_canvas');
        canvas.innerHTML = '';

        flowData.screens.forEach((screen, index) => {
            renderScreen(screen, index);
        });
    }

    function updateJsonFromVisual() {
        const jsonTextarea = document.getElementById('flow_json');
        jsonTextarea.value = JSON.stringify(flowData, null, 2);
    }

    function updateVisualFromJson() {
        try {
            const jsonValue = document.getElementById('flow_json').value;
            if (jsonValue.trim()) {
                flowData = JSON.parse(jsonValue);
                refreshCanvas();
            }
        } catch (error) {
            console.error('Invalid JSON:', error);
            showNotification('Invalid JSON format', 'error');
        }
    }

    function formatJson() {
        try {
            const jsonTextarea = document.getElementById('flow_json');
            const parsed = JSON.parse(jsonTextarea.value);
            jsonTextarea.value = JSON.stringify(parsed, null, 2);
            showNotification('JSON formatted successfully', 'success');
        } catch (error) {
            showNotification('Invalid JSON format', 'error');
        }
    }

    function validateJson() {
        try {
            const jsonTextarea = document.getElementById('flow_json');
            const parsed = JSON.parse(jsonTextarea.value);

            // Basic validation
            if (!parsed.version) {
                throw new Error('Missing version field');
            }
            if (!parsed.screens || !Array.isArray(parsed.screens)) {
                throw new Error('Missing or invalid screens array');
            }

            // Remove existing validation result
            const existingResult = document.querySelector('.json-validation-result');
            if (existingResult) {
                existingResult.remove();
            }

            // Show success message
            const validationResult = document.createElement('div');
            validationResult.className = 'json-validation-result valid';
            validationResult.textContent = '✓ JSON is valid';
            jsonTextarea.parentNode.appendChild(validationResult);

        } catch (error) {
            // Remove existing validation result
            const existingResult = document.querySelector('.json-validation-result');
            if (existingResult) {
                existingResult.remove();
            }

            // Show error message
            const validationResult = document.createElement('div');
            validationResult.className = 'json-validation-result invalid';
            validationResult.textContent = '✗ ' + error.message;
            document.getElementById('flow_json').parentNode.appendChild(validationResult);
        }
    }

    function importJson() {
        const fileInput = document.getElementById('import_file_input');
        fileInput.click();
    }

    function handleFileImport(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            showNotification('Please select a valid JSON file', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedData = JSON.parse(e.target.result);

                // Validate the imported JSON
                if (!importedData.version || !importedData.screens) {
                    throw new Error('Invalid WhatsApp Flow JSON structure');
                }

                // Confirm import
                if (confirm('This will replace your current flow. Are you sure you want to import?')) {
                    flowData = importedData;
                    screenCounter = flowData.screens.length;

                    // Update both editors
                    updateJsonFromVisual();
                    refreshCanvas();

                    // Clear properties panel
                    document.getElementById('properties_panel').innerHTML = '<p class="text-muted small">Select a component or screen to edit its properties</p>';

                    showNotification('Flow imported successfully!', 'success');
                }
            } catch (error) {
                showNotification('Invalid JSON file: ' + error.message, 'error');
            }
        };

        reader.readAsText(file);

        // Clear the file input
        event.target.value = '';
    }

    function exportJson() {
        try {
            // Update JSON from visual editor if in visual mode
            const visualEditorRadio = document.getElementById('visual_editor');
            if (visualEditorRadio.checked) {
                updateJsonFromVisual();
            }

            const jsonData = document.getElementById('flow_json').value;
            const parsed = JSON.parse(jsonData); // Validate JSON

            // Create blob and download
            const blob = new Blob([JSON.stringify(parsed, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `whatsapp-flow-${new Date().toISOString().slice(0, 10)}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('Flow exported successfully!', 'success');
        } catch (error) {
            showNotification('Error exporting flow: ' + error.message, 'error');
        }
    }

    // Template definitions
    const flowTemplates = {
        contact_form: {
            version: "5.0",
            screens: [
                {
                    id: "CONTACT_FORM",
                    title: "Contact Form",
                    layout: {
                        type: "SingleColumnLayout",
                        children: [
                            {
                                type: "TextHeading",
                                text: "Contact Us"
                            },
                            {
                                type: "TextBody",
                                text: "Please fill out the form below and we'll get back to you soon."
                            },
                            {
                                type: "TextInput",
                                label: "Full Name",
                                name: "full_name",
                                "input-type": "text",
                                required: true
                            },
                            {
                                type: "TextInput",
                                label: "Email Address",
                                name: "email",
                                "input-type": "email",
                                required: true
                            },
                            {
                                type: "TextInput",
                                label: "Phone Number",
                                name: "phone",
                                "input-type": "phone",
                                required: false
                            },
                            {
                                type: "TextArea",
                                label: "Message",
                                name: "message",
                                required: true
                            },
                            {
                                type: "Footer",
                                label: "Submit",
                                "on-click-action": {
                                    name: "complete",
                                    payload: {}
                                }
                            }
                        ]
                    },
                    terminal: true,
                    success: true,
                    data: []
                }
            ]
        },
        survey: {
            version: "5.0",
            screens: [
                {
                    id: "SURVEY_SCREEN",
                    title: "Customer Survey",
                    layout: {
                        type: "SingleColumnLayout",
                        children: [
                            {
                                type: "TextHeading",
                                text: "Customer Satisfaction Survey"
                            },
                            {
                                type: "TextBody",
                                text: "Help us improve by sharing your feedback."
                            },
                            {
                                type: "RadioButtonsGroup",
                                label: "How satisfied are you with our service?",
                                name: "satisfaction",
                                "data-source": [
                                    { id: "very_satisfied", title: "Very Satisfied" },
                                    { id: "satisfied", title: "Satisfied" },
                                    { id: "neutral", title: "Neutral" },
                                    { id: "dissatisfied", title: "Dissatisfied" },
                                    { id: "very_dissatisfied", title: "Very Dissatisfied" }
                                ]
                            },
                            {
                                type: "CheckboxGroup",
                                label: "What aspects did you like? (Select all that apply)",
                                name: "liked_aspects",
                                "data-source": [
                                    { id: "quality", title: "Quality" },
                                    { id: "speed", title: "Speed" },
                                    { id: "support", title: "Customer Support" },
                                    { id: "pricing", title: "Pricing" }
                                ]
                            },
                            {
                                type: "TextArea",
                                label: "Additional Comments",
                                name: "comments",
                                required: false
                            },
                            {
                                type: "Footer",
                                label: "Submit Survey",
                                "on-click-action": {
                                    name: "complete",
                                    payload: {}
                                }
                            }
                        ]
                    },
                    terminal: true,
                    success: true,
                    data: []
                }
            ]
        },
        appointment: {
            version: "5.0",
            screens: [
                {
                    id: "APPOINTMENT_BOOKING",
                    title: "Book Appointment",
                    layout: {
                        type: "SingleColumnLayout",
                        children: [
                            {
                                type: "TextHeading",
                                text: "Book Your Appointment"
                            },
                            {
                                type: "TextBody",
                                text: "Please provide your details to schedule an appointment."
                            },
                            {
                                type: "TextInput",
                                label: "Full Name",
                                name: "customer_name",
                                "input-type": "text",
                                required: true
                            },
                            {
                                type: "TextInput",
                                label: "Phone Number",
                                name: "phone_number",
                                "input-type": "phone",
                                required: true
                            },
                            {
                                type: "DatePicker",
                                label: "Preferred Date",
                                name: "appointment_date"
                            },
                            {
                                type: "Dropdown",
                                label: "Service Type",
                                name: "service_type",
                                "data-source": [
                                    { id: "consultation", title: "Consultation" },
                                    { id: "treatment", title: "Treatment" },
                                    { id: "followup", title: "Follow-up" },
                                    { id: "other", title: "Other" }
                                ]
                            },
                            {
                                type: "TextArea",
                                label: "Additional Notes",
                                name: "notes",
                                required: false
                            },
                            {
                                type: "Footer",
                                label: "Book Appointment",
                                "on-click-action": {
                                    name: "complete",
                                    payload: {}
                                }
                            }
                        ]
                    },
                    terminal: true,
                    success: true,
                    data: []
                }
            ]
        },
        lead_gen: {
            version: "5.0",
            screens: [
                {
                    id: "LEAD_GENERATION",
                    title: "Lead Generation",
                    layout: {
                        type: "SingleColumnLayout",
                        children: [
                            {
                                type: "TextHeading",
                                text: "Get Your Free Quote"
                            },
                            {
                                type: "TextBody",
                                text: "Tell us about your project and we'll provide a customized quote."
                            },
                            {
                                type: "TextInput",
                                label: "Company Name",
                                name: "company_name",
                                "input-type": "text",
                                required: true
                            },
                            {
                                type: "TextInput",
                                label: "Contact Person",
                                name: "contact_person",
                                "input-type": "text",
                                required: true
                            },
                            {
                                type: "TextInput",
                                label: "Email",
                                name: "email",
                                "input-type": "email",
                                required: true
                            },
                            {
                                type: "Dropdown",
                                label: "Project Type",
                                name: "project_type",
                                "data-source": [
                                    { id: "web_development", title: "Web Development" },
                                    { id: "mobile_app", title: "Mobile App" },
                                    { id: "ecommerce", title: "E-commerce" },
                                    { id: "consulting", title: "Consulting" },
                                    { id: "other", title: "Other" }
                                ]
                            },
                            {
                                type: "RadioButtonsGroup",
                                label: "Budget Range",
                                name: "budget_range",
                                "data-source": [
                                    { id: "under_5k", title: "Under $5,000" },
                                    { id: "5k_15k", title: "$5,000 - $15,000" },
                                    { id: "15k_50k", title: "$15,000 - $50,000" },
                                    { id: "over_50k", title: "Over $50,000" }
                                ]
                            },
                            {
                                type: "TextArea",
                                label: "Project Description",
                                name: "project_description",
                                required: true
                            },
                            {
                                type: "Footer",
                                label: "Get Quote",
                                "on-click-action": {
                                    name: "complete",
                                    payload: {}
                                }
                            }
                        ]
                    },
                    terminal: true,
                    success: true,
                    data: []
                }
            ]
        }
    };

    // Global function for loading templates
    window.loadTemplate = function(templateName) {
        if (!flowTemplates[templateName]) {
            showNotification('Template not found', 'error');
            return;
        }

        if (confirm('This will replace your current flow with the selected template. Continue?')) {
            flowData = JSON.parse(JSON.stringify(flowTemplates[templateName]));
            screenCounter = flowData.screens.length;

            // Update both editors
            updateJsonFromVisual();
            refreshCanvas();

            // Clear properties panel
            document.getElementById('properties_panel').innerHTML = '<p class="text-muted small">Select a component or screen to edit its properties</p>';

            showNotification(`${templateName.replace('_', ' ')} template loaded successfully!`, 'success');
        }
    };

    // Form validation
    form.addEventListener('submit', function(e) {
        submitBtn.disabled = true;

        // Validate name
        if (!nameInput.value.trim()) {
            e.preventDefault();
            nameInput.classList.add('is-invalid');
            submitBtn.disabled = false;
            return;
        }
        nameInput.classList.remove('is-invalid');

        // Validate category
        if (!categorySelect.value) {
            e.preventDefault();
            categorySelect.classList.add('is-invalid');
            submitBtn.disabled = false;
            return;
        }
        categorySelect.classList.remove('is-invalid');

        // Update JSON from visual editor if in visual mode
        const visualEditorRadio = document.getElementById('visual_editor');
        if (visualEditorRadio.checked) {
            updateJsonFromVisual();
        }

        // Validate JSON
        if (!flowJsonTextarea.value.trim()) {
            e.preventDefault();
            flowJsonTextarea.classList.add('is-invalid');
            submitBtn.disabled = false;
            return;
        }

        try {
            const flowJson = JSON.parse(flowJsonTextarea.value);
            if (!flowJson.version || !flowJson.screens) {
                throw new Error('Invalid flow structure');
            }
            flowJsonTextarea.classList.remove('is-invalid');
        } catch (error) {
            e.preventDefault();
            flowJsonTextarea.classList.add('is-invalid');
            showNotification('Invalid JSON format. Please check your flow configuration.', 'error');
            submitBtn.disabled = false;
            return;
        }

        // Allow form submission if validation passes
    });

    // Clear validation on input
    nameInput.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });

    categorySelect.addEventListener('change', function() {
        this.classList.remove('is-invalid');
    });

    flowJsonTextarea.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
});
</script>
@endpush

@push('head')
<style>
    /* Modern Flow Builder - Aisensy Style */
    .flow-builder-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        min-height: 100vh !important;
        padding: 0 !important;
        margin: 0 !important;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    }

    .flow-builder-container .container {
        background: transparent !important;
        max-width: 100% !important;
        padding: 0 !important;
    }

    .flow-builder-container .row {
        margin: 0 !important;
    }

    .flow-builder-container .col-md-12 {
        padding: 0 !important;
    }

    /* Modern Header */
    .flow-builder-header {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        padding: 20px 30px !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 1000 !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    }

    .flow-builder-header h1 {
        color: #1a202c !important;
        font-size: 28px !important;
        font-weight: 700 !important;
        margin: 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
    }

    .flow-builder-header h1::before {
        content: '🔄' !important;
        font-size: 24px !important;
    }

    .flow-builder-header hr {
        display: none !important;
    }

    /* Modern Form Container */
    .flow-builder-form {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        margin: 20px !important;
        border-radius: 20px !important;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        overflow: hidden !important;
    }

    /* Modern Fieldset */
    .flow-builder-container fieldset {
        border: 0 !important;
        padding: 30px !important;
        margin: 0 !important;
        background: transparent !important;
    }

    .flow-builder-container legend {
        font-size: 20px !important;
        font-weight: 700 !important;
        margin-bottom: 20px !important;
        color: #1a202c !important;
        border: none !important;
        width: auto !important;
        padding: 0 !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        position: relative !important;
    }

    .flow-builder-container legend::after {
        content: '' !important;
        position: absolute !important;
        bottom: -8px !important;
        left: 0 !important;
        width: 60px !important;
        height: 3px !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-radius: 2px !important;
    }

    /* Modern Form Controls */
    .flow-builder-container .form-control {
        background: rgba(255, 255, 255, 0.9) !important;
        border: 2px solid rgba(102, 126, 234, 0.1) !important;
        border-radius: 12px !important;
        padding: 14px 18px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #2d3748 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
    }

    .flow-builder-container .form-control:focus {
        background: rgba(255, 255, 255, 1) !important;
        border-color: #667eea !important;
        outline: none !important;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1) !important;
        transform: translateY(-2px) !important;
    }

    .flow-builder-container .form-label {
        font-weight: 600 !important;
        color: #4a5568 !important;
        margin-bottom: 8px !important;
        font-size: 14px !important;
    }

    /* Modern Editor Mode Toggle */
    .flow-builder-container .btn-group {
        background: rgba(255, 255, 255, 0.9) !important;
        border-radius: 12px !important;
        padding: 4px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid rgba(102, 126, 234, 0.1) !important;
    }

    .flow-builder-container .btn-check:checked + .btn {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        color: white !important;
        border: none !important;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
        transform: translateY(-1px) !important;
    }

    .flow-builder-container .btn-outline-primary {
        color: #667eea !important;
        border: none !important;
        background: transparent !important;
        padding: 10px 20px !important;
        border-radius: 8px !important;
        font-weight: 600 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .flow-builder-container .btn-outline-primary:hover {
        background: rgba(102, 126, 234, 0.1) !important;
        color: #667eea !important;
        transform: translateY(-1px) !important;
    }

    /* Toast notification styles */
    #statusNotification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 350px;
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        border-radius: 12px !important;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
    }

    /* Flow Editor Container */
    .flow-builder-container .flow-editor-container {
        min-height: 700px !important;
        background: transparent !important;
    }

    /* Modern Component Library */
    .flow-builder-container .component-library {
        max-height: 600px !important;
        overflow-y: auto !important;
        padding: 8px !important;
    }

    .flow-builder-container .component-library::-webkit-scrollbar {
        width: 6px !important;
    }

    .flow-builder-container .component-library::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05) !important;
        border-radius: 10px !important;
    }

    .flow-builder-container .component-library::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-radius: 10px !important;
    }

    .flow-builder-container .component-category h6 {
        color: #667eea !important;
        font-weight: 700 !important;
        font-size: 11px !important;
        text-transform: uppercase !important;
        letter-spacing: 1px !important;
        margin-bottom: 12px !important;
        margin-top: 20px !important;
    }

    .flow-builder-container .component-category:first-child h6 {
        margin-top: 0 !important;
    }

    .flow-builder-container .component-item {
        padding: 12px 16px !important;
        margin: 6px 0 !important;
        background: rgba(255, 255, 255, 0.9) !important;
        border: 1px solid rgba(102, 126, 234, 0.1) !important;
        border-radius: 10px !important;
        cursor: grab !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        font-size: 13px !important;
        font-weight: 600 !important;
        color: #4a5568 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .flow-builder-container .component-item::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 3px !important;
        height: 100% !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
    }

    .flow-builder-container .component-item:hover {
        background: rgba(255, 255, 255, 1) !important;
        border-color: #667eea !important;
        transform: translateY(-2px) translateX(4px) !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
        color: #667eea !important;
    }

    .flow-builder-container .component-item:hover::before {
        opacity: 1 !important;
    }

    .flow-builder-container .component-item:active {
        cursor: grabbing !important;
        transform: scale(0.98) !important;
    }

    .flow-builder-container .component-item i {
        width: 18px !important;
        margin-right: 12px !important;
        color: #667eea !important;
        font-size: 14px !important;
    }

    /* Modern Flow Canvas */
    .flow-builder-container .flow-canvas {
        min-height: 600px !important;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%) !important;
        border: 2px dashed rgba(102, 126, 234, 0.2) !important;
        border-radius: 16px !important;
        position: relative !important;
        overflow-y: auto !important;
        padding: 24px !important;
        backdrop-filter: blur(10px) !important;
    }

    .flow-builder-container .flow-canvas::-webkit-scrollbar {
        width: 8px !important;
    }

    .flow-builder-container .flow-canvas::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05) !important;
        border-radius: 10px !important;
    }

    .flow-builder-container .flow-canvas::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-radius: 10px !important;
    }

    /* Modern Flow Screen */
    .flow-builder-container .flow-screen {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        border: 2px solid rgba(102, 126, 234, 0.1) !important;
        border-radius: 16px !important;
        margin-bottom: 24px !important;
        min-height: 250px !important;
        position: relative !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        overflow: hidden !important;
    }

    .flow-builder-container .flow-screen::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 4px !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
    }

    .flow-builder-container .flow-screen:hover {
        transform: translateY(-4px) !important;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
    }

    .flow-builder-container .flow-screen:hover::before {
        opacity: 1 !important;
    }

    .flow-builder-container .flow-screen.active {
        border-color: #667eea !important;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 12px 40px rgba(102, 126, 234, 0.2) !important;
        transform: translateY(-4px) !important;
    }

    .flow-builder-container .flow-screen.active::before {
        opacity: 1 !important;
    }

    /* Modern Screen Header */
    .flow-builder-container .screen-header {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05)) !important;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1) !important;
        padding: 16px 20px !important;
        border-radius: 16px 16px 0 0 !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        position: relative !important;
    }

    .flow-builder-container .screen-header::after {
        content: '' !important;
        position: absolute !important;
        bottom: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 2px !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
    }

    .flow-builder-container .screen-title {
        font-weight: 700 !important;
        margin: 0 !important;
        flex-grow: 1 !important;
        color: #1a202c !important;
        font-size: 16px !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .flow-builder-container .screen-title::before {
        content: '📱' !important;
        font-size: 14px !important;
    }

    .flow-builder-container .screen-actions {
        display: flex !important;
        gap: 8px !important;
    }

    .flow-builder-container .screen-actions button {
        padding: 6px 12px !important;
        font-size: 12px !important;
        background: rgba(102, 126, 234, 0.1) !important;
        border: 1px solid rgba(102, 126, 234, 0.2) !important;
        color: #667eea !important;
        border-radius: 8px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        font-weight: 600 !important;
    }

    .flow-builder-container .screen-actions button:hover {
        background: #667eea !important;
        border-color: #667eea !important;
        color: white !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
    }

    .flow-builder-container .screen-content {
        padding: 15px;
        min-height: 150px;
        position: relative;
    }

    /* Modern Drop Zone */
    .flow-builder-container .drop-zone {
        border: 2px dashed rgba(102, 126, 234, 0.3) !important;
        border-radius: 12px !important;
        padding: 24px !important;
        text-align: center !important;
        color: #667eea !important;
        margin: 16px 0 !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        background: rgba(102, 126, 234, 0.02) !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .flow-builder-container .drop-zone::before {
        content: '✨' !important;
        display: block !important;
        font-size: 24px !important;
        margin-bottom: 8px !important;
        opacity: 0.7 !important;
    }

    .flow-builder-container .drop-zone.drag-over {
        border-color: #667eea !important;
        background: rgba(102, 126, 234, 0.1) !important;
        color: #667eea !important;
        transform: scale(1.02) !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2) !important;
    }

    .flow-builder-container .drop-zone.drag-over::before {
        content: '🎯' !important;
        animation: bounce 0.6s ease-in-out infinite alternate !important;
    }

    /* Modern Flow Component */
    .flow-builder-container .flow-component {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        border: 1px solid rgba(102, 126, 234, 0.1) !important;
        border-radius: 12px !important;
        padding: 16px !important;
        margin: 12px 0 !important;
        position: relative !important;
        cursor: pointer !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
        overflow: hidden !important;
    }

    .flow-builder-container .flow-component::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 4px !important;
        height: 100% !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
    }

    .flow-builder-container .flow-component:hover {
        border-color: #667eea !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
        transform: translateY(-2px) !important;
    }

    .flow-builder-container .flow-component:hover::before {
        opacity: 1 !important;
    }

    .flow-builder-container .flow-component.selected {
        border-color: #667eea !important;
        background: rgba(102, 126, 234, 0.05) !important;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(102, 126, 234, 0.2) !important;
        transform: translateY(-2px) !important;
    }

    .flow-builder-container .flow-component.selected::before {
        opacity: 1 !important;
    }

    /* Modern Component Controls */
    .flow-builder-container .component-controls {
        position: absolute !important;
        top: 8px !important;
        right: 8px !important;
        display: none !important;
        gap: 4px !important;
        z-index: 10 !important;
    }

    .flow-builder-container .flow-component:hover .component-controls,
    .flow-builder-container .flow-component.selected .component-controls {
        display: flex !important;
        animation: fadeInUp 0.2s ease-out !important;
    }

    .flow-builder-container .component-controls button {
        width: 28px !important;
        height: 28px !important;
        padding: 0 !important;
        font-size: 12px !important;
        border-radius: 6px !important;
        border: none !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.2s ease !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        backdrop-filter: blur(10px) !important;
    }

    .flow-builder-container .component-controls .edit-component-btn {
        background: rgba(102, 126, 234, 0.9) !important;
        color: white !important;
    }

    .flow-builder-container .component-controls .edit-component-btn:hover {
        background: #667eea !important;
        transform: scale(1.1) !important;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
    }

    .flow-builder-container .component-controls .delete-component-btn {
        background: rgba(239, 68, 68, 0.9) !important;
        color: white !important;
    }

    .flow-builder-container .component-controls .delete-component-btn:hover {
        background: #ef4444 !important;
        transform: scale(1.1) !important;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
    }

    .flow-builder-container .component-preview {
        pointer-events: none;
    }

    .flow-builder-container .component-preview .form-control {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
    }

    /* Modern Properties Panel */
    .flow-builder-container .properties-form .form-group {
        margin-bottom: 20px !important;
    }

    .flow-builder-container .properties-form label {
        font-size: 13px !important;
        font-weight: 600 !important;
        margin-bottom: 8px !important;
        color: #4a5568 !important;
        display: block !important;
    }

    .flow-builder-container .properties-form .form-control,
    .flow-builder-container .properties-form .form-select {
        font-size: 14px !important;
        padding: 12px 16px !important;
        background: rgba(255, 255, 255, 0.9) !important;
        border: 2px solid rgba(102, 126, 234, 0.1) !important;
        border-radius: 8px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
        color: #2d3748 !important;
        font-weight: 500 !important;
    }

    .flow-builder-container .properties-form .form-control:focus,
    .flow-builder-container .properties-form .form-select:focus {
        background: rgba(255, 255, 255, 1) !important;
        border-color: #667eea !important;
        outline: none !important;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        transform: translateY(-1px) !important;
    }

    .flow-builder-container .properties-form .form-check {
        margin-top: 12px !important;
        padding-left: 0 !important;
    }

    .flow-builder-container .properties-form .form-check-input {
        width: 18px !important;
        height: 18px !important;
        margin-right: 8px !important;
        border: 2px solid rgba(102, 126, 234, 0.3) !important;
        border-radius: 4px !important;
        background: rgba(255, 255, 255, 0.9) !important;
        transition: all 0.2s ease !important;
    }

    .flow-builder-container .properties-form .form-check-input:checked {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
    }

    .flow-builder-container .properties-form .form-check-label {
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #4a5568 !important;
        margin-left: 4px !important;
    }

    /* Properties Panel Header */
    .flow-builder-container #properties_panel h6 {
        color: #1a202c !important;
        font-weight: 700 !important;
        font-size: 16px !important;
        margin-bottom: 20px !important;
        padding-bottom: 12px !important;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1) !important;
        position: relative !important;
    }

    .flow-builder-container #properties_panel h6::after {
        content: '' !important;
        position: absolute !important;
        bottom: -2px !important;
        left: 0 !important;
        width: 40px !important;
        height: 2px !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
    }

    /* Properties Panel Badges */
    .flow-builder-container .component-type-badge,
    .flow-builder-container .screen-info-badge {
        margin-bottom: 20px !important;
    }

    .flow-builder-container .component-type-badge .badge,
    .flow-builder-container .screen-info-badge .badge {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        color: white !important;
        padding: 6px 12px !important;
        border-radius: 20px !important;
        font-size: 12px !important;
        font-weight: 600 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
    }

    /* Properties Panel Empty State */
    .flow-builder-container #properties_panel .text-muted {
        text-align: center !important;
        padding: 40px 20px !important;
        color: #a0aec0 !important;
        font-style: italic !important;
        background: rgba(102, 126, 234, 0.02) !important;
        border-radius: 12px !important;
        border: 2px dashed rgba(102, 126, 234, 0.1) !important;
    }

    .flow-builder-container #properties_panel .text-muted::before {
        content: '👆' !important;
        display: block !important;
        font-size: 24px !important;
        margin-bottom: 8px !important;
    }

    /* Form Text Helper */
    .flow-builder-container .form-text {
        font-size: 12px !important;
        color: #718096 !important;
        margin-top: 4px !important;
        font-style: italic !important;
    }

    /* Modern Card Styles */
    .flow-builder-container .card {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(102, 126, 234, 0.1) !important;
        border-radius: 16px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        overflow: hidden !important;
    }

    .flow-builder-container .card:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
    }

    .flow-builder-container .card-header {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05)) !important;
        border-bottom: 1px solid rgba(102, 126, 234, 0.1) !important;
        padding: 20px 24px !important;
        position: relative !important;
    }

    .flow-builder-container .card-header::before {
        content: '' !important;
        position: absolute !important;
        bottom: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 2px !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
    }

    .flow-builder-container .card-header h6 {
        color: #1a202c !important;
        font-weight: 700 !important;
        font-size: 16px !important;
        margin: 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .flow-builder-container .card-header h6::before {
        content: '⚡' !important;
        font-size: 14px !important;
    }

    .flow-builder-container .card-body {
        padding: 24px !important;
        background: rgba(255, 255, 255, 0.95) !important;
    }

    /* Modern Button Styles */
    .flow-builder-container .btn {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 8px !important;
        font-weight: 600 !important;
        line-height: 1.5 !important;
        text-align: center !important;
        text-decoration: none !important;
        vertical-align: middle !important;
        cursor: pointer !important;
        border: 2px solid transparent !important;
        padding: 12px 20px !important;
        font-size: 14px !important;
        border-radius: 12px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .flow-builder-container .btn::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: -100% !important;
        width: 100% !important;
        height: 100% !important;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
        transition: left 0.5s ease !important;
    }

    .flow-builder-container .btn:hover::before {
        left: 100% !important;
    }

    .flow-builder-container .btn-primary {
        color: #fff !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: transparent !important;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
    }

    .flow-builder-container .btn-primary:hover {
        color: #fff !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    }

    .flow-builder-container .btn-outline-primary {
        color: #667eea !important;
        border-color: #667eea !important;
        background: rgba(102, 126, 234, 0.05) !important;
    }

    .flow-builder-container .btn-outline-primary:hover {
        color: #fff !important;
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: transparent !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
    }

    .flow-builder-container .btn-outline-secondary {
        color: #6c757d !important;
        border-color: #e2e8f0 !important;
        background: rgba(108, 117, 125, 0.05) !important;
    }

    .flow-builder-container .btn-outline-secondary:hover {
        color: #fff !important;
        background: #6c757d !important;
        border-color: #6c757d !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3) !important;
    }

    .flow-builder-container .btn-outline-success {
        color: #10b981 !important;
        border-color: #10b981 !important;
        background: rgba(16, 185, 129, 0.05) !important;
    }

    .flow-builder-container .btn-outline-success:hover {
        color: #fff !important;
        background: #10b981 !important;
        border-color: #10b981 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3) !important;
    }

    .flow-builder-container .btn-outline-info {
        color: #0ea5e9 !important;
        border-color: #0ea5e9 !important;
        background: rgba(14, 165, 233, 0.05) !important;
    }

    .flow-builder-container .btn-outline-info:hover {
        color: #fff !important;
        background: #0ea5e9 !important;
        border-color: #0ea5e9 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3) !important;
    }

    .flow-builder-container .btn-outline-danger {
        color: #ef4444 !important;
        border-color: #ef4444 !important;
        background: rgba(239, 68, 68, 0.05) !important;
    }

    .flow-builder-container .btn-outline-danger:hover {
        color: #fff !important;
        background: #ef4444 !important;
        border-color: #ef4444 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3) !important;
    }

    .flow-builder-container .btn-sm {
        padding: 8px 16px !important;
        font-size: 12px !important;
        border-radius: 8px !important;
    }

    .flow-builder-container .btn-group .btn {
        position: relative;
        flex: 1 1 auto;
        margin: 0 !important;
    }

    .flow-builder-container .btn-group .btn:not(:last-child) {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        margin-right: -2px !important;
    }

    .flow-builder-container .btn-group .btn:not(:first-child) {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }

    /* Drag and drop visual feedback */
    .flow-builder-container .dragging {
        opacity: 0.5;
        transform: rotate(5deg);
    }

    .flow-builder-container .drag-placeholder {
        height: 2px;
        background: #007bff;
        margin: 4px 0;
        border-radius: 1px;
        opacity: 0;
        transition: opacity 0.2s;
    }

    .flow-builder-container .drag-placeholder.active {
        opacity: 1;
    }

    /* JSON Editor enhancements */
    .flow-builder-container #flow_json {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
        font-size: 0.875rem !important;
        line-height: 1.4 !important;
        background-color: #ffffff !important;
        border: 1px solid #ced4da !important;
    }

    .flow-builder-container .json-validation-result {
        margin-top: 10px;
        padding: 10px;
        border-radius: 4px;
        font-size: 0.875rem;
    }

    .flow-builder-container .json-validation-result.valid {
        background: #d4edda !important;
        border: 1px solid #c3e6cb !important;
        color: #155724 !important;
    }

    .flow-builder-container .json-validation-result.invalid {
        background: #f8d7da !important;
        border: 1px solid #f5c6cb !important;
        color: #721c24 !important;
    }

    /* Form controls specific to flow builder */
    .flow-builder-container .form-control {
        background-color: #ffffff !important;
        border: 1px solid #ced4da !important;
        color: #495057 !important;
    }

    .flow-builder-container .form-control:focus {
        background-color: #ffffff !important;
        border-color: #80bdff !important;
        outline: 0 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    /* Dropdown specific styles */
    .flow-builder-container .dropdown-menu {
        background-color: #ffffff !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 0.375rem !important;
    }

    .flow-builder-container .dropdown-item {
        color: #212529 !important;
        background-color: transparent !important;
    }

    .flow-builder-container .dropdown-item:hover {
        color: #16181b !important;
        background-color: #f8f9fa !important;
    }

    /* Fix for Bootstrap dropdown toggle */
    .flow-builder-container .dropdown-toggle::after {
        display: inline-block;
        margin-left: 0.255em;
        vertical-align: 0.255em;
        content: "";
        border-top: 0.3em solid;
        border-right: 0.3em solid transparent;
        border-bottom: 0;
        border-left: 0.3em solid transparent;
    }

    /* Fix for button group spacing */
    .flow-builder-container .btn-group .btn:not(:last-child) {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }

    .flow-builder-container .btn-group .btn:not(:first-child) {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        margin-left: -1px;
    }

    /* Fix for radio button styling in editor mode toggle */
    .flow-builder-container .btn-check {
        position: absolute;
        clip: rect(0, 0, 0, 0);
        pointer-events: none;
    }

    .flow-builder-container .btn-check:checked + .btn {
        color: #fff !important;
        background-color: #007bff !important;
        border-color: #007bff !important;
    }

    /* Ensure proper spacing for form elements */
    .flow-builder-container .form-group {
        margin-bottom: 1rem;
    }

    .flow-builder-container .form-label {
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    /* Override table styles that might interfere */
    .flow-builder-container th,
    .flow-builder-container td {
        background-color: transparent !important;
        color: inherit !important;
    }

    /* Override global button styles completely */
    .flow-builder-container .btn-sm.btn-default,
    .flow-builder-container .btn-danger.btn-sm,
    .flow-builder-container .btn-warning.btn-sm,
    .flow-builder-container .btn-light.btn-sm,
    .flow-builder-container .btn-dark.btn-sm {
        background: initial !important;
        color: initial !important;
    }

    /* Ensure proper z-index for dropdowns */
    .flow-builder-container .dropdown-menu {
        z-index: 1050 !important;
    }

    /* Fix for any conflicting margin/padding */
    .flow-builder-container * {
        box-sizing: border-box;
    }

    /* Ensure proper text colors */
    .flow-builder-container h1,
    .flow-builder-container h2,
    .flow-builder-container h3,
    .flow-builder-container h4,
    .flow-builder-container h5,
    .flow-builder-container h6,
    .flow-builder-container p,
    .flow-builder-container span,
    .flow-builder-container div {
        color: inherit;
    }

    /* Reset any global transforms or animations that might interfere */
    .flow-builder-container .card,
    .flow-builder-container .btn {
        transform: none !important;
        animation: none !important;
    }

    /* Ensure proper hover states work */
    .flow-builder-container .card:hover,
    .flow-builder-container .btn:hover {
        transform: none !important;
    }

    /* Modern Animations */
    @keyframes bounce {
        0% { transform: translateY(0); }
        100% { transform: translateY(-4px); }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    /* Apply animations */
    .flow-builder-container .card {
        animation: fadeInUp 0.6s ease-out !important;
    }

    .flow-builder-container .component-item {
        animation: slideInRight 0.4s ease-out !important;
    }

    .flow-builder-container .flow-component {
        animation: fadeInUp 0.5s ease-out !important;
    }

    .flow-builder-container .flow-screen {
        animation: fadeInUp 0.7s ease-out !important;
    }

    /* Stagger animations */
    .flow-builder-container .component-item:nth-child(1) { animation-delay: 0.1s !important; }
    .flow-builder-container .component-item:nth-child(2) { animation-delay: 0.15s !important; }
    .flow-builder-container .component-item:nth-child(3) { animation-delay: 0.2s !important; }
    .flow-builder-container .component-item:nth-child(4) { animation-delay: 0.25s !important; }
    .flow-builder-container .component-item:nth-child(5) { animation-delay: 0.3s !important; }

    /* Modern JSON Editor */
    .flow-builder-container #flow_json {
        font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
        font-size: 13px !important;
        line-height: 1.6 !important;
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        border: 2px solid rgba(102, 126, 234, 0.1) !important;
        border-radius: 12px !important;
        padding: 20px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .flow-builder-container #flow_json:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 12px 40px rgba(0, 0, 0, 0.15) !important;
        transform: translateY(-2px) !important;
    }

    /* Modern Dropdown */
    .flow-builder-container .dropdown-menu {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(102, 126, 234, 0.1) !important;
        border-radius: 12px !important;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
        padding: 8px !important;
        margin-top: 8px !important;
        animation: fadeInUp 0.3s ease-out !important;
    }

    .flow-builder-container .dropdown-item {
        color: #4a5568 !important;
        background: transparent !important;
        border-radius: 8px !important;
        padding: 10px 16px !important;
        margin: 2px 0 !important;
        font-weight: 500 !important;
        font-size: 14px !important;
        transition: all 0.2s ease !important;
    }

    .flow-builder-container .dropdown-item:hover {
        color: #667eea !important;
        background: rgba(102, 126, 234, 0.1) !important;
        transform: translateX(4px) !important;
    }

    /* Modern Submit Button */
    .flow-builder-container #submitBtn {
        background: linear-gradient(135deg, #10b981, #059669) !important;
        border: none !important;
        padding: 14px 28px !important;
        font-size: 16px !important;
        font-weight: 700 !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .flow-builder-container #submitBtn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .flow-builder-container .flow-editor-container .row {
            flex-direction: column !important;
        }

        .flow-builder-container .flow-editor-container .col-md-3,
        .flow-builder-container .flow-editor-container .col-md-6 {
            width: 100% !important;
            margin-bottom: 20px !important;
        }

        .flow-builder-header {
            padding: 15px 20px !important;
        }

        .flow-builder-header h1 {
            font-size: 24px !important;
        }

        .flow-builder-form {
            margin: 10px !important;
            border-radius: 16px !important;
        }

        .flow-builder-container fieldset {
            padding: 20px !important;
        }
    }

    /* Override any global font settings */
    .flow-builder-container {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
    }

    /* Loading States */
    .flow-builder-container .loading {
        opacity: 0.7 !important;
        pointer-events: none !important;
        position: relative !important;
    }

    .flow-builder-container .loading::after {
        content: '' !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        width: 20px !important;
        height: 20px !important;
        margin: -10px 0 0 -10px !important;
        border: 2px solid #667eea !important;
        border-top: 2px solid transparent !important;
        border-radius: 50% !important;
        animation: spin 1s linear infinite !important;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
@endpush