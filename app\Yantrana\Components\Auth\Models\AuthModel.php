<?php

/**
 * Auth.php - Model file
 *
 * This file is part of the Auth component.
 *-----------------------------------------------------------------------------*/

namespace App\Yantrana\Components\Auth\Models;

use App\Yantrana\Base\BaseModel;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Notifications\Notifiable;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\Access\Authorizable;
use App\Yantrana\Components\Vendor\Models\VendorModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Yantrana\Components\Vendor\Models\VendorUserModel;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;

class AuthModel extends BaseModel implements AuthenticatableContract, AuthorizableContract, CanResetPasswordContract
{
    use Authenticatable, Authorizable, CanResetPassword, MustVerifyEmail;
    use HasFactory, Notifiable;

    /**
     * @var string - The database table used by the model.
     */
    protected $table = 'users';

    /**
     * @var array - The attributes that are mass assignable.
     */
    protected $fillable = [
        '_uid',
        'first_name',
        'last_name',
        'email',
        'password',
        'user_roles__id',
        'status',
        'company_id',
        'vendors__id',
        'mobile_number',
        'username',
        'module_permissions'
    ];

    /**
     * @var array - The attributes that should be casted to native types.
     */
    protected $casts = [
        'module_permissions' => 'array',
    ];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = ['password', 'remember_token'];

    protected $appends = [
        'full_name',
    ];

    /**
     * Available modules in the system
     */
    public static $availableModules = [
        'whatsapp_flows' => 'WhatsApp Flows',
        'whatsapp_orders' => 'WhatsApp Orders', 
        'campaigns' => 'Campaigns',
        'templates' => 'Templates',
        'chatbot' => 'Chatbot',
    ];

    /**
     * Get user role
     */
    public function role()
    {
        return $this->belongsTo(AuthRoleModel::class, 'user_roles__id', '_id');
    }

    /**
     * Get user role
     */
    public function vendor()
    {
        return $this->belongsTo(VendorModel::class, 'vendors__id', '_id');
    }

    /**
     * Get user company
     */
    public function company()
    {
        return $this->belongsTo(\App\Models\Company::class, 'company_id', '_id');
    }

    /**
     * Get the vendor user details
     *
     * @return HasOne
     */
    public function vendorUserDetails():HasOne
    {
        return $this->hasOne(VendorUserModel::class, 'users__id', '_id')->where('vendors__id', getVendorId());
    }

    /**
     * prepare and get user full name
     */
    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => (($attributes['first_name'] ?? '') . ' ' . ($attributes['last_name'] ?? '')),
        );
    }

    /**
     * Get modules that this user can assign to others
     *
     * @return array
     */
    public function getAssignableModules()
    {
        // System admin can assign all modules
        if ($this->user_roles__id == 4) { // System Admin role
            return self::$availableModules;
        }
        
        // Super admin can assign only modules they have access to
        if ($this->user_roles__id == 1) { // Super Admin role
            $userModules = $this->module_permissions ?? [];
            $assignableModules = [];
            
            foreach ($userModules as $moduleKey) {
                if (isset(self::$availableModules[$moduleKey])) {
                    $assignableModules[$moduleKey] = self::$availableModules[$moduleKey];
                }
            }
            
            return $assignableModules;
        }
        
        // Regular users cannot assign modules
        return [];
    }

    /**
     * Check if user has access to a specific module
     *
     * @param string $module
     * @return bool
     */
    public function hasModuleAccess($module)
    {
        // System admin has access to all modules
        if ($this->user_roles__id == 4) {
            return true;
        }
        
        // Check if user has the module in their permissions
        $userModules = $this->module_permissions ?? [];
        return in_array($module, $userModules);
    }

}
