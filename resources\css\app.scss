@use "datatables-responsive";
div.dataTables_wrapper {
    .table-striped tbody tr {
        td {
            vertical-align: initial;
        }
        &:nth-of-type(odd) {
            background-color: rgb(255 255 255 / 5%);
        }
    }
    table.dataTable>tbody>tr:hover {
        background-color: #ededed;
    }
    @media (max-width: 1280px) {
        div.dataTables_filter {
            input {
                width: 100%;
            }
            label {
                white-space: unset;
                text-align: center;
            }
        }
    }
}
table.dataTable {
    max-width: 100% !important;
    .btn {
        margin-bottom: 8px;
    }
}
.dataTables_filter {
    min-width: 100%;
}
table.dataTable.dtr-inline.collapsed > tbody > tr.child > {
    td.child,
    th.child {
        padding-left: 0;
        padding: 8px;
        li {
            list-style: none;
            .dtr-title,
            .dtr-data {
                display: block;
                padding: 8px 0;
            }
        }
    }
}
img {
    max-width: 100%;
}
a {
    color: #2aac32;
    &:hover {
        color: #15c420;
    }
}

.dataTables_wrapper .dataTables_length select {
    border: 1px solid #aaa;
    border-radius: 3px;
    padding: 5px;
    background-color: transparent;
    padding: 4px;
    width: auto;
    display: inline-block;
}

// cssware
[data-show-if] {
    display: none;
}

.lds-ring {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}

.lds-ring div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 8px solid #fff;
    border-radius: 50%;
    animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #fff transparent transparent transparent;
}

.lds-ring div:nth-child(1) {
    animation-delay: -0.45s;
}

.lds-ring div:nth-child(2) {
    animation-delay: -0.3s;
}

.lds-ring div:nth-child(3) {
    animation-delay: -0.15s;
}

@keyframes lds-ring {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* width */
::-webkit-scrollbar {
    width: 4px;
}
/* Track */
::-webkit-scrollbar-track {
    box-shadow: inset 0 0 1px grey;
    border-radius: 10px;
}
/* Handle */
::-webkit-scrollbar-thumb {
    background: #dddddd;
    border-radius: 10px;
}
/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #dddddd;
}

html > body {
    min-height: 100vh;
    overflow-x: hidden;
    /* width */
    ::-webkit-scrollbar {
        width: 2px;
    }
}
.navbar > .container {
    padding-right: 4.5rem !important;
}

.navbar-horizontal .navbar-nav .nav-link {
    font-size: initial;
    color: #212529;
    @media (min-width: 992px) {
        i {
            margin-right: 0;
        }
    }
    .text-danger {
        color: #dc3545 !important;
    }
}
.lw-terms-and-conditions-page {
    strong {
        font-weight: 600;
    }
}

.form-input {
    padding: 10px 8px;
    border: 1px solid #c7c7c7;
}

.bg-gradient-primary {
    background: linear-gradient(87deg, #249b4b 0, #cfffd1 100%) !important;
}

.bg-default {
    background-color: #283244 !important;
}

.fill-default {
    fill: #186fdf;
}

label.custom-control-label {
    user-select: none;
    cursor: pointer;
}

// customs
label.error,
label.lw-validation-error,
div.lw-validation-error {
    width: 100%;
    color: #fb6340;
    padding: 10px 0 10px 0;
    .text-left & {
        text-align: left;
    }
}
label.lw-validation-error {
    padding: 10px 0 0 0;
}
.form-group + div.lw-validation-error {
    padding: 0px 0 10px 18px;
}

.btn {
    box-shadow: none;
    border-radius: 4px;
    font-weight: 400;
    &.lw-btn-breakable{
        word-break: break-word;
        white-space: normal;
    }
    &:hover {
        box-shadow: none;
        filter: saturate(1.3);
    }
    &:not(:last-child) {
        margin-right: 0;
    }
    margin-bottom: 4px;
    .input-group & {
        margin-bottom: 0;
        padding: 0.825rem 1.25rem;
    }

    &.btn-primary {
        color: #ffffff;
        border-color: #119242;
        background-color: #2bac32;
        &:not(:disabled):not(.disabled).active,
        &:not(:disabled):not(.disabled):active,
        .show > &.dropdown-toggle {
            background-color: #119242;
        }
    }
    &.btn-primary[data-toggle="modal"] {
        &:not(:disabled):not(.disabled).active,
        &:not(:disabled):not(.disabled):active,
        .show > &.dropdown-toggle {
            color: #0a0a0a;
            border-color: #b3b3b3;
            background-color: #eaedef;
        }
    }
}

.page-item.active .page-link {
    color: #fff;
    border-color: #53b262;
    background-color: #5cb666;
}

.display-2 {
    font-weight: 500;
}

.form-control {
    height: calc(3rem + 2px);
    border: 1px solid #cad1d7;
    font-size: 0.975rem;
    color: #636f8c;
    background-clip: unset;
}
.selectize-input,
.input-group,
.form-control {
    transition: box-shadow 0.15s ease;
    box-shadow: 0 1px 3px rgb(50 50 93 / 15%), 0 1px 0 rgb(0 0 0 / 2%);
    &:focus {
        box-shadow: 0 4px 6px rgb(50 50 93 / 11%), 0 1px 3px rgb(0 0 0 / 8%);
    }
}

.selectize-input.not-full {
    > input,
    > input[type="select-one"] {
        width: 100% !important;
    }
}

.has-danger:after {
    display: none !important;
}

.form-group {
    margin-bottom: 0.5rem;
    margin-top: 0.9rem;
}

label {
    // font-weight: 200;
    // font-size: 0.9rem;
    &.form-control-label {
        font-weight: 400;
    }
}

.dataTables_wrapper {
    .table th {
        font-weight: 500;
    }
    .table td,
    .table th {
        font-size: 1rem;
        .btn-group-sm > .btn,
        .btn-sm {
            font-size: 0.85rem;
        }
    }
    table.dataTable.table thead th,
    table.dataTable.table thead td {
        border-bottom: none;
    }
    table.dataTable.table.no-footer {
        border-bottom: none;
    }
    table.dataTable tbody th,
    table.dataTable tbody td {
        padding: 12px;
        .avatar.avatar-sm.rounded-circle img {
            width: 36px;
            height: 36px;
            object-fit: cover;
        }
    }
    .card .table td,
    .card .table th {
        padding: 1rem 1.5rem;
    }
    select.custom-select {
        padding-right: 20px;
        padding-left: 10px;
    }
    .page-item .page-link,
    .page-item span {
        width: auto;
        height: 36px;
        margin: 0 3px;
        padding: 3px 14px;
        border-radius: 4px !important;
    }
    .form-control {
        height: calc(2rem + 2px);
    }
    .dataTables_paginate .paginate_button {
        padding: 0;
    }
}
.modal {
    transition: none;
}
.modal-backdrop.show {
    opacity: 0.4;
}
.modal .modal-body {
    background-color: #f6f8fa;
}
.modal-open .modal {
    backdrop-filter: blur(4px) grayscale(0.9);
    &.lw-has-form {
        .lw-form .lw-form-modal-body {
            padding: 1.5rem;
        }
        .modal-footer,
        .modal-body {
            border-bottom-left-radius: 0.4375rem;
            border-bottom-right-radius: 0.4375rem;
        }
        .modal-footer {
            background-color: #ffffff;
        }
    }
    .modal-body .modal-header {
        background-color: #ffffff;
        border-radius: 0;
        margin: -23px -24px 16px;
        .modal-title {
            font-size: 1rem;
        }
    }
}

.btn-secondary {
    border-color: #e1e2e2;
}

.lw-form:not([data-show-processing=false]) {
    .lw-form-processing,
    &.lw-form-processing {
        * {
            pointer-events: none;
            user-select: none;
        }
        .lw-form-overlay {
            display: block;
            height: 100%;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background-color: white;
            opacity: 0.2;
            pointer-events: none;
            -webkit-backdrop-filter: blur(2px) grayscale(1);
            backdrop-filter: blur(2px) grayscale(1);
        }
    }
    &.has-danger .form-control {
        &::-webkit-input-placeholder {
            /* Edge */
            color: #c0c7cc;
        }
        &:-ms-input-placeholder {
            /* Internet Explorer 10-11 */
            color: #c0c7cc;
        }
        &::placeholder {
            color: #c0c7cc;
        }
    }
}

.input-group.input-group-alternative {
    border: 1px solid #cad1d7;
    box-shadow: none;
}

.focused .input-group {
    box-shadow: none !important;
}

.swal2-container {
    -webkit-backdrop-filter: blur(2px) grayscale(1);
    backdrop-filter: blur(2px) grayscale(1);
}

.selectize-input {
    padding: 14px 12px;
}

// float selectize as in some cases its opened dropdown going under other element
.selectize-dropdown,
.selectize-dropdown.form-control {
    position: relative;
    top: 0 !important;
}

.lw-form-in-process {
    user-select: none;
    * {
        pointer-events: none;
    }
    .lw-spinner-box {
        position: absolute;
        text-align: center;
        vertical-align: middle;
        align-self: center;
        align-items: center;
        display: flex;
        color: rgb(255, 255, 255);
        z-index: 1;
        border-radius: 8px;
        width: 100px;
        padding: 10px;
        background-color: rgb(106, 119, 132, 0.4);
        & ~ * {
            filter: blur(1px) grayscale(0.5);
            -webkit-filter: blur(1px) grayscale(0.5);
        }
        .spinner-border {
            margin-bottom: 4px;
        }
        small {
            display: block;
        }
    }
}

fieldset {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 16px;
    margin-top: 20px;
    background-color: rgb(255 255 255 / 85%);
    box-shadow: 0 0 1rem 0 rgba(136, 152, 170, 0.15);
    legend {
        font-size: 1.1rem;
        padding: 0 10px;
        width: auto;
        color: #5f72e4;
        border: 1px solid rgba(0, 0, 0, 0.1);
        background-color: #fff;
        padding: 8px 12px;
        border-radius: 8px;
    }
}

#lwTemplateStructureContainer .card,
#lwConversionChatContainer .card,
.lw-template-structure-form fieldset .card {
    box-shadow: none;
}
.card-body {
    fieldset legend {
        border: 1px solid rgba(0, 0, 0, 0.1);
        background-color: #fff;
        padding: 8px 12px;
        border-radius: 8px;
    }
}

/* Chrome, Safari, Edge, Opera */

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input {
    border-color: rgba(50, 151, 211, 0.25);
}

/* Firefox */

input[type="number"] {
    -moz-appearance: textfield;
}

.lw-page-title {
    color: #2bac32;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 400;
}
.navbar-brand-img {
    min-height: 50px;
}
@media (min-width: 768px) {
    .main-content .container-fluid {
        padding-right: 20px !important;
        padding-left: 20px !important;
    }
    .navbar-vertical.navbar-expand-md .navbar-nav .nav-link {
        padding: 0.95rem 1.5rem;
    }
    .navbar-nav .lw-expandable-nav .nav .nav-item .nav-link {
        padding: 0.6rem 1.5rem;
        padding-left: 2.75rem;
    }
    .navbar-vertical.navbar-expand-md .navbar-brand-img {
        // max-height: 120px;
        object-fit: contain;
    }
}

@media (max-width: 768px) {
    .navbar-collapse .collapse-brand img {
        height: 70px;
        object-fit: contain;
        width: auto;
    }
    .card-body {
        padding: 0.5rem;
    }
    .input-group {
        padding: 0 12px;
        padding-bottom: 20px;
        input[type="color"] {
            height: 50px;
        }
        .input-group-text {
            border: none;
            background: transparent;
        }
        .form-control {
            padding: 0.625rem 0.75rem !important;
        }
        * {
            width: 100%;
            display: block;
            border-radius: 4px;
            margin: 8px 0 0 0;
            border-radius: 0.375rem !important;
        }
    }
    .lw-btn-block-mobile,
    .btn.lw-btn-block-mobile {
        width: 100%;
        margin-bottom: 8px;
    }

    .btn-group {
        width: 100%;
        display: block;
        > *:not(.dropdown-menu) {
            width: 100%;
            display: block;
            border-radius: 0 !important;
            &:first-child {
                border-top-left-radius: 0.375rem !important;
                border-top-right-radius: 0.375rem !important;
            }

            &:last-child {
                border-bottom-left-radius: 0.375rem !important;
                border-bottom-right-radius: 0.375rem !important;
            }
        }
    }

    .nav-tabs {
        margin-bottom: 20px;
        .nav-item {
            width: 100%;
            .nav-link {
                border-radius: 0;
            }
            &:first-child .nav-link {
                border-top-left-radius: 0.375rem;
                border-top-right-radius: 0.375rem;
            }

            &:last-child .nav-link {
                border-bottom-left-radius: 0.375rem;
                border-bottom-right-radius: 0.375rem;
            }
        }
    }
}

.navbar-vertical .navbar-nav .nav-link {
    font-size: 1rem;
}

.navbar-light .navbar-nav .nav-link {
    color: #000000;
}

.text-primary {
    color: #5db666 !important;
}

a.text-primary:focus,
a.text-primary:hover {
    color: #139343 !important;
}

.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .show > .nav-link {
    color: rgb(19 147 67);
}

#lwUploadLogo {
    width: 300px;
}

#lwUploadFavicon {
    width: 100px;
}

.lw-disabled-block-content {
    display: block;
    -ms-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    opacity: 0.6;
    pointer-events: none;
    -webkit-filter: blur(2px) grayscale(0.4);
    filter: blur(2px) grayscale(0.4);
    cursor: not-allowed;
}

[x-cloak] {
    display: none !important;
}

.lw-stamp-container {
    height: 150px;
    user-select: none;
    -webkit-user-drag: none;
    -moz-window-dragging: none;
}

.lw-ws-pre-line {
    white-space: pre-line;
}

@media print {
    .btn.lw-whatsapp-btn {
        display: none;
    }
    .card {
        page-break-before: always;
    }
}

.lw-logo-on-order-page {
    min-height: 100px;
    max-height: 200px;
    max-width: 90%;
    object-fit: contain;
}

.lw-bg-blue-gray {
    background-color: #636f7b;
}

.navbar-horizontal .navbar-brand {
    text-align: center;
    img {
        max-height: 90px;
        min-height: 60px;
        max-width: 100%;
        object-fit: contain;
        height: auto;
    }
}

.lw-form-card-box {
    backdrop-filter: blur(4px) grayscale(0.6);
    background-color: rgb(0 0 0 / 70%) !important;
    @media (min-width: 767px) {
        margin-top: 6vh;
    }

    .btn-google {
        color: #fff;
        background-color: #ea4335;
    }

    .btn-facebook {
        color: #fff;
        background-color: #3b5998;
    }
}
.btn-group-lg > .btn,
.btn-lg {
    font-size: 1.2rem;
}

.main-content .navbar-top {
    background-color: #2bac32;
    .lw-guest-page & {
        background-color: #ffffff;
    }
    backdrop-filter: blur(4px) grayscale(0.6);
}
.main-content-has-bg::before {
    position: fixed;
    top: 0;
    left: 0;
    display: block;
    content: "";
    min-width: 100vw;
    min-height: 100vh;
    background-position: top center;
    background-repeat: no-repeat;
    background-size: cover;
    filter: blur(4px) grayscale(0.5);
}

.card.card-stats {
    min-height: 168px;
    .h2 {
        font-size: 2.4em;
    }
    .card-body {
        padding: 1rem 1.5rem;
        border-radius: 8px;
    }
}
.card {
    box-shadow: 4px 4px 4px 1px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.15);
    background-color: rgb(255 255 255 / 80%);
    // background-color: rgb(255 255 255 / 50%);
    backdrop-filter: blur(2px);
}
html > body {
    // efe7dd
    background: #f8f6f3 url("../../imgs/wa-message-bg-faded.png") repeat;
    font-family: "IBM Plex Sans", sans-serif;
}
nav.lw-breadcrumb-container {
    padding: 20px 0 10px 20px;
    display: block;
    background-color: #40474f;
    font-size: 1.2rem;
    border-radius: 4px;
}

.nav-tabs {
    border-bottom: 0;
    margin-left: 20px;
    margin-top: 20px;
    .nav-link {
        padding: 10px 1.75rem;
        font-size: 1.2rem;
        background-color: #dadadad9;
        border: 0 !important;
        color: #8d8d8d;
        &.active {
            background-color: #ffffff;
        }
        &:hover {
            border: 0;
        }
    }
}
fieldset.filepond--file-wrapper {
    background-color: initial;
}
.filepond--file {
    color: white;
    [data-filepond-item-state="processing-error"] & {
        color: white;
    }
    [data-filepond-item-state="processing-complete"] & {
        color: white;
    }
}
.filepond--file-info-sub {
    display: none;
}

.lw-d-none {
    display: none;
}

.btn-size {
    width: 220px !important;
}
@import "app-rtl";

.lw-whatsapp-preview-container {
    background: #e5ddd5;
    padding: 20px;
    max-width: 400px;
    overflow: hidden;
    font-size: 13.6px;
    .lw-whatsapp-preview-bg {
        opacity: 0.2;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: auto;
    }
    position: relative;
}
.lw-whatsapp-header-placeholder {
    border-radius: 6px;
    display: flex;
    align-content: center;
    max-height: 200px;
    background-color: #ccd0d5;
    text-align: center;
    justify-content: center;
    .lw-whatsapp-header-video {
        min-width: 100%;
    }
    .lw-whatsapp-header-image {
        min-width: 80%;
        max-height: 200px;
        object-fit: contain;
    }
}
.lw-whatsapp-preview {
    .lw-whatsapp-header-placeholder,
    a.lw-wa-message-document-link {
        border-radius: 6px;
        display: flex;
        align-content: center;
        height: 150px;
        background-color: #ccd0d5;
        text-align: center;
        justify-content: center;
        width: 100%;
        i.fa {
            align-self: center;
            margin: auto;
        }
    }
    > .card {
        border-top-left-radius: 0;
        padding: 2px;
        // message call-out
        &::after {
            border-width: 0px 10px 10px 0;
            border-color: transparent #fff transparent transparent;
            top: 0px;
            left: -10px;
            position: absolute;
            content: "";
            width: 0;
            height: 0;
            border-style: solid;
        }
    }
    .lw-whatsapp-body {
        padding: 16px;
        > div {
            white-space: pre-line;
        }
    }
    .lw-whatsapp-footer {
        padding: 0 16px 12px 16px;
    }
    .lw-whatsapp-buttons {
        color: #00a5f4;
        padding: 0;
        text-align: center;
    }
}

.lw-configured-badge {
    font-size: 1.6em;
    padding-bottom: 3px;
    display: inline-block;
}

.navbar-horizontal .navbar-nav .nav-link {
    font-weight: 500;
    padding-right: 0.4rem;
}
@media (min-width: 1400px) {
    .container-xxl,
    .container-xl,
    .container-lg,
    .container-md,
    .container-sm,
    .container {
        max-width: 1320px;
    }
}
.lw-error-page-block {
    padding: 10%;
    background: rgb(255 255 255 / 70%);
    border-radius: 50%;
    height: 100%;
    width: 100%;
    display: block;
    margin-top: 20%;
    backdrop-filter: blur(1px);
}

div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm,
.dropdown-item.active,
.dropdown-item:active {
    text-decoration: none;
    color: #fff;
    background-color: #2aac32;
}
div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm:focus {
    box-shadow: none !important;
}
.card .table td,
.card .table th {
    padding-left: 0.7rem;
}
.lw-whatsapp-template-create-preview {
    position: sticky;
    top: 10vh;
}

/* @media (min-width: 768px) {
    .navbar-vertical.navbar-expand-md.fixed-left+.main-content {
        margin-left: 250px;
    }

    .navbar-vertical.navbar-expand-md.fixed-right+.main-content {
        margin-right: 250px
    }
    .navbar-vertical.navbar-expand-md {
        max-width: 250px;
    }
} */
.lw-sidebar-logo-small {
    display: none;
}
.lw-sidebar-container {
    color: white;
}

.lw-minimized-menu {
    @media (min-width: 768px) {
        .navbar-vertical.navbar-expand-md.fixed-left + .main-content {
            margin-left: 60px;
        }
        .navbar-vertical.navbar-expand-md.fixed-right + .main-content {
            margin-right: 60px;
            overflow-x: hidden;
        }
        .navbar-vertical.navbar-expand-md {
            max-width: 250px;
            z-index: 2;
            width: 60px;
            overflow-x: hidden;
            transition: width 0.15s ease;
            > .container-fluid {
                width: 200px;
                .nav .nav-item .nav-link {
                    padding-left: 1.5rem;
                    transition: padding-left 0.15s ease;
                }
                .lw-sidebar-logo-normal {
                    display: none;
                }
                .lw-sidebar-logo-small {
                    display: inline-block;
                    margin-left: -20px;
                }
            }
            &:hover,
            &:focus-within {
                width: 248px;
                .nav .nav-item .nav-link {
                    padding-left: 2.75rem;
                }
                .lw-sidebar-logo-normal {
                    display: inline-block;
                }
                .lw-sidebar-logo-small {
                    display: none;
                }
            }
            .navbar-nav .nav-link.active:before {
                border-left: 2px solid #2aac32;
            }
        }
    }
}
.lw-qr-image {
    height: 160px;
}
.lw-flow-builder-container-holder {
    padding-bottom: 20px;
}
.lw-flow-builder-container {
    // it worn't work properly for panning etc, items get hidden if its not fixed
    width: 3000px;
    height: 3000px;
    border: 2px dotted #e5e7eb;
    background: #fafbfc;
    // margin-right: 20px;
    // margin-bottom: 20px;
    .flowchart-operator {
        min-width: 280px;
        width: auto;
    }
    &:active {
        cursor: grabbing !important;
    }
}
.flowchart-operator-body {
    min-height: 80px;
}

.flowchart-operator-inputs .flowchart-operator-connector {
    padding-top: 0px;
    padding-bottom: 10px;
    display: block;
    &:hover {
        .flowchart-operator-connector-arrow:hover {
            border-left: 10px solid #c91f1f;
        }
    }
}

.lw-business-profile-image {
    height: 150px;
    width: 100%;
    object-fit: contain;
    margin-bottom: 8px;
}
.lw-page-description {
    white-space: pre-line;
    word-wrap: break-word;
}
.lw-page-dropdown{
    background-color: transparent;
    button{
        background-color: white;
        box-shadow: none !important;
        &:hover{
            transform: translateY(0px);
        }
    }
    .dropdown-menu{
        min-width: 10rem;
        box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15) !important;
    }
}
textarea.form-control {
    &::-webkit-scrollbar {
        width: 10px;
    }
}
.pay-box-padding{
    padding: 5rem;
}
.lw-white-space-normal {
    white-space: normal;
}

#paypal-button-container {
    width: 300px;
    max-width: 100%;
    margin: 0 auto;
}