<?php

namespace App\Yantrana\Components\Auth\Requests;

use App\Yantrana\Base\BaseRequest;

class SystemAdminCreateRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return hasSystemAdminAccess();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username',
            'email' => 'required|string|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'first_name.required' => __tr('First name is required'),
            'last_name.required' => __tr('Last name is required'),
            'username.required' => __tr('Username is required'),
            'username.unique' => __tr('Username is already taken'),
            'email.required' => __tr('Email is required'),
            'email.email' => __tr('Please enter a valid email address'),
            'email.unique' => __tr('Email is already registered'),
            'password.required' => __tr('Password is required'),
            'password.min' => __tr('Password must be at least 8 characters'),
            'password.confirmed' => __tr('Password confirmation does not match'),
        ];
    }
}
