@extends('layouts.app', ['title' => __tr('WhatsApp Flows')])

@section('content')
@include('users.partials.header', [
    'title' => __tr(''),
    'description' => '',
    'class' => 'col-lg-7'
])

<div class="container-fluid mt-lg--6">
    <div class="row mt-5">
        <!-- Button -->
        <div class="col-xl-12 mb-3">
            <div class="mt-3 d-flex justify-content-between align-items-center flex-wrap gap-2">
                <!-- Left: Title -->
                <h1 class="page-title mb-0" style="color: #22A755;">
                    <i class="fas fa-sitemap gradient-icon-10"></i> {{ __tr('WhatsApp Flows') }}
                </h1>

                <!-- Right: Action Buttons -->
                <div class="d-flex gap-2 flex-wrap">
                    <a class="lw-btn btn"
                        style="
                            background-color: #0B7753;
                            color: #ffffff;
                            border: none;
                            border-radius: 8px;
                            padding: 8px 16px;
                            font-weight: 500;
                            margin-right: 8px;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                            display: inline-flex;
                            align-items: center;
                            gap: 6px;
                        "
                        href="{{ route('whatsapp-flows.create') }}"
                        onmouseover="this.style.backgroundColor='#095c41'; this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 12px rgba(11, 119, 83, 0.3)'"
                        onmouseout="this.style.backgroundColor='#0B7753'; this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)'">
                        <i class="fa fa-plus-circle"></i> {{ __tr('Create New Flow') }}
                    </a>


                    <button id="refresh-btn"
                        class="lw-btn btn"
                        style="
                            background-color: #122C58;
                            color: #ffffff;
                            border: none;
                            border-radius: 8px;
                            padding: 8px 16px;
                            font-weight: 500;
                            margin-right: 8px;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                            display: inline-flex;
                            align-items: center;
                            gap: 6px;
                        "
                        onmouseover="this.style.backgroundColor='#095c41'; this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 12px rgba(11, 119, 83, 0.3)'"
                        onmouseout="this.style.backgroundColor='#122C58'; this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)'">
                        <i class="fas fa-redo-alt"></i> {{ __tr('Refresh') }}
                    </button>


                    <a class="lw-btn btn"
                        target="_blank"
                        href="https://business.facebook.com/wa/manage/flows?waba_id={{ getVendorSettings('whatsapp_business_account_id') }}"
                        style="
                            background-color: #0861F2;
                            color: #333;
                            border: none;
                            border-radius: 10px;
                            padding: 8px 16px;
                            font-weight: 500;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                            display: inline-flex;
                            align-items: center;
                            gap: 6px;
                        "
                        onmouseover="this.style.backgroundColor='#0B7753'; this.style.color='#fff'; this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 12px rgba(11, 119, 83, 0.2)'"
                        onmouseout="this.style.backgroundColor='#0861F2'; this.style.color='#0861F2'; this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.05)'">
                        {{ __tr('Manage Flows on Meta') }} <i class="fas fa-external-link-alt"></i>
                    </a>

                </div>
            </div>
        </div>

        <!-- /Button -->

        @if (session('status'))
            <div class="col-xl-12">
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            </div>
        @endif

        @if (session('error'))
            <div class="col-xl-12">
                <div class="alert alert-danger" role="alert">
                    {{ session('error') }}
                </div>
            </div>
        @endif

        @if (session('success'))
            <div class="col-xl-12">
                <div class="alert alert-success" role="alert">
                    {{ session('success') }}
                </div>
            </div>
        @endif

        <div class="col-xl-12">
            <div class="card">
                <div class="card-header bg-transparent">
                    <h3 class="mb-0">{{ __tr('WhatsApp Flows List') }}</h3>
                </div>
                <div class="card-body">
                    <div id="loading" class="text-center py-4 d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">{{ __tr('Fetching flows...') }}</p>
                    </div>

                    <div id="flows-container">
                        @if(empty($flows))
                            <div class="alert alert-info">
                                {{ __tr('No WhatsApp flows found. Try refreshing or check your API connection.') }}
                            </div>
                        @else
                            <div class="table-responsive">
                                <table id="flowsTable" class="table table-flush">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>{{ __tr('ID') }}</th>
                                            <th>{{ __tr('Name') }}</th>
                                            <th>{{ __tr('Status') }}</th>
                                            <th>{{ __tr('Categories') }}</th>
                                            <th>{{ __tr('Actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($flows as $flow)
                                            <tr class="{{ $flow['status'] === 'PUBLISHED' ? 'table-success' : '' }}" data-flow-id="{{ $flow['id'] }}">
                                                <td>{{ $flow['id'] }}</td>
                                                <td>{{ $flow['name'] }}</td>
                                                <td>
                                                    @if($flow['status'] === 'PUBLISHED')
                                                        <span class="badge badge-success p-2">
                                                            <i class="fa fa-check-circle"></i> {{ $flow['status'] }}
                                                        </span>
                                                    @elseif($flow['status'] === 'DRAFT')
                                                        <span class="badge badge-warning p-2">
                                                            <i class="fa fa-clock"></i> {{ $flow['status'] }}
                                                        </span>
                                                    @else
                                                        <span class="badge badge-info p-2">
                                                            {{ $flow['status'] }}
                                                        </span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @foreach($flow['categories'] as $category)
                                                        <span class="badge badge-info mr-1">{{ $category }}</span>
                                                    @endforeach
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        @if($flow['status'] === 'DRAFT')
                                                            <!-- Edit Button -->
                                                            <a href="{{ route('whatsapp-flows.edit', ['id' => $flow['id']]) }}" 
                                                            class="btn"
                                                            style="
                                                                background-color: #f8f9fa;
                                                                color: #333;
                                                                border: 1px solid #ced4da;
                                                                border-radius: 8px;
                                                                padding: 6px 12px;
                                                                margin-right: 6px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                                                                display: inline-flex;
                                                                align-items: center;
                                                                gap: 6px;
                                                            "
                                                            onmouseover="this.style.backgroundColor='#e2e6ea'; this.style.transform='scale(1.05)'"
                                                            onmouseout="this.style.backgroundColor='#f8f9fa'; this.style.transform='scale(1)'"
                                                            title="{{ __tr('Edit') }}">
                                                                <i class="fa fa-edit" style="font-size: 12px;"></i>{{ __tr('Edit') }}                        
                                                            </a>

                                                            <!-- Delete Button -->
                                                            <form action="{{ route('whatsapp-flows.delete', ['id' => $flow['id']]) }}" 
                                                                method="POST" 
                                                                style="display: inline;">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit"
                                                                        class="btn"
                                                                        style="
                                                                            background-color: #fff;
                                                                            color: #dc3545;
                                                                            border: 1px solid #dc3545;
                                                                            border-radius: 8px;
                                                                            padding: 12px 12px;
                                                                            margin-right: 6px;
                                                                            transition: all 0.3s ease;
                                                                            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                                                                            display: inline-flex;
                                                                            align-items: center;
                                                                            gap: 6px;
                                                                        "
                                                                        onmouseover="this.style.backgroundColor='#dc3545'; this.style.color='#fff'; this.style.transform='scale(1.05)'"
                                                                        onmouseout="this.style.backgroundColor='#fff'; this.style.color='#dc3545'; this.style.transform='scale(1)'"
                                                                        data-flow-id="{{ $flow['id'] }}"
                                                                        data-flow-name="{{ $flow['name'] }}"
                                                                        title="{{ __tr('Delete') }}">
                                                                    <i class="fa fa-trash"></i> {{ __tr('') }}
                                                                </button>
                                                            </form>
                                                        @endif

                                                        @if($flow['status'] !== 'DEPRECATED')
                                                            <!-- Send Button -->
                                                            <a href="{{ route('whatsapp-flows.send', ['id' => $flow['id']]) }}"
                                                                class="btn"
                                                                style="
                                                                    background-color: #0B7753;
                                                                    color: #ffffff;
                                                                    border: none;
                                                                    border-radius: 12px;
                                                                    padding: 10px 18px;
                                                                    font-weight: 500;
                                                                    font-size: 14px;
                                                                    margin-right: 8px;
                                                                    transition: all 0.3s ease;
                                                                    box-shadow: 0 4px 8px rgba(11, 119, 83, 0.25);
                                                                    display: inline-flex;
                                                                    align-items: center;
                                                                    gap: 8px;
                                                                "
                                                                onmouseover="
                                                                    this.style.backgroundColor='#095c41';
                                                                    this.style.transform='scale(1.06)';
                                                                    this.style.boxShadow='0 6px 12px rgba(11, 119, 83, 0.35)';
                                                                "
                                                                onmouseout="
                                                                    this.style.backgroundColor='#0B7753';
                                                                    this.style.transform='scale(1)';
                                                                    this.style.boxShadow='0 4px 8px rgba(11, 119, 83, 0.25)';
                                                                "
                                                                title="{{ __tr('Send') }}">
                                                                <span style="color: #fff;">
                                                                    <i class="fa fa-paper-plane" style="font-size: 14px;"></i> {{ __tr('Send') }}
                                                                </span>
                                                            </a>

                                                        @endif

                                                        <!-- Preview Button -->
                                                        <a href="{{ route('whatsapp-flows.preview', ['id' => $flow['id']]) }}" 
                                                        class="btn"
                                                        style="
                                                            background-color: #e9ecef;
                                                            color: #333;
                                                            border: 1px solid #ced4da;
                                                            border-radius: 8px;
                                                            padding: 6px 12px;
                                                            transition: all 0.3s ease;
                                                            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                                                            display: inline-flex;
                                                            align-items: center;
                                                            gap: 6px;
                                                        "
                                                        onmouseover="this.style.backgroundColor='#dee2e6'; this.style.transform='scale(1.05)'"
                                                        onmouseout="this.style.backgroundColor='#e9ecef'; this.style.transform='scale(1)'"
                                                        title="{{ __tr('Preview') }}">
                                                            <i class="fa fa-eye"></i> {{ __tr('Preview') }}
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Flow Template -->
        <script type="text/template" id="lwDeleteFlow-template">
            <h2>{{ __tr('Are You Sure!') }}</h2>
            <p>{{ __tr('You want to delete this WhatsApp Flow?') }}</p>
        </script>
        <!-- /Delete Flow Template -->
    </div>
</div>

<style>
    th {
        background-color: rgb(11, 119, 83) !important;
        color: white;
    }
    
    .badge {
        font-size: 85%;
    }
    
    .btn-group .btn {
        margin-right: 2px;
    }

    .table-flush th, .table-flush td {
        padding: 12px 15px;
        vertical-align: middle;
    }

    .thead-light th {
        font-weight: 600;
    }

    .card-header {
        border-bottom: 1px solid #e9ecef;
        padding: 1.25rem 1.5rem;
    }
    /* Table container */
.table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Table styling */
.table-flush {
    background-color: #ffffff;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

/* Header */
.table-flush thead th {
    background-color: #0B7753 !important;
    color: #ffffff !important;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 14px 16px;
    border: none;
}

/* Table rows */
.table-flush tbody tr {
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #dee2e6;
}

.table-flush tbody tr:hover {
    background-color: #f4fdf7;
}

/* Zebra striping */
.table-flush tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* Table cells */
.table-flush td {
    padding: 14px 16px;
    vertical-align: middle;
    font-size: 14px;
    color: #333;
    border-top: none;
}

/* Badges */
.badge {
    font-size: 85%;
    padding: 6px 12px;
    border-radius: 6px;
}

/* Success row highlight */
tr.table-success td {
    background-color: #e8f8ef !important;
}

/* Action buttons group */
.btn-group .btn {
    margin-right: 6px;
}

/* Buttons in table */
.table .btn {
    transition: all 0.3s ease;
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 6px;
}

</style>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const refreshBtn = document.getElementById('refresh-btn');
        const loadingElement = document.getElementById('loading');
        const flowsContainer = document.getElementById('flows-container');

        // Initialize delete buttons
        document.querySelectorAll('.delete-flow-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get the form element
                const form = this.closest('form');
                
                // Get flow details from data attributes
                const flowId = this.getAttribute('data-flow-id');
                const flowName = this.getAttribute('data-flow-name');
                
                // Show confirmation
                console.log('Showing confirmation dialog for flow:', flowName);
                if (confirm('{{ __tr("Are you sure you want to delete flow") }} "' + flowName + '"? {{ __tr("This action cannot be undone.") }}')) {
                    console.log('User confirmed deletion of flow:', flowName);
                    // Submit the form directly
                    form.submit();
                } else {
                    console.log('User cancelled deletion of flow:', flowName);
                }
            });
        });

        // Refresh Functionality
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                loadingElement.classList.remove('d-none');
                flowsContainer.classList.add('d-none');
                window.location.reload();
            });
        }

        // Initialize DataTable if it exists
        if (typeof $.fn.DataTable !== 'undefined') {
            $('#flowsTable').DataTable({
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "language": {
                    "paginate": {
                        "next": '<i class="fa fa-angle-right"></i>',
                        "previous": '<i class="fa fa-angle-left"></i>'
                    }
                }
            });
        }
    });
</script>
@endsection