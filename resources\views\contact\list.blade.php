@php
/**
* Component : Contact
* Controller : ContactController
* File : contact.list.blade.php
* ----------------------------------------------------------------------------- */
$currentGroup = $groupUid ? $vendorContactGroups->where('_uid', $groupUid)->first() : null;
@endphp
@extends('layouts.app', ['title' => __tr('Contacts')])
@section('content')
@include('users.partials.header', [
'title' => $groupUid ? __tr('__groupName__ group contacts', [
'__groupName__' => $currentGroup->title
]) : __tr(''),
// 'description' => $groupUid ? $currentGroup->description : '',
'class' => 'col-lg-7'
])
@php
$groupDescription = $groupUid ? $currentGroup->description : '';
@endphp
<div class="container-fluid mt-lg--6">
    <div class="row">
        <!-- button -->
        <div class="col-xl-12 mt-3">
            <div class="mt-5 d-flex justify-content-between align-items-center flex-wrap">
                @if ($groupUid)
                    <a class="lw-btn btn btn-secondary mb-2"
                        href="{{ route('vendor.contact.group.read.list_view') }}"
                        style="
                            border-radius: 10px;
                            padding: 8px 16px;
                            font-weight: 500;
                            background-color: #0B7753;
                            color: #ffffff;
                            border: 2px solid #0B7753;
                            transition: all 0.3s ease;
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                        "
                        onmouseover="this.style.backgroundColor='#095c41'; this.style.transform='scale(1.03)'"
                        onmouseout="this.style.backgroundColor='#0B7753'; this.style.transform='scale(1)'">
                            <i class="fas fa-arrow-circle-left me-1"></i> {{ __tr('Back to Contact Groups') }}
                        </a>
                @endif

                <h1 class="page-title mb-0" style="color: #22A755;">
                    <i class="fas fa-user" title="Email"></i> {{ __tr('Contacts') }}
                </h1>

                <div class="btn-group mb-2">
                    <button 
                        type="button" 
                        class="lw-btn btn me-2" 
                        data-toggle="modal" 
                        data-target="#lwAddNewContact"
                        style="background-color: #1e7a3d; color: #fff; border: 1px solid #1e7a3d; transition: background-color 0.3s ease; border-radius: 10px; margin-right: 8px;"
                        onmouseover="this.style.backgroundColor='#16632f'"
                        onmouseout="this.style.backgroundColor='#1e7a3d'"
                    >
                        <i class="fas fa-plus-circle me-2"></i>{{ __tr(' Create New Contact') }}
                    </button>

                    <button 
                        type="button" 
                        class="lw-btn btn me-2" 
                        data-toggle="modal" 
                        data-target="#lwExportDialog"
                        style="background-color: #2F1C6B; color: #fff; border: 1px solid #1e7a3d; transition: background-color 0.3s ease; border-radius: 10px; margin-right: 8px;"
                        onmouseover="this.style.backgroundColor='#16632f'"
                        onmouseout="this.style.backgroundColor='#2F1C6B'"
                    >
                        <i class="fas fa-file-export"></i>{{ __tr(' Export Contacts') }}
                    </button>

                    <button
                        type="button"
                        class="lw-btn btn"
                        data-toggle="modal"
                        data-target="#lwImportContactDialog"
                        style="background-color: #673EE5; color: #fff; border: 1px solid #1e7a3d; transition: background-color 0.3s ease; border-radius: 10px; margin-right: 8px;"
                        onmouseover="this.style.backgroundColor='#16632f'"
                        onmouseout="this.style.backgroundColor='#673EE5'"
                    >
                        <i class="fas fa-file-import"></i> {{ __tr(' Import Contacts') }}
                    </button>

                    <button
                        type="button"
                        class="lw-btn btn me-2"
                        id="lwFetchExternalContacts"
                        style="background-color: #FF6B35; color: #fff; border: 1px solid #FF6B35; transition: background-color 0.3s ease; border-radius: 10px; margin-left: 8px;"
                        onmouseover="this.style.backgroundColor='#E55A2B'"
                        onmouseout="this.style.backgroundColor='#FF6B35'"
                    >
                        <i class="fas fa-cloud-download-alt"></i> {{ __tr(' Fetch External Contacts') }}
                    <button 
                        type="button" 
                        class="lw-btn btn btn-danger delete-all-contacts" 
                        style="background-color: #dc3545; color: #fff; border: 1px solid #dc3545; transition: background-color 0.3s ease; border-radius: 10px;"
                        onmouseover="this.style.backgroundColor='#c82333'"
                        onmouseout="this.style.backgroundColor='#dc3545'"
                    >
                        <i class="fas fa-trash-alt"></i> {{ __tr(' Delete All') }}
                    </button>
                </div>


            </div>
        </div>

        <!--/ button -->
        {{-- import contacts --}}
        <x-lw.modal id="lwImportContactDialog" :header="__tr('Import Contacts')" :hasForm="true"
            data-pre-callback="appFuncs.clearContainer">
            <x-lw.form id="lwImportContactDialogForm" :action="route('vendor.contact.write.import')"
                :data-callback-params="['modalId' => '#lwImportContactDialog', 'datatableId' => '#lwContactList']"
                data-callback="appFuncs.modelSuccessCallback">
                <div class="lw-form-modal-body">
                    <div class="alert alert-danger">
                        {{ __tr('Please use Template from Export contacts') }}
                    </div>
                    <p>{{ __tr('You can import excel file with new contacts or existing updated.') }}</p>
                    <div class="alert alert-light">
                        <h3>{{ __tr('Conventions') }}</h3>
                        <h4>{{ __tr('Mobile Number') }}</h4>
                        {{ __tr('Mobile number treated as unique entity, it should be with country code without prefixing
                        0 or +, if the Mobile number is found in the records other information for the same will get
                        updated with data from the excel.') }}
                        <div class="mt-3">
                            <h4>{{ __tr('Group') }}</h4>
                            {{ __tr('Use comma separated group title, make sure groups are already exists into the
                            system. Groups won\'t be deleted, only new groups will be assigned.') }}
                        </div>
                    </div>
                    <div class="form-group ">
                        <input id="lwImportDocumentFilepond" type="file" data-allow-revert="true"
                            data-label-idle="{{ __tr('Select XLSX File') }}" class="lw-file-uploader"
                            data-instant-upload="true"
                            data-action="<?= route('media.upload_temp_media', 'vendor_contact_import') ?>"
                            data-file-input-element="#lwImportDocument" data-allowed-media='{{ getMediaRestriction('
                            vendor_contact_import') }}'>
                        <input id="lwImportDocument" type="hidden" value="" name="document_name" />
                    </div>
                </div>
                <!-- form footer -->
                <div class="modal-footer">
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary">{{ __('Process Import') }}</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
                </div>
            </x-lw.form>
        </x-lw.modal>
        {{-- /import contacts --}}
        {{-- export contacts --}}
        <x-lw.modal id="lwExportDialog" :header="__tr('Export Contacts')" :hasForm="true"
            data-pre-callback="appFuncs.clearContainer">
            <div class="lw-form-modal-body p-3">
                <h5>{{ __tr('Export with Data') }}</h5>
                <p>{{ __tr('You can export all contacts excel file and import it back with updated data.') }}</p>
                <a href="{{ route('vendor.contact.write.export', [
                    'exportType' => 'data'
                ]) }}" data-method="post" class="btn btn-primary">{{ __('Export Excel File with Data') }}</a>
                <hr>
                <h5>{{ __tr('Blank Excel Template') }}</h5>
                <p>{{ __tr('You can export blank excel file and fill with data according to column header and import it
                    for updates.') }}</p>
                <a href="{{ route('vendor.contact.write.export') }}" data-method="post" class="btn btn-primary">{{
                    __('Export Blank Template') }}</a>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
            </div>
        </x-lw.modal>
        {{-- /export contacts --}}
        <!-- Add New Contact Modal -->
        <x-lw.modal id="lwAddNewContact" :header="__tr('Add New Contact')" :hasForm="true"
            data-pre-callback="appFuncs.clearContainer">
            <!--  Add New Contact Form -->
            <x-lw.form id="lwAddNewContactForm" :action="route('vendor.contact.write.create')"
                :data-callback-params="['modalId' => '#lwAddNewContact', 'datatableId' => '#lwContactList']"
                data-callback="appFuncs.modelSuccessCallback">
                <!-- form body -->
                <div class="lw-form-modal-body">
                    <!-- form fields form fields -->
                    <!-- First_Name -->
                    <x-lw.input-field type="text" id="lwFirstNameField" data-form-group-class=""
                        :label="__tr('First Name')" name="first_name"  />
                    <!-- /First_Name -->
                    <!-- Last_Name -->
                    <x-lw.input-field type="text" id="lwLastNameField" data-form-group-class=""
                        :label="__tr('Last Name')" name="last_name"  />
                    <!-- /Last_Name -->
                    <!-- Country -->
                    <x-lw.input-field type="selectize" data-lw-plugin="lwSelectize" id="lwCountryField"
                        data-form-group-class="" data-selected=" " :label="__tr('Country')" name="country"
                        >
                        <x-slot name="selectOptions">
                            <option value="">{{ __tr('Country') }}</option>
                            @foreach(getCountryPhoneCodes() as $getCountryCode)
                            <option value="{{ $getCountryCode['_id'] }}">{{ $getCountryCode['name'] }}</option>
                            @endforeach
                        </x-slot>
                    </x-lw.input-field>
                    <!-- /Country -->
                    <!-- Phone_Number -->
                    <x-lw.input-field type="number" id="lwPhoneNumberField" data-form-group-class=""
                        :label="__tr('Mobile Number')" name="phone_number" minlength="9"
                        :helpText="__tr('Number should be with country code without 0 or +')" required="true" />
                    <!-- /Phone_Number -->
                    <!-- Language Code -->
                    <x-lw.input-field type="text" id="lwLanguageCodeField" data-form-group-class=""
                        :label="__tr('Language Code')" name="language_code" />
                    <!-- /Language Code -->
                    <!-- Email -->
                    <x-lw.input-field type="email" id="lwEmailField" data-form-group-class="" :label="__tr('Email')"
                        name="email" />
                    <!-- /Email -->
                    <x-lw.input-field type="selectize" data-lw-plugin="lwSelectize" id="lwSelectGroupsField"
                        data-form-group-class="" data-selected=" " :label="__tr('Groups')" name="contact_groups[]"
                        multiple>
                        <x-slot name="selectOptions">
                            <option value="">{{ __tr('Select Groups') }}</option>
                            @foreach($vendorContactGroups as $vendorContactGroup)
                            <option value="{{ $vendorContactGroup['_id'] }}">{{ $vendorContactGroup['title'] }} {{ $vendorContactGroup['status'] == 5  ? __tr('(Archived)') : '' }}</option>
                            @endforeach
                        </x-slot>
                    </x-lw.input-field>
                    <div class="my-3">
                        <x-lw.checkbox id="lwPromotionalOpt" name="whatsapp_opt_out" data-color="#ff0000" data-size="small" value="1" data-lw-plugin="lwSwitchery" :label="__tr('Opt out Marketing Messages')" />
                    </div>
                    <div class="my-3">
                        @if(isAiBotAvailable())
                        <x-lw.checkbox id="lwAiBotEnable" :checked="getVendorSettings('default_enable_flowise_ai_bot_for_users')" name="enable_ai_bot" value="1" data-size="small" 
                            data-lw-plugin="lwSwitchery" :label="__tr('Enable AI Bot')" />
                        @endif
                    </div>
                    <fieldset>
                        <legend>{{ __tr('Other Information') }}</legend>
                        @foreach ($vendorContactCustomFields as $vendorContactCustomField)
                        <x-lw.input-field type="{{ $vendorContactCustomField->input_type }}"
                            id="lwCustomField{{ $vendorContactCustomField->_id }}" data-form-group-class=""
                            :label="$vendorContactCustomField->input_name"
                            name="custom_input_fields[{{ $vendorContactCustomField->_uid }}]" />
                        @endforeach
                    </fieldset>
                </div>
                <!-- form footer -->
                <div class="modal-footer">
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary">{{ __('Submit') }}</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
                </div>
            </x-lw.form>
            <!--/  Add New Contact Form -->
        </x-lw.modal>
        <!--/ Add New Contact Modal -->

        <!-- Details Contact Modal -->
        <x-lw.modal id="lwDetailsContact" :header="__tr('Contact Details')">
            <!--  Details Contact Form -->
            <!-- Details body -->
            <div id="lwDetailsContactBody" class="lw-form-modal-body"></div>
            <script type="text/template" id="lwDetailsContactBody-template">
                <!-- form fields -->
                <div>
                    <label class="small">{{ __tr('First Name') }}:</label>
                    <div class="lw-details-item">
                        <%- __tData.first_name %>
                    </div>
                </div>

                <div>
                    <label class="small">{{ __tr('Last Name') }}:</label>
                    <div class="lw-details-item">
                        <%- __tData.last_name %>
                    </div>
                </div>

                <div>
                    <label class="small">{{ __tr('Country') }}:</label>
                    <div class="lw-details-item">
                        <%- __tData.country?.name %>
                    </div>
                </div>

                <div>
                    <label class="small">{{ __tr('Mobile Number') }}:</label>
                    <div class="lw-details-item">
                        <%- __tData.wa_id %>
                    </div>
                </div>
                <div>
                    <label class="small">{{ __tr('Language Code') }}:</label>
                    <div class="lw-details-item">
                        <%- __tData.language_code %>
                    </div>
                </div>

                <div>
                    <label class="small">{{ __tr('Email') }}:</label>
                    <div class="lw-details-item">
                        <%- __tData.email %>
                    </div>
                </div>

                <fieldset>
                    <legend>{{ __tr('Groups') }}</legend>
                    <% _.forEach(__tData.groups, function(value, key) { %>
                        <span class="badge badge-light">
                            <%- value.title %>
                        </span>
                        <% } ); %>
                </fieldset>
                <fieldset>
                    <legend>{{ __tr('Other Information') }}</legend>
                    @foreach ($vendorContactCustomFields as $vendorContactCustomField)
                    <div class="mb-2">
                        <label class="small">{{ $vendorContactCustomField->input_name }}:</label>
                        <div class="lw-details-item">
                            <%- _.get(_.find(__tData.custom_field_values, {'contact_custom_fields__id' : {{
                                $vendorContactCustomField->_id }} }), 'field_value') %>
                        </div>
                    </div>
                    @endforeach
                </fieldset>
            </script>
            <!--/  Details Contact Form -->
        </x-lw.modal>
        <!--/ Edit Contact Modal -->
         <!--Group description -->
        <div class="ml-3">
            <p class="card-text">{{$groupDescription
            }}</p>
        </div>
         <!--/ Group description -->
        <!-- Edit Contact Modal -->
        @include('contact.contact-edit-modal-partial')
        <!--/ Edit Contact Modal -->
        <div class="col-xl-12" x-cloak x-data="{isSelectedAll:false,selectedContacts: [],selectedGroupsForSelectedContacts:[],
            toggle(id) {
                if (this.selectedContacts.includes(id)) {
                    const index = this.selectedContacts.indexOf(id);
                    this.selectedContacts.splice(index, 1);
                    this.isSelectedAll = false;
                } else {
                    this.selectedContacts.push(id);
                    if($('.dataTables_wrapper table>tbody input[type=checkbox].lw-checkboxes').length == this.selectedContacts.length) {
                        this.isSelectedAll = true;
                    }
                };
            },toggleAll() {
                if(!this.isSelectedAll) {
                    $('.dataTables_wrapper table>tbody input[type=checkbox].lw-checkboxes').not(':checked').trigger('click');
                    this.isSelectedAll = true;
                } else {
                    $('.dataTables_wrapper table>tbody input[type=checkbox].lw-checkboxes:checked').trigger('click');
                    this.isSelectedAll = false;
                }
            },deleteSelectedContacts() {
                var that = this;
                showConfirmation('{{ __tr('Are you sure you want to delete all selected contacts?') }}', function() {
                    __DataRequest.post('{{ route('vendor.contacts.selected.write.delete') }}', {
                        'selected_contacts' : that.selectedContacts
                    });
                }, {
                    confirmButtonText: '{{ __tr('Yes') }}',
                    cancelButtonText: '{{ __tr('No') }}',
                    type: 'error'
                });
            }, assignGroupsToSelectedContacts(){
                var that = this;
                __DataRequest.post('{{ route('vendor.contacts.selected.write.assign_groups') }}', {
                    'selected_contacts' : that.selectedContacts,
                    'selected_groups' : that.selectedGroupsForSelectedContacts
                });
                $('#lwAssignGroups').modal('hide');
                $('.dataTables_wrapper table>tbody input[type=checkbox].lw-checkboxes:checked').trigger('click');
                this.isSelectedAll = false;
            }}" x-init="$('#lwContactList').on( 'draw.dt', function () {
                $('.dataTables_wrapper table>tbody input[type=checkbox].lw-checkboxes:checked').trigger('click');
                isSelectedAll = false;
            } );">
            <button x-show="!isSelectedAll" class="btn btn-dark btn-sm my-2" @click="toggleAll">{{ __tr('Select All')
                }}</button>
            <button x-show="isSelectedAll" class="btn btn-dark btn-sm my-2" @click="toggleAll">{{ __tr('Unselect All')
                }}</button>
            <div class="btn-group">
                <button :class="!selectedContacts.length ? 'disabled' : ''"
                    class="btn btn-danger mt-1 btn-sm dropdown-toggle" type="button" data-toggle="dropdown"
                    aria-expanded="false">
                    {{ __tr('Bulk Actions') }}
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" @click.prevent="deleteSelectedContacts" href="#">{{ __tr('Delete Selected
                        Contacts') }}</a>
                    <a class="dropdown-item" data-toggle="modal" data-target="#lwAssignGroups" href="#">{{ __tr('Assign
                        Group to Selected Contacts') }}</a>
                </div>
            </div>
            <!-- Assign Groups to the selected contacts -->
            <x-lw.modal id="lwAssignGroups" :header="__tr('Assign Groups to Selected Contacts')" :hasForm="true"
                data-pre-callback="appFuncs.clearContainer">
                <!-- form body -->
                <div class="lw-form-modal-body p-4">
                    <!-- form fields form fields -->
                    <x-lw.input-field x-model="selectedGroupsForSelectedContacts" type="selectize"
                        data-lw-plugin="lwSelectize" id="lwSelectGroupsField" data-form-group-class="" data-selected=" "
                        :label="__tr('Groups')" name="contact_groups[]" multiple>
                        <x-slot name="selectOptions">
                            <option value="">{{ __tr('Select Groups') }}</option>
                            @foreach($vendorContactGroups as $vendorContactGroup)
                            <option value="{{ $vendorContactGroup['_id'] }}">{{ $vendorContactGroup['title'] }} {{ $vendorContactGroup['status'] == 5  ? __tr('(Archived)') : '' }}</option>
                            @endforeach
                        </x-slot>
                    </x-lw.input-field>
                </div>
                <!-- form footer -->
                <div class="modal-footer">
                    <!-- Submit Button -->
                    <button type="button" @click="assignGroupsToSelectedContacts" class="btn btn-primary">{{
                        __('Submit') }}</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
                </div>
                <!--/  Add New Contact Form -->
            </x-lw.modal>
            <!--/ Assign Groups to the selected contacts -->
            <x-lw.datatable data-page-length="500" id="lwContactList" :url="route('vendor.contact.read.list', [
                'groupUid' => $groupUid
            ])">
                <th style="width: 1px;padding:0;" data-name="none"></th>
                <th data-name="none" data-template="#lwSelectMultipleContactsCheckbox">{{ __tr('Select') }}</th>
                <th data-orderable="true" data-name="first_name">{{ __tr('First Name') }}</th>
                <th data-orderable="true" data-name="last_name">{{ __tr('Last Name') }}</th>
                <th data-name="phone_number">{{ __tr('Mobile Number') }}</th>
                <th data-name="language_code">{{ __tr('Language Code') }}</th>
                <th data-orderable="true" data-name="created_at">{{ __tr('Created on') }}</th>
                <th data-name="country_name">{{ __tr('Country') }}</th>
                <th data-orderable="true" data-name="email">{{ __tr('Email') }}</th>
                <th data-orderable="true" data-name="whatsapp_opt_out">{{ __tr('Marketing') }}</th>
                @if (isAiBotAvailable())
                <th data-orderable="true" data-name="disable_ai_bot">{{ __tr('AI Bot') }}</th>
                @endif
                <th data-template="#contactActionColumnTemplate" name="null">{{ __tr('Action') }}</th>
            </x-lw.datatable>
        </div>
        <!-- action template -->
        <script type="text/template" id="lwSelectMultipleContactsCheckbox">
            <input @click="toggle('<%- __tData._uid %>')" type="checkbox" name="selected_contacts[]" class="lw-checkboxes custom-checkbox" value="<%- __tData._uid %>">
        </script>
        <script type="text/template" id="contactActionColumnTemplate">
            <div class="d-flex flex-wrap gap-2 action-buttons-container">
                <!-- Details Button -->
                <a data-pre-callback="appFuncs.clearContainer" 
                    title="{{ __tr('Details') }}" 
                    class="action-btn details-btn lw-ajax-link-action"
                    data-response-template="#lwDetailsContactBody" 
                    href="<%= __Utils.apiURL('{{ route('vendor.contact.read.update.data', ['contactIdOrUid']) }}', {'contactIdOrUid': __tData._uid}) %>"  
                    data-toggle="modal" 
                    data-target="#lwDetailsContact">
                        <i class="fa fa-info-circle me-1"></i> {{ __tr('Details') }}
                    </a>

                    <!-- Edit Button -->
                    <a data-pre-callback="appFuncs.clearContainer" 
                    title="{{ __tr('Edit') }}" 
                    class="action-btn edit-btn lw-ajax-link-action"
                    data-response-template="#lwEditContactBody" 
                    href="<%= __Utils.apiURL('{{ route('vendor.contact.read.update.data', ['contactIdOrUid']) }}', {'contactIdOrUid': __tData._uid}) %>"  
                    data-toggle="modal" 
                    data-target="#lwEditContact">
                        <i class="fa fa-edit me-1"></i> {{ __tr('Edit') }}
                    </a>
                
                <!-- Messaging Buttons -->
                @if(hasVendorAccess('messaging'))
                <a data-pre-callback="appFuncs.clearContainer" 
                   title="{{ __tr('Send Template Message') }}" 
                   class="action-btn template-btn"
                   href="<%= __Utils.apiURL('{{ route('vendor.template_message.contact.view', ['contactUid']) }}', {'contactUid': __tData._uid}) %>">
                    <i class="fab fa-whatsapp me-1"></i> {{ __tr('Template') }}
                </a>
                
                <a data-pre-callback="appFuncs.clearContainer" 
                   title="{{ __tr('Chat') }}" 
                   class="action-btn chat-btn"
                   href="<%= __Utils.apiURL('{{ route('vendor.chat_message.contact.view', ['contactUid']) }}', {'contactUid': __tData._uid}) %>">
                    <i class="fa fa-comments"></i> {{ __tr('Chat') }}
                </a>
                @endif
                
                <!-- Delete Button -->
                <a data-method="post" 
                    href="<%= __Utils.apiURL('{{ route('vendor.contact.write.delete', ['contactIdOrUid']) }}', {'contactIdOrUid': __tData._uid}) %>" 
                    class="action-btn delete-btn lw-ajax-link-action-via-confirm"
                    data-confirm="#lwDeleteContact-template" 
                    title="{{ __tr('Delete') }}" 
                    data-callback-params="{{ json_encode(['datatableId' => '#lwContactList']) }}" 
                    data-callback="appFuncs.modelSuccessCallback"
                    style="
                        display: inline-flex;
                        align-items: center;
                        color: #dc3545; /* Bootstrap danger red */
                        font-weight: 500;
                        text-decoration: none;
                        transition: all 0.3s ease;
                        cursor: pointer;
                    "
                    onmouseover="this.style.color='#a71d2a'; this.style.transform='scale(1.05)';"
                    onmouseout="this.style.color='#dc3545'; this.style.transform='scale(1)';"
                    >
                    <i class="fa fa-trash me-1" style="color: #dc3545; transition: transform 0.3s ease;"></i> {{ __tr('Delete') }}
                    </a>
                
                <!-- Remove Contact Button -->
                @if($currentGroup!=null)
                <a data-method="post" 
                   href="<%= __Utils.apiURL('{{ route('vendor.contact.write.remove', ['contactIdOrUid', 'groupUid' => $groupUid]) }}', {'contactIdOrUid': __tData._uid}) %>" 
                   class="action-btn remove-btn lw-ajax-link-action-via-confirm" 
                   data-confirm="#lwRemoveContact-template" 
                   title="{{ __tr('Remove contact from group') }}" 
                   data-callback-params="{{ json_encode(['datatableId' => '#lwContactList']) }}" 
                   data-callback="appFuncs.modelSuccessCallback">
                    <i class="fa fa-user-times me-1"></i> {{ __tr('Remove') }}
                </a>
                @endif
            </div>
        </script>
        <!-- /action template -->
        <!-- Contact delete template -->
        <script type="text/template" id="lwDeleteContact-template">
            <h2>{{ __tr('Are You Sure!') }}</h2>
            <p>{{ __tr('You want to delete this Contact permanently?') }}</p>
    </script>
        <!-- /Contact delete template -->
         <!-- Contact remove template -->
         <script type="text/template" id="lwRemoveContact-template">
            <h2>{{ __tr('Are You Sure!') }}</h2>
            <p>{{ __tr('You want to remove this Contact from this group?') }}</p>
    </script>
        <!-- /Contact remove template -->
    </div>
</div>
<!-- Delete All Contacts Confirmation Modal -->
<div class="modal fade" id="deleteAllContactsModal" tabindex="-1" role="dialog" aria-labelledby="deleteAllContactsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAllContactsModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    {{ __tr('Delete All Contacts') }}
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ __tr('Are you sure you want to delete all contacts? This action cannot be undone.') }}
                </div>
                <p class="mb-0">{{ __tr('All contact information will be permanently removed from the system.') }}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-dismiss="modal">
                    <i class="fas fa-times me-1"></i> {{ __tr('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteAllContacts">
                    <i class="fas fa-trash-alt me-1"></i> {{ __tr('Delete All') }}
                </button>
            </div>
        </div>
    </div>
</div>

@push('appScripts')
<script>
(function($) {
    'use strict';

    // Handle delete all contacts
    $(document).on('click', '.delete-all-contacts', function(e) {
        e.preventDefault();
        $('#deleteAllContactsModal').modal('show');
    });

    // Handle confirm delete all
    $(document).on('click', '#confirmDeleteAllContacts', function() {
        var $modal = $('#deleteAllContactsModal');
        var $btn = $(this);
        
        // Disable button and show loading state
        $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> {{ __tr("Deleting...") }}');
        
        $.ajax({
            url: '{{ route("vendor.contact.write.delete_all") }}',
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $modal.modal('hide');
                
                if (response.status === true) {
                    // Reload the datatable
                    if (typeof window.lwContactListTable !== 'undefined') {
                        window.lwContactListTable.draw();
                    }
                    // Show success message
                    appFuncs.notification('{{ __tr("All contacts have been deleted successfully.") }}', 'success');
                } else {
                    appFuncs.notification(response.message || '{{ __tr("Failed to delete contacts. Please try again.") }}', 'error');
                }
            },
            error: function(xhr) {
                appFuncs.notification(xhr.responseJSON?.message || '{{ __tr("An error occurred while processing your request.") }}', 'error');
            },
            complete: function() {
                // Reset button state
                $btn.prop('disabled', false).html('<i class="fas fa-trash-alt me-1"></i> {{ __tr("Delete All") }}');
            }
        });
    });
    window.onUpdateContactDetails = function(responseData, callbackParams) {
        appFuncs.modelSuccessCallback(responseData, callbackParams);
    }

    // Add row highlighting on button hover
    $(document).on('mouseenter', '.action-btn', function() {
        $(this).closest('tr').addClass('highlight-row');
    }).on('mouseleave', '.action-btn', function() {
        $(this).closest('tr').removeClass('highlight-row');
    });

    // Add button hover animations
    $(document).on('mouseenter', '.action-btn', function() {
        $(this).addClass('btn-hover');
    }).on('mouseleave', '.action-btn', function() {
        $(this).removeClass('btn-hover');
    });

    // Handle external contacts fetch
    $('#lwFetchExternalContacts').on('click', function() {
        var $button = $(this);
        var originalText = $button.html();

        // Show loading state
        $button.prop('disabled', true);
        $button.html('<i class="fas fa-spinner fa-spin"></i> {{ __tr("Fetching...") }}');

        // Make AJAX request to fetch external contacts
        __DataRequest.post('{{ route("vendor.contact.write.fetch_external") }}', {}, function(responseData) {
            // Reset button state
            $button.prop('disabled', false);
            $button.html(originalText);

            if (responseData.reaction == 1) {
                // Success - refresh the contact list
                $('#lwContactList').DataTable().ajax.reload();
                showSuccessMessage(responseData.data.message || '{{ __tr("External contacts fetched successfully!") }}');
            } else {
                // Error
                showErrorMessage(responseData.data.message || '{{ __tr("Failed to fetch external contacts") }}');
            }
        }, function(error) {
            // Reset button state on error
            $button.prop('disabled', false);
            $button.html(originalText);
            showErrorMessage('{{ __tr("An error occurred while fetching external contacts") }}');
        });
    });
})(jQuery);
</script>
<style>
    /* Modern Button Styles */
    .action-buttons-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: flex-start;
    }
    
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 6px 12px;
        font-size: 0.8rem;
        font-weight: 500;
        border-radius: 50px;
        transition: all 0.3s ease;
        text-decoration: none;
        box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        min-width: 90px;
        border: 2px solid transparent;
    }
    
    .action-btn i {
        margin-right: 5px;
        font-size: 0.9rem;
    }
    
    /* Button Types */
    .details-btn {
        background-color: transparent;
        color: #3498db;
        border-color: #3498db;
    }
    
    .edit-btn {
        background-color: transparent;
        color: #2ecc71;
        border-color: #2ecc71;
    }
    
    .template-btn, .chat-btn {
        background-color: transparent;
        color: #27ae60;
        border-color: #27ae60;
    }
    
    .delete-btn {
        background-color: transparent;
        color: #e74c3c;
        border-color: #e74c3c;
    }
    
    .remove-btn {
        background-color: transparent;
        color: #f39c12;
        border-color: #f39c12;
    }
    
    /* Hover Effects */
    .details-btn.btn-hover {
        background-color: #3498db;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
    }
    
    .edit-btn.btn-hover {
        background-color: #2ecc71;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(46, 204, 113, 0.3);
    }
    
    .template-btn.btn-hover, .chat-btn.btn-hover {
        background-color: #27ae60;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
    }
    
    .delete-btn.btn-hover {
        background-color: #e74c3c;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
    }
    
    .remove-btn.btn-hover {
        background-color: #f39c12;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
    }
    
    /* Row Highlight */
    .highlight-row {
        background-color: rgba(236, 240, 241, 0.6) !important;
        transition: background-color 0.3s ease;
    }
    .hover-text-danger:hover {
    color: #dc3545 !important; /* Bootstrap's danger color */
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .action-buttons-container {
            justify-content: center;
        }
        
        .action-btn {
            padding: 5px 10px;
            font-size: 0.75rem;
            min-width: 80px;
        }
    }
    
    @media (max-width: 576px) {
        .action-btn {
            margin-bottom: 5px;
            width: calc(50% - 5px);
            justify-content: center;
        }
    }
</style>
@endpush
@endsection()
