<?php
/**
 * Simple test webhook endpoint for testing webhook nodes
 * 
 * Usage: Place this file on a web server and use its URL in webhook nodes
 * Example: https://yourdomain.com/test-webhook.php
 */

// Set content type to JSON
header('Content-Type: application/json');

// Enable CORS for testing
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get request method and data
$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true) ?? [];

// Log the request (optional - for debugging)
$logEntry = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $method,
    'headers' => getallheaders(),
    'data' => $data,
    'query' => $_GET
];

// You can uncomment this to log requests to a file
// file_put_contents('webhook-log.txt', json_encode($logEntry) . "\n", FILE_APPEND);

// Simulate different responses based on request data
$response = [
    'success' => true,
    'message' => 'Webhook received successfully',
    'method' => $method,
    'timestamp' => date('c'),
    'data' => [
        'received_data' => $data,
        'processed_at' => time(),
        'result' => 'success',
        'user' => [
            'name' => $data['context']['contact_phone'] ?? 'Unknown',
            'status' => 'active'
        ]
    ]
];

// Simulate different scenarios based on input
if (isset($data['test_scenario'])) {
    switch ($data['test_scenario']) {
        case 'error':
            http_response_code(400);
            $response = [
                'success' => false,
                'error' => 'Test error scenario',
                'code' => 'TEST_ERROR'
            ];
            break;
            
        case 'timeout':
            // Simulate slow response
            sleep(2);
            break;
            
        case 'invalid_json':
            // Return invalid JSON
            echo 'This is not valid JSON';
            exit();
            
        default:
            // Default success response
            break;
    }
}

// Add some sample data that can be mapped
$response['data']['sample_values'] = [
    'order_id' => 'ORD-' . rand(1000, 9999),
    'status' => 'confirmed',
    'amount' => rand(10, 100),
    'currency' => 'USD'
];

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>
