<?php

/**
 * SystemAdminController.php - Controller file
 *
 * This file is part of the Auth component.
 *-----------------------------------------------------------------------------*/

namespace App\Yantrana\Components\Auth\Controllers;

use App\Yantrana\Base\BaseController;
use App\Yantrana\Components\Auth\AuthEngine;
use App\Yantrana\Components\Auth\Requests\SystemAdminCreateRequest;
use Illuminate\Http\Request;

class SystemAdminController extends BaseController
{
    /**
     * @var AuthEngine - Auth Engine
     */
    protected $authEngine;

    /**
     * Constructor
     *
     * @param AuthEngine $authEngine - Auth Engine
     * @return void
     *-----------------------------------------------------------------------*/
    public function __construct(AuthEngine $authEngine)
    {
        $this->authEngine = $authEngine;
    }

    /**
     * Show system admin dashboard
     *
     * @return \Illuminate\View\View
     *-----------------------------------------------------------------------*/
    public function dashboard()
    {
        return $this->loadView('system-admin.dashboard');
    }

    /**
     * Show super admins list
     *
     * @return \Illuminate\View\View
     *-----------------------------------------------------------------------*/
    public function superAdminsList()
    {
        return $this->loadView('system-admin.super-admins-list');
    }

    /**
     * Get super admins data for DataTables
     *
     * @return \Illuminate\Http\JsonResponse
     *-----------------------------------------------------------------------*/
    public function prepareSuperAdminsList()
    {
        $processReaction = $this->authEngine->prepareSuperAdminsList();

        return $this->processResponse($processReaction, [], [], true);
    }

    /**
     * Show create super admin form
     *
     * @return \Illuminate\View\View
     *-----------------------------------------------------------------------*/
    public function createSuperAdmin()
    {
        return $this->loadView('system-admin.create-super-admin');
    }

    /**
     * Process create super admin request
     *
     * @param SystemAdminCreateRequest $request
     * @return \Illuminate\Http\JsonResponse
     *-----------------------------------------------------------------------*/
    public function processCreateSuperAdmin(SystemAdminCreateRequest $request)
    {
        $processReaction = $this->authEngine->processCreateSuperAdmin($request->all());

        return $this->processResponse($processReaction, [], [], true);
    }

    /**
     * Process update super admin status
     *
     * @param Request $request
     * @param string $superAdminUid
     * @return \Illuminate\Http\JsonResponse
     *-----------------------------------------------------------------------*/
    public function processUpdateSuperAdminStatus(Request $request, $superAdminUid)
    {
        $processReaction = $this->authEngine->processUpdateSuperAdminStatus($superAdminUid, $request->all());

        return $this->processResponse($processReaction, [], [], true);
    }

    /**
     * Process delete super admin
     *
     * @param string $superAdminUid
     * @return \Illuminate\Http\JsonResponse
     *-----------------------------------------------------------------------*/
    public function processDeleteSuperAdmin($superAdminUid)
    {
        $processReaction = $this->authEngine->processDeleteSuperAdmin($superAdminUid);

        return $this->processResponse($processReaction, [], [], true);
    }
}
