<?php
/**
 * Debug script for new_message trigger
 * Run this with: php artisan tinker < test_new_message_debug.php
 */

// Check if new_message trigger type exists in config
$triggerTypes = config('__tech.bot_reply_trigger_types');
echo "=== Trigger Types Available ===\n";
foreach ($triggerTypes as $key => $type) {
    echo "- {$key}: {$type['title']}\n";
}
echo "\n";

// Check if there are any bot flows with new_message trigger
$newMessageFlows = \App\Yantrana\Components\BotReply\Models\BotFlowModel::where('trigger_type', 'new_message')->get();
echo "=== New Message Bot Flows ===\n";
echo "Count: " . $newMessageFlows->count() . "\n";
foreach ($newMessageFlows as $flow) {
    echo "- Flow ID: {$flow->_uid}, Title: {$flow->title}, Status: {$flow->status}\n";
}
echo "\n";

// Check if there are any bot replies with new_message trigger
$newMessageReplies = \App\Yantrana\Components\BotReply\Models\BotReplyModel::where('trigger_type', 'new_message')->get();
echo "=== New Message Bot Replies ===\n";
echo "Count: " . $newMessageReplies->count() . "\n";
foreach ($newMessageReplies as $reply) {
    echo "- Reply ID: {$reply->_uid}, Trigger: {$reply->trigger_type}, Flow ID: {$reply->bot_flows__id}\n";
}
echo "\n";

// Check all bot replies for a specific vendor (if any)
$vendorId = 1; // Change this to an actual vendor ID
$allReplies = \App\Yantrana\Components\BotReply\Models\BotReplyModel::where('vendors__id', $vendorId)->get();
echo "=== All Bot Replies for Vendor {$vendorId} ===\n";
echo "Count: " . $allReplies->count() . "\n";
$triggerTypeCounts = $allReplies->groupBy('trigger_type')->map(function($group) {
    return $group->count();
});
foreach ($triggerTypeCounts as $type => $count) {
    echo "- {$type}: {$count}\n";
}
echo "\n";

echo "=== Debug Complete ===\n";
