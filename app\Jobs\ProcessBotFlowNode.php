<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Yantrana\Components\BotReply\Services\FlowExecutionService;
use App\Yantrana\Components\BotReply\Services\FlowIntegrationService;
use App\Yantrana\Components\BotReply\Models\BotFlowModel;
use App\Models\UserActiveFlow;
use App\Yantrana\Components\Contact\Models\ContactModel;
use App\Yantrana\Components\WhatsAppService\WhatsAppServiceEngine;

class ProcessBotFlowNode implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The phone number of the user
     *
     * @var string
     */
    protected $phoneNumber;

    /**
     * The flow ID
     *
     * @var string
     */
    protected $flowId;

    /**
     * The node ID to process
     *
     * @var string
     */
    protected $nodeId;

    /**
     * The flow context
     *
     * @var array
     */
    protected $context;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * Create a new job instance.
     *
     * @param string $phoneNumber
     * @param string $flowId
     * @param string $nodeId
     * @param array $context
     * @return void
     */
    public function __construct($phoneNumber, $flowId, $nodeId, $context = [])
    {
        $this->phoneNumber = $phoneNumber;
        $this->flowId = $flowId;
        $this->nodeId = $nodeId;
        $this->context = $context;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('Processing delayed bot flow node', [
            'phone_number' => $this->phoneNumber,
            'flow_id' => $this->flowId,
            'node_id' => $this->nodeId,
            'job_id' => $this->job->getJobId(),
            'is_delayed_execution' => $this->context['is_delayed_execution'] ?? false,
            'wait_until' => isset($this->context['wait_until']) ? date('Y-m-d H:i:s', $this->context['wait_until']) : null
        ]);

        // Check if this is a delayed execution that needs to wait
        if (isset($this->context['is_delayed_execution']) && $this->context['is_delayed_execution']) {
            $waitUntil = $this->context['wait_until'] ?? null;
            if ($waitUntil && time() < $waitUntil) {
                $remainingWait = $waitUntil - time();
                Log::info('Job executed too early, waiting for delay period', [
                    'phone_number' => $this->phoneNumber,
                    'node_id' => $this->nodeId,
                    'wait_until' => date('Y-m-d H:i:s', $waitUntil),
                    'remaining_seconds' => $remainingWait
                ]);
                
                // Sleep for the remaining time
                sleep($remainingWait);
                
                Log::info('Wait period completed, continuing with node execution', [
                    'phone_number' => $this->phoneNumber,
                    'node_id' => $this->nodeId
                ]);
            }
        }

        try {
            // Get the contact
            $contact = ContactModel::where('wa_id', $this->phoneNumber)->first();
            if (!$contact) {
                Log::error('Contact not found for delayed flow processing', [
                    'phone_number' => $this->phoneNumber,
                    'flow_id' => $this->flowId,
                    'node_id' => $this->nodeId
                ]);
                return;
            }

            // Get the bot flow
            $botFlow = BotFlowModel::where('_uid', $this->flowId)->first();
            if (!$botFlow) {
                Log::error('Bot flow not found for delayed processing', [
                    'phone_number' => $this->phoneNumber,
                    'flow_id' => $this->flowId,
                    'node_id' => $this->nodeId
                ]);
                return;
            }

            // Check if user still has an active flow
            $activeFlow = UserActiveFlow::getActiveFlow($this->phoneNumber);
            if (!$activeFlow || $activeFlow->flow_id != $botFlow->_id) {
                Log::warning('No active flow or different flow active for delayed processing', [
                    'phone_number' => $this->phoneNumber,
                    'expected_flow_id' => $botFlow->_id,
                    'actual_flow_id' => $activeFlow ? $activeFlow->flow_id : null,
                    'node_id' => $this->nodeId
                ]);
                
                // Try to recreate the active flow session if it was a wait node processing
                if (!$activeFlow) {
                    Log::info('Attempting to recreate active flow session for delayed wait node processing', [
                        'phone_number' => $this->phoneNumber,
                        'flow_id' => $botFlow->_id,
                        'node_id' => $this->nodeId
                    ]);
                    
                    // Create a new active flow session for this delayed processing
                    $contact = ContactModel::where('wa_id', $this->phoneNumber)->first();
                    if ($contact) {
                        $activeFlow = UserActiveFlow::setActiveFlow(
                            $contact->users__id ?? null,
                            $botFlow->_id,
                            $this->phoneNumber,
                            $this->nodeId
                        );
                        
                        // Set the context data
                        $activeFlowData = $activeFlow->__data ?? [];
                        $activeFlowData['flow_context'] = $this->context;
                        $activeFlowData['recreated_for_wait'] = true;
                        $activeFlowData['recreated_at'] = now();
                        $activeFlow->__data = $activeFlowData;
                        $activeFlow->save();
                        
                        Log::info('Successfully recreated active flow session', [
                            'phone_number' => $this->phoneNumber,
                            'flow_id' => $botFlow->_id,
                            'node_id' => $this->nodeId
                        ]);
                    } else {
                        Log::error('Cannot recreate active flow - contact not found', [
                            'phone_number' => $this->phoneNumber,
                            'node_id' => $this->nodeId
                        ]);
                        return;
                    }
                } else {
                    // Different flow is active, don't interfere
                    return;
                }
            }

            // Get flow data
            $flowData = $botFlow->getFlowNodesData();
            if (!$flowData) {
                // Try legacy format
                $legacyData = $botFlow->getFlowBuilderData();
                if ($legacyData) {
                    $botFlowEngine = app(\App\Yantrana\Components\BotReply\BotFlowEngine::class);
                    $flowData = $botFlowEngine->convertToNewFlowStructure($legacyData, $botFlow->_uid);
                }
            }

            if (!$flowData || empty($flowData['nodes'])) {
                Log::error('No valid flow data found for delayed processing', [
                    'phone_number' => $this->phoneNumber,
                    'flow_id' => $this->flowId,
                    'node_id' => $this->nodeId
                ]);
                return;
            }

            // Continue flow execution from the specified node
            $flowExecutionService = new FlowExecutionService();
            $result = $flowExecutionService->executeFlow(
                $flowData,
                $this->nodeId,
                $this->context
            );

            Log::info('Delayed flow execution completed', [
                'phone_number' => $this->phoneNumber,
                'flow_id' => $this->flowId,
                'node_id' => $this->nodeId,
                'response_count' => count($result['responses'] ?? []),
                'is_complete' => $result['is_complete'] ?? false,
                'next_node' => $result['current_node_id'] ?? null
            ]);

            // Update active flow state
            $this->updateActiveFlowState($activeFlow, $result);

            // Send responses to WhatsApp
            $this->sendResponsesToWhatsApp($contact, $result);

            // Clean up if flow is complete
            if ($result['is_complete'] ?? false) {
                Log::info('Flow completed after delayed processing, cleaning up', [
                    'phone_number' => $this->phoneNumber,
                    'flow_id' => $this->flowId
                ]);
                UserActiveFlow::where('phone_number', $this->phoneNumber)->delete();
            }

        } catch (\Exception $e) {
            Log::error('Error processing delayed bot flow node', [
                'phone_number' => $this->phoneNumber,
                'flow_id' => $this->flowId,
                'node_id' => $this->nodeId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Update active flow state with execution result
     *
     * @param object $activeFlow
     * @param array $result
     * @return void
     */
    private function updateActiveFlowState($activeFlow, $result)
    {
        $activeFlowData = $activeFlow->__data ?? [];

        // Update context
        if (isset($result['context'])) {
            $activeFlowData['flow_context'] = $result['context'];
        }

        // Update current node
        if (isset($result['current_node_id'])) {
            $activeFlowData['current_node_id'] = $result['current_node_id'];
        }

        // Set waiting for input flag
        $waitingForInput = false;
        if (isset($result['responses'])) {
            foreach ($result['responses'] as $response) {
                if ($response['requires_input'] ?? false) {
                    $waitingForInput = true;
                    break;
                }
            }
        }
        $activeFlowData['waiting_for_input'] = $waitingForInput;

        // If flow is complete, mark for deletion
        if ($result['is_complete'] ?? false) {
            $activeFlowData['completed'] = true;
            $activeFlowData['completed_at'] = now();
        }

        $activeFlow->__data = $activeFlowData;
        $activeFlow->save();

        Log::info('Updated active flow state after delayed processing', [
            'phone_number' => $this->phoneNumber,
            'current_node' => $result['current_node_id'] ?? null,
            'waiting_for_input' => $waitingForInput,
            'is_complete' => $result['is_complete'] ?? false
        ]);
    }

    /**
     * Send responses to WhatsApp
     *
     * @param object $contact
     * @param array $result
     * @return void
     */
    private function sendResponsesToWhatsApp($contact, $result)
    {
        if (empty($result['responses'])) {
            return;
        }

        $whatsAppService = app(WhatsAppServiceEngine::class);

        foreach ($result['responses'] as $response) {
            try {
                Log::info('Sending delayed response to WhatsApp', [
                    'phone_number' => $this->phoneNumber,
                    'response_type' => $response['type'] ?? 'unknown',
                    'node_id' => $response['node_id'] ?? null
                ]);

                $text = $response['text'] ?? '';
                $isMediaMessage = false;
                $options = ['bot_reply' => true];

                if ($response['type'] === 'interactive') {
                    // Handle interactive messages
                    if (isset($response['list_data'])) {
                        $options['interaction_message_data'] = [
                            'body_text' => $text,
                            'interactive_type' => 'list',
                            'list_data' => $response['list_data']
                        ];
                    } elseif (isset($response['buttons'])) {
                        $options['interaction_message_data'] = [
                            'body_text' => $text,
                            'interactive_type' => 'button',
                            'buttons' => $response['buttons'],
                            'header_text' => $response['header_text'] ?? '',
                            'footer_text' => $response['footer_text'] ?? ''
                        ];
                        $text = '';
                    }

                    // Send interactive message
                    $whatsAppService->sendInteractiveMessageProcess([
                        'messageBody' => $text,
                        'contactUid' => $contact->_uid,
                        'interactive_data' => $options['interaction_message_data']
                    ], false, $contact->vendors__id, $options);

                } elseif ($response['type'] === 'media_message') {
                    // Handle media messages
                    $isMediaMessage = true;
                    $options['media_message_data'] = [
                        'header_type' => $response['media_type'] ?? 'document',
                        'media_link' => $response['media_url'] ?? '',
                        'caption' => $response['caption'] ?? '',
                        'file_name' => $response['filename'] ?? ''
                    ];

                    // Send media message
                    $whatsAppService->processSendChatMessage([
                        'messageBody' => $response['caption'] ?? '',
                        'contactUid' => $contact->_uid,
                    ], $isMediaMessage, $contact->vendors__id, $options);

                } else {
                    // Handle regular text messages
                    $whatsAppService->processSendChatMessage([
                        'messageBody' => $text,
                        'contactUid' => $contact->_uid,
                    ], $isMediaMessage, $contact->vendors__id, $options);
                }

            } catch (\Exception $e) {
                Log::error('Error sending delayed response to WhatsApp', [
                    'phone_number' => $this->phoneNumber,
                    'response' => $response,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Format response for WhatsApp service
     *
     * @param array $response
     * @param object $contact
     * @return array|null
     */
    private function formatResponseForWhatsApp($response, $contact)
    {
        $type = $response['type'] ?? 'message';

        switch ($type) {
            case 'message':
                return [
                    'type' => 'text',
                    'text' => $response['text'] ?? '',
                    'to' => $contact->wa_id
                ];

            case 'interactive':
                $formatted = [
                    'type' => 'interactive',
                    'to' => $contact->wa_id,
                    'interactive' => [
                        'type' => isset($response['list_data']) ? 'list' : 'button',
                        'body' => [
                            'text' => $response['text'] ?? ''
                        ]
                    ]
                ];

                if (isset($response['list_data'])) {
                    $formatted['interactive']['action'] = [
                        'button' => $response['list_data']['button_text'] ?? 'Select an option',
                        'sections' => $response['list_data']['sections'] ?? []
                    ];
                } elseif (isset($response['buttons'])) {
                    $formatted['interactive']['action'] = [
                        'buttons' => array_map(function($button) {
                            return [
                                'type' => 'reply',
                                'reply' => [
                                    'id' => $button['id'],
                                    'title' => $button['title']
                                ]
                            ];
                        }, $response['buttons'])
                    ];
                }

                return $formatted;

            case 'media_message':
                return [
                    'type' => $response['media_type'] ?? 'image',
                    'to' => $contact->wa_id,
                    $response['media_type'] ?? 'image' => [
                        'link' => $response['media_url'] ?? '',
                        'caption' => $response['caption'] ?? ''
                    ]
                ];

            case 'wait':
                // Wait responses are handled by the job scheduling, not sent as messages
                return null;

            default:
                Log::warning('Unknown response type for WhatsApp formatting', [
                    'type' => $type,
                    'response' => $response
                ]);
                return null;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('ProcessBotFlowNode job failed', [
            'phone_number' => $this->phoneNumber,
            'flow_id' => $this->flowId,
            'node_id' => $this->nodeId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
