table.dataTable.dtr-inline.collapsed>tbody>tr>td:first-child,
table.dataTable.dtr-inline.collapsed>tbody>tr>th:first-child {
    cursor: pointer;
    padding-left: 30px;
    position: relative
}

table.dataTable.dtr-inline.collapsed>tbody>tr>td:first-child:before,
table.dataTable.dtr-inline.collapsed>tbody>tr>th:first-child:before {
    background-color: #31b131;
    border: 2px solid #fff;
    border-radius: 16px;
    box-shadow: 0 0 3px #444;
    box-sizing: content-box;
    color: #fff;
    content: "+";
    display: block;
    height: 16px;
    left: 4px;
    line-height: 14px;
    position: absolute;
    text-align: center;
    top: 8px;
    width: 16px
}

table.dataTable.dtr-inline.collapsed>tbody>tr>td:first-child.dataTables_empty:before,
table.dataTable.dtr-inline.collapsed>tbody>tr>th:first-child.dataTables_empty:before {
    display: none
}

table.dataTable.dtr-inline.collapsed>tbody>tr.parent>td:first-child:before,
table.dataTable.dtr-inline.collapsed>tbody>tr.parent>th:first-child:before {
    background-color: #d33333;
    content: "-"
}

table.dataTable.dtr-inline.collapsed>tbody>tr.child td:before {
    display: none
}

table.dataTable.dtr-inline.collapsed.compact>tbody>tr>td:first-child,
table.dataTable.dtr-inline.collapsed.compact>tbody>tr>th:first-child {
    padding-left: 27px
}

table.dataTable.dtr-inline.collapsed.compact>tbody>tr>td:first-child:before,
table.dataTable.dtr-inline.collapsed.compact>tbody>tr>th:first-child:before {
    border-radius: 14px;
    height: 14px;
    left: 4px;
    line-height: 12px;
    top: 5px;
    width: 14px
}

table.dataTable.dtr-column>tbody>tr>td.control,
table.dataTable.dtr-column>tbody>tr>th.control {
    cursor: pointer;
    position: relative
}

table.dataTable.dtr-column>tbody>tr>td.control:before,
table.dataTable.dtr-column>tbody>tr>th.control:before {
    background-color: #31b131;
    border: 2px solid #fff;
    border-radius: 16px;
    box-shadow: 0 0 3px #444;
    box-sizing: content-box;
    color: #fff;
    content: "+";
    display: block;
    height: 16px;
    left: 50%;
    line-height: 14px;
    margin-left: -10px;
    margin-top: -10px;
    position: absolute;
    text-align: center;
    top: 50%;
    width: 16px
}

table.dataTable.dtr-column>tbody>tr.parent td.control:before,
table.dataTable.dtr-column>tbody>tr.parent th.control:before {
    background-color: #d33333;
    content: "-"
}

table.dataTable>tbody>tr.child {
    padding: .5em 1em
}

table.dataTable>tbody>tr.child:hover {
    background: transparent !important
}

table.dataTable>tbody>tr.child ul {
    display: inline-block;
    list-style-type: none;
    margin: 0;
    padding: 0
}

table.dataTable>tbody>tr.child ul li {
    border-bottom: 1px solid #efefef;
    padding: .5em 0
}

table.dataTable>tbody>tr.child ul li:first-child {
    padding-top: 0
}

table.dataTable>tbody>tr.child ul li:last-child {
    border-bottom: none
}

table.dataTable>tbody>tr.child span.dtr-title {
    display: inline-block;
    font-weight: 700;
    min-width: 75px
}

table.dataTable td,
table.dataTable th {
    white-space: normal
}

div.dataTables_wrapper .table-striped tbody tr td {
    vertical-align: initial
}

div.dataTables_wrapper .table-striped tbody tr:nth-of-type(odd) {
    background-color: hsla(0, 0%, 100%, .05)
}

div.dataTables_wrapper table.dataTable>tbody>tr:hover {
    background-color: #ededed
}

@media (max-width:1280px) {
    div.dataTables_wrapper div.dataTables_filter input {
        width: 100%
    }

    div.dataTables_wrapper div.dataTables_filter label {
        text-align: center;
        white-space: unset
    }
}

table.dataTable {
    max-width: 100% !important
}

table.dataTable .btn {
    margin-bottom: 8px
}

.dataTables_filter {
    min-width: 100%
}

table.dataTable.dtr-inline.collapsed>tbody>tr.child>td.child,
table.dataTable.dtr-inline.collapsed>tbody>tr.child>th.child {
    padding: 8px
}

table.dataTable.dtr-inline.collapsed>tbody>tr.child>td.child li,
table.dataTable.dtr-inline.collapsed>tbody>tr.child>th.child li {
    list-style: none
}

table.dataTable.dtr-inline.collapsed>tbody>tr.child>td.child li .dtr-data,
table.dataTable.dtr-inline.collapsed>tbody>tr.child>td.child li .dtr-title,
table.dataTable.dtr-inline.collapsed>tbody>tr.child>th.child li .dtr-data,
table.dataTable.dtr-inline.collapsed>tbody>tr.child>th.child li .dtr-title {
    display: block;
    padding: 8px 0
}

img {
    max-width: 100%
}

a {
    color: #2aac32
}

a:hover {
    color: #15c420
}

.dataTables_wrapper .dataTables_length select {
    background-color: transparent;
    border: 1px solid #aaa;
    border-radius: 3px;
    display: inline-block;
    padding: 4px;
    width: auto
}

[data-show-if] {
    display: none
}

.lds-ring {
    display: inline-block;
    height: 80px;
    position: relative;
    width: 80px
}

.lds-ring div {
    animation: lds-ring 1.2s cubic-bezier(.5, 0, .5, 1) infinite;
    border: 8px solid transparent;
    border-radius: 50%;
    border-top-color: #fff;
    box-sizing: border-box;
    display: block;
    height: 64px;
    margin: 8px;
    position: absolute;
    width: 64px
}

.lds-ring div:first-child {
    animation-delay: -.45s
}

.lds-ring div:nth-child(2) {
    animation-delay: -.3s
}

.lds-ring div:nth-child(3) {
    animation-delay: -.15s
}

@keyframes lds-ring {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

::-webkit-scrollbar {
    width: 4px
}

::-webkit-scrollbar-track {
    border-radius: 10px;
    box-shadow: inset 0 0 1px grey
}

::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 10px
}

::-webkit-scrollbar-thumb:hover {
    background: #ddd
}

html>body {
    min-height: 100vh;
    overflow-x: hidden
}

html>body ::-webkit-scrollbar {
    width: 2px
}

.navbar>.container {
    padding-right: 4.5rem !important
}

.navbar-horizontal .navbar-nav .nav-link {
    color: #212529;
    font-size: medium
}

@media (min-width:992px) {
    .navbar-horizontal .navbar-nav .nav-link i {
        margin-right: 0
    }
}

.navbar-horizontal .navbar-nav .nav-link .text-danger {
    color: #dc3545 !important
}

.lw-terms-and-conditions-page strong {
    font-weight: 600
}

.form-input {
    border: 1px solid #c7c7c7;
    padding: 10px 8px
}

.bg-gradient-primary {
    background: linear-gradient(87deg, #249b4b, #cfffd1) !important
}

.bg-default {
    background-color: #283244 !important
}

.fill-default {
    fill: #186fdf
}

label.custom-control-label {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

div.lw-validation-error,
label.error,
label.lw-validation-error {
    color: #fb6340;
    padding: 10px 0;
    width: 100%
}

.text-left div.lw-validation-error,
.text-left label.error,
.text-left label.lw-validation-error {
    text-align: left
}

label.lw-validation-error {
    padding: 10px 0 0
}

.form-group+div.lw-validation-error {
    padding: 0 0 10px 18px
}

.btn {
    border-radius: 4px;
    box-shadow: none;
    font-weight: 400;
    margin-bottom: 4px
}

.btn.lw-btn-breakable {
    white-space: normal;
    word-break: break-word
}

.btn:hover {
    box-shadow: none;
    filter: saturate(1.3)
}

.btn:not(:last-child) {
    margin-right: 0
}

.input-group .btn {
    margin-bottom: 0;
    padding: .825rem 1.25rem
}

.btn.btn-primary {
    background-color: #2bac32;
    border-color: #119242;
    color: #fff
}

.btn.btn-primary:not(:disabled):not(.disabled).active,
.btn.btn-primary:not(:disabled):not(.disabled):active,
.show>.btn.btn-primary.dropdown-toggle {
    background-color: #119242
}

.btn.btn-primary[data-toggle=modal]:not(:disabled):not(.disabled).active,
.btn.btn-primary[data-toggle=modal]:not(:disabled):not(.disabled):active,
.show>.btn.btn-primary[data-toggle=modal].dropdown-toggle {
    background-color: #eaedef;
    border-color: #b3b3b3;
    color: #0a0a0a
}

.page-item.active .page-link {
    background-color: #5cb666;
    border-color: #53b262;
    color: #fff
}

.display-2 {
    font-weight: 500
}

.form-control {
    background-clip: unset;
    border: 1px solid #cad1d7;
    color: #636f8c;
    font-size: .975rem;
    height: calc(3rem + 2px)
}

.form-control,
.input-group,
.selectize-input {
    box-shadow: 0 1px 3px rgba(50, 50, 93, .15), 0 1px 0 rgba(0, 0, 0, .02);
    transition: box-shadow .15s ease
}

.form-control:focus,
.input-group:focus,
.selectize-input:focus {
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08)
}

.selectize-input.not-full>input,
.selectize-input.not-full>input[type=select-one] {
    width: 100% !important
}

.has-danger:after {
    display: none !important
}

.form-group {
    margin-bottom: .5rem;
    margin-top: .9rem
}

label.form-control-label {
    font-weight: 400
}

.dataTables_wrapper .table th {
    font-weight: 500
}

.dataTables_wrapper .table td,
.dataTables_wrapper .table th {
    font-size: 1rem
}

.dataTables_wrapper .table td .btn-group-sm>.btn,
.dataTables_wrapper .table td .btn-sm,
.dataTables_wrapper .table th .btn-group-sm>.btn,
.dataTables_wrapper .table th .btn-sm {
    font-size: .85rem
}

.dataTables_wrapper table.dataTable.table thead td,
.dataTables_wrapper table.dataTable.table thead th,
.dataTables_wrapper table.dataTable.table.no-footer {
    border-bottom: none
}

.dataTables_wrapper table.dataTable tbody td,
.dataTables_wrapper table.dataTable tbody th {
    padding: 12px
}

.dataTables_wrapper table.dataTable tbody td .avatar.avatar-sm.rounded-circle img,
.dataTables_wrapper table.dataTable tbody th .avatar.avatar-sm.rounded-circle img {
    height: 36px;
    -o-object-fit: cover;
    object-fit: cover;
    width: 36px
}

.dataTables_wrapper .card .table td,
.dataTables_wrapper .card .table th {
    padding: 1rem 1.5rem
}

.dataTables_wrapper select.custom-select {
    padding-left: 10px;
    padding-right: 20px
}

.dataTables_wrapper .page-item .page-link,
.dataTables_wrapper .page-item span {
    border-radius: 4px !important;
    height: 36px;
    margin: 0 3px;
    padding: 3px 14px;
    width: auto
}

.dataTables_wrapper .form-control {
    height: calc(2rem + 2px)
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0
}

.modal {
    transition: none
}

.modal-backdrop.show {
    opacity: .4
}

.modal .modal-body {
    background-color: #f6f8fa
}

.modal-open .modal {
    -webkit-backdrop-filter: blur(4px) grayscale(.9);
    backdrop-filter: blur(4px) grayscale(.9)
}

.modal-open .modal.lw-has-form .lw-form .lw-form-modal-body {
    padding: 1.5rem
}

.modal-open .modal.lw-has-form .modal-body,
.modal-open .modal.lw-has-form .modal-footer {
    border-bottom-left-radius: .4375rem;
    border-bottom-right-radius: .4375rem
}

.modal-open .modal.lw-has-form .modal-footer {
    background-color: #fff
}

.modal-open .modal .modal-body .modal-header {
    background-color: #fff;
    border-radius: 0;
    margin: -23px -24px 16px
}

.modal-open .modal .modal-body .modal-header .modal-title {
    font-size: 1rem
}

.btn-secondary {
    border-color: #e1e2e2
}

.lw-form:not([data-show-processing=false]) .lw-form-processing *,
.lw-form:not([data-show-processing=false]).lw-form-processing * {
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.lw-form:not([data-show-processing=false]) .lw-form-processing .lw-form-overlay,
.lw-form:not([data-show-processing=false]).lw-form-processing .lw-form-overlay {
    -webkit-backdrop-filter: blur(2px) grayscale(1);
    backdrop-filter: blur(2px) grayscale(1);
    background-color: #fff;
    display: block;
    height: 100%;
    left: 0;
    opacity: .2;
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 100%
}

.lw-form:not([data-show-processing=false]).has-danger .form-control::-moz-placeholder {
    color: #c0c7cc
}

.lw-form:not([data-show-processing=false]).has-danger .form-control::placeholder {
    color: #c0c7cc
}

.input-group.input-group-alternative {
    border: 1px solid #cad1d7;
    box-shadow: none
}

.focused .input-group {
    box-shadow: none !important
}

.swal2-container {
    -webkit-backdrop-filter: blur(2px) grayscale(1);
    backdrop-filter: blur(2px) grayscale(1)
}

.selectize-input {
    padding: 14px 12px
}

.selectize-dropdown,
.selectize-dropdown.form-control {
    position: relative;
    top: 0 !important
}

.lw-form-in-process {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.lw-form-in-process * {
    pointer-events: none
}

.lw-form-in-process .lw-spinner-box {
    align-items: center;
    align-self: center;
    background-color: rgba(106, 119, 132, .4);
    border-radius: 8px;
    color: #fff;
    display: flex;
    padding: 10px;
    position: absolute;
    text-align: center;
    vertical-align: middle;
    width: 100px;
    z-index: 1
}

.lw-form-in-process .lw-spinner-box~* {
    filter: blur(1px) grayscale(.5);
    -webkit-filter: blur(1px) grayscale(.5)
}

.lw-form-in-process .lw-spinner-box .spinner-border {
    margin-bottom: 4px
}

.lw-form-in-process .lw-spinner-box small {
    display: block
}

fieldset {
    background-color: hsla(0, 0%, 100%, .85);
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: 4px;
    box-shadow: 0 0 1rem 0 rgba(136, 152, 170, .15);
    margin-top: 20px;
    padding: 16px
}

fieldset legend {
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: 8px;
    color: #5f72e4;
    font-size: 1.1rem;
    padding: 8px 12px;
    width: auto
}

#lwConversionChatContainer .card,
#lwTemplateStructureContainer .card,
.lw-template-structure-form fieldset .card {
    box-shadow: none
}

.card-body fieldset legend {
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: 8px;
    padding: 8px 12px
}

input::-webkit-inner-spin-button,
input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

input {
    border-color: rgba(50, 151, 211, .25)
}

input[type=number] {
    -moz-appearance: textfield
}

.lw-page-title {
    color: #2bac32
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 400
}

.navbar-brand-img {
    min-height: 50px
}

@media (min-width:768px) {
    .main-content .container-fluid {
        padding-left: 20px !important;
        padding-right: 20px !important
    }

    .navbar-vertical.navbar-expand-md .navbar-nav .nav-link {
        padding: .95rem 1.5rem
    }

    .navbar-nav .lw-expandable-nav .nav .nav-item .nav-link {
        padding: .6rem 1.5rem .6rem 2.75rem
    }

    .navbar-vertical.navbar-expand-md .navbar-brand-img {
        -o-object-fit: contain;
        object-fit: contain
    }
}

@media (max-width:768px) {
    .navbar-collapse .collapse-brand img {
        height: 70px;
        -o-object-fit: contain;
        object-fit: contain;
        width: auto
    }

    .card-body {
        padding: .5rem
    }

    .input-group {
        padding: 0 12px 20px
    }

    .input-group input[type=color] {
        height: 50px
    }

    .input-group .input-group-text {
        background: transparent;
        border: none
    }

    .input-group .form-control {
        padding: .625rem .75rem !important
    }

    .input-group * {
        border-radius: 4px;
        border-radius: .375rem !important;
        display: block;
        margin: 8px 0 0;
        width: 100%
    }

    .btn.lw-btn-block-mobile,
    .lw-btn-block-mobile {
        margin-bottom: 8px;
        width: 100%
    }

    .btn-group {
        display: block;
        width: 100%
    }

    .btn-group>:not(.dropdown-menu) {
        border-radius: 0 !important;
        display: block;
        width: 100%
    }

    .btn-group>:not(.dropdown-menu):first-child {
        border-top-left-radius: .375rem !important;
        border-top-right-radius: .375rem !important
    }

    .btn-group>:not(.dropdown-menu):last-child {
        border-bottom-left-radius: .375rem !important;
        border-bottom-right-radius: .375rem !important
    }

    .nav-tabs {
        margin-bottom: 20px
    }

    .nav-tabs .nav-item {
        width: 100%
    }

    .nav-tabs .nav-item .nav-link {
        border-radius: 0
    }

    .nav-tabs .nav-item:first-child .nav-link {
        border-top-left-radius: .375rem;
        border-top-right-radius: .375rem
    }

    .nav-tabs .nav-item:last-child .nav-link {
        border-bottom-left-radius: .375rem;
        border-bottom-right-radius: .375rem
    }
}

.navbar-vertical .navbar-nav .nav-link {
    font-size: 1rem
}

.navbar-light .navbar-nav .nav-link {
    color: #000
}

.text-primary {
    color: #5db666 !important
}

a.text-primary:focus,
a.text-primary:hover {
    color: #139343 !important
}

.navbar-light .navbar-nav .active>.nav-link,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .show>.nav-link {
    color: #139343
}

#lwUploadLogo {
    width: 300px
}

#lwUploadFavicon {
    width: 100px
}

.lw-disabled-block-content {
    cursor: not-allowed;
    display: block;
    filter: blur(2px) grayscale(.4);
    opacity: .6;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

[x-cloak] {
    display: none !important
}

.lw-stamp-container {
    -webkit-user-drag: none;
    -moz-window-dragging: none;
    height: 150px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.lw-ws-pre-line {
    white-space: pre-line
}

@media print {
    .btn.lw-whatsapp-btn {
        display: none
    }

    .card {
        page-break-before: always
    }
}

.lw-logo-on-order-page {
    max-height: 200px;
    max-width: 90%;
    min-height: 100px;
    -o-object-fit: contain;
    object-fit: contain
}

.lw-bg-blue-gray {
    background-color: #636f7b
}

.navbar-horizontal .navbar-brand {
    text-align: center
}

.navbar-horizontal .navbar-brand img {
    height: auto;
    max-height: 90px;
    max-width: 100%;
    min-height: 60px;
    -o-object-fit: contain;
    object-fit: contain
}

.lw-form-card-box {
    -webkit-backdrop-filter: blur(4px) grayscale(.6);
    backdrop-filter: blur(4px) grayscale(.6);
    background-color: rgba(0, 0, 0, .7) !important
}

@media (min-width:767px) {
    .lw-form-card-box {
        margin-top: 6vh
    }
}

.lw-form-card-box .btn-google {
    background-color: #ea4335;
    color: #fff
}

.lw-form-card-box .btn-facebook {
    background-color: #3b5998;
    color: #fff
}

.btn-group-lg>.btn,
.btn-lg {
    font-size: 1.2rem
}

.main-content .navbar-top {
    -webkit-backdrop-filter: blur(4px) grayscale(.6);
    backdrop-filter: blur(4px) grayscale(.6);
    background-color: #2bac32
}

.lw-guest-page .main-content .navbar-top {
    background-color: #fff
}

.main-content-has-bg:before {
    background-position: top;
    background-repeat: no-repeat;
    background-size: cover;
    content: "";
    display: block;
    filter: blur(4px) grayscale(.5);
    left: 0;
    min-height: 100vh;
    min-width: 100vw;
    position: fixed;
    top: 0
}

.card.card-stats {
    min-height: 168px
}

.card.card-stats .h2 {
    font-size: 2.4em
}

.card.card-stats .card-body {
    border-radius: 8px;
    padding: 1rem 1.5rem
}

.card {
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    background-color: hsla(0, 0%, 100%, .8);
    border: 1px solid rgba(0, 0, 0, .15);
    box-shadow: 4px 4px 4px 1px rgba(0, 0, 0, .15)
}

html>body {
    background: #f8f6f3 url(../../imgs/wa-message-bg-faded.png) repeat;
    font-family: IBM Plex Sans, sans-serif
}

nav.lw-breadcrumb-container {
    background-color: #40474f;
    border-radius: 4px;
    display: block;
    font-size: 1.2rem;
    padding: 20px 0 10px 20px
}

.nav-tabs {
    border-bottom: 0;
    margin-left: 20px;
    margin-top: 20px
}

.nav-tabs .nav-link {
    background-color: hsla(0, 0%, 85%, .851);
    border: 0 !important;
    color: #8d8d8d;
    font-size: 1.2rem;
    padding: 10px 1.75rem
}

.nav-tabs .nav-link.active {
    background-color: #fff
}

.nav-tabs .nav-link:hover {
    border: 0
}

fieldset.filepond--file-wrapper {
    background-color: initial
}

.filepond--file,
[data-filepond-item-state=processing-complete] .filepond--file,
[data-filepond-item-state=processing-error] .filepond--file {
    color: #fff
}

.filepond--file-info-sub,
.lw-d-none {
    display: none
}

.btn-size {
    width: 220px !important
}

[dir=rtl] .float-left {
    float: right !important
}

[dir=rtl] .float-right {
    float: left !important
}

[dir=rtl] .text-left {
    text-align: right !important
}

[dir=rtl] .text-right {
    text-align: left !important
}

[dir=rtl] .modal-header .close {
    display: contents;
    float: left;
    margin: 0
}

[dir=rtl] .sidebar {
    padding-right: 0
}

[dir=rtl] .navbar,
[dir=rtl] .sidebar-dark .sidebar-brand {
    border-radius: 0
}

[dir=rtl] .lw-lang-direction-ltr input,
[dir=rtl] .lw-lang-direction-ltr textarea,
[dir=rtl] .lw-original-text-line {
    direction: ltr;
    text-align: left
}

[dir=rtl] body {
    text-align: right
}

[dir=rtl] body .lw-icon-btn {
    margin-left: 10px
}

[dir=rtl] body .dropdown-menu,
[dir=rtl] body input {
    text-align: right
}

[dir=rtl] body .dropdown-menu .dropdown-item,
[dir=rtl] body input .dropdown-item {
    padding-right: 4px
}

[dir=rtl] body .dropdown-menu .dropdown-item .dropdown-list-image,
[dir=rtl] body .dropdown-menu .dropdown-item>div,
[dir=rtl] body input .dropdown-item .dropdown-list-image,
[dir=rtl] body input .dropdown-item>div {
    margin-left: 8px
}

[dir=rtl] body .input-group>* {
    border-radius: 0
}

[dir=rtl] body .input-group>:last-child {
    border-bottom-left-radius: .35rem;
    border-bottom-right-radius: 0;
    border-top-left-radius: .35rem;
    border-top-right-radius: 0
}

[dir=rtl] body .input-group>:first-child {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: .35rem;
    border-top-left-radius: 0;
    border-top-right-radius: .35rem
}

[dir=rtl] body .input-group>.input-group-append,
[dir=rtl] body .input-group>.input-group-append :not(:last-child):not(.dropdown-toggle),
[dir=rtl] body .input-group>.input-group-prepend,
[dir=rtl] body .input-group>.input-group-prepend :not(:last-child):not(.dropdown-toggle) {
    border-radius: 0
}

[dir=rtl] body .input-group>.input-group-append:last-child>*,
[dir=rtl] body .input-group>.input-group-prepend:last-child>* {
    border-bottom-left-radius: .35rem;
    border-bottom-right-radius: 0;
    border-top-left-radius: .35rem;
    border-top-right-radius: 0
}

[dir=rtl] body .input-group>.input-group-append:first-child>*,
[dir=rtl] body .input-group>.input-group-prepend:first-child>* {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: .35rem;
    border-top-left-radius: 0;
    border-top-right-radius: .35rem
}

[dir=rtl] body .page-item:last-child .page-link {
    border-bottom-left-radius: .35rem;
    border-bottom-right-radius: 0;
    border-top-left-radius: .35rem;
    border-top-right-radius: 0
}

[dir=rtl] body .page-item:first-child .page-link {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: .35rem;
    border-top-left-radius: 0;
    border-top-right-radius: .35rem
}

[dir=rtl] body .btn-group>.btn-group:not(:last-child)>.btn,
[dir=rtl] body .btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: .2rem;
    border-top-left-radius: 0;
    border-top-right-radius: .2rem
}

[dir=rtl] body .btn-group>.btn-group:not(:first-child)>.btn,
[dir=rtl] body .btn-group>.btn:not(:first-child):not(.dropdown-toggle) {
    border-bottom-left-radius: .2rem;
    border-bottom-right-radius: 0;
    border-top-left-radius: .2rem;
    border-top-right-radius: 0
}

[dir=rtl] body .btn-group,
[dir=rtl] body .navbar-nav {
    margin-left: 0;
    margin-right: auto
}

[dir=rtl] body .btn-group .nav-item .nav-link,
[dir=rtl] body .navbar-nav .nav-item .nav-link {
    text-align: right
}

[dir=rtl] body .btn-group .dropdown-menu-right,
[dir=rtl] body .navbar-nav .dropdown-menu-right {
    left: 0;
    right: auto;
    transform: none !important
}

.lw-whatsapp-preview-container {
    background: #e5ddd5;
    font-size: 13.6px;
    max-width: 400px;
    overflow: hidden;
    padding: 20px;
    position: relative
}

.lw-whatsapp-preview-container .lw-whatsapp-preview-bg {
    height: auto;
    left: 0;
    opacity: .2;
    position: absolute;
    top: 0;
    width: 100%
}

.lw-whatsapp-header-placeholder {
    align-content: center;
    background-color: #ccd0d5;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    max-height: 200px;
    text-align: center
}

.lw-whatsapp-header-placeholder .lw-whatsapp-header-video {
    min-width: 100%
}

.lw-whatsapp-header-placeholder .lw-whatsapp-header-image {
    max-height: 200px;
    min-width: 80%;
    -o-object-fit: contain;
    object-fit: contain
}

.lw-whatsapp-preview .lw-whatsapp-header-placeholder,
.lw-whatsapp-preview a.lw-wa-message-document-link {
    align-content: center;
    background-color: #ccd0d5;
    border-radius: 6px;
    display: flex;
    height: 150px;
    justify-content: center;
    text-align: center;
    width: 100%
}

.lw-whatsapp-preview .lw-whatsapp-header-placeholder i.fa,
.lw-whatsapp-preview a.lw-wa-message-document-link i.fa {
    align-self: center;
    margin: auto
}

.lw-whatsapp-preview>.card {
    border-top-left-radius: 0;
    padding: 2px
}

.lw-whatsapp-preview>.card:after {
    border-color: transparent #fff transparent transparent;
    border-style: solid;
    border-width: 0 10px 10px 0;
    content: "";
    height: 0;
    left: -10px;
    position: absolute;
    top: 0;
    width: 0
}

.lw-whatsapp-preview .lw-whatsapp-body {
    padding: 16px
}

.lw-whatsapp-preview .lw-whatsapp-body>div {
    white-space: pre-line
}

.lw-whatsapp-preview .lw-whatsapp-footer {
    padding: 0 16px 12px
}

.lw-whatsapp-preview .lw-whatsapp-buttons {
    color: #00a5f4;
    padding: 0;
    text-align: center
}

.lw-configured-badge {
    display: inline-block;
    font-size: 1.6em;
    padding-bottom: 3px
}

.navbar-horizontal .navbar-nav .nav-link {
    font-weight: 500;
    padding-right: .4rem
}

@media (min-width:1400px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl,
    .container-xxl {
        max-width: 1320px
    }
}

.lw-error-page-block {
    -webkit-backdrop-filter: blur(1px);
    backdrop-filter: blur(1px);
    background: hsla(0, 0%, 100%, .7);
    border-radius: 50%;
    display: block;
    height: 100%;
    margin-top: 20%;
    padding: 10%;
    width: 100%
}

.dropdown-item.active,
.dropdown-item:active,
div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm {
    background-color: #2aac32;
    color: #fff;
    text-decoration: none
}

div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm:focus {
    box-shadow: none !important
}

.card .table td,
.card .table th {
    padding-left: .7rem
}

.lw-whatsapp-template-create-preview {
    position: sticky;
    top: 10vh
}

.lw-sidebar-logo-small {
    display: none
}

.lw-sidebar-container {
    color: #fff
}

@media (min-width:768px) {
    .lw-minimized-menu .navbar-vertical.navbar-expand-md.fixed-left+.main-content {
        margin-left: 60px
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md.fixed-right+.main-content {
        margin-right: 60px;
        overflow-x: hidden
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md {
        max-width: 250px;
        overflow-x: hidden;
        transition: width .15s ease;
        width: 60px;
        z-index: 2
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md>.container-fluid {
        width: 200px
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md>.container-fluid .nav .nav-item .nav-link {
        padding-left: 1.5rem;
        transition: padding-left .15s ease
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md>.container-fluid .lw-sidebar-logo-normal {
        display: none
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md>.container-fluid .lw-sidebar-logo-small {
        display: inline-block;
        margin-left: -20px
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md:focus-within,
    .lw-minimized-menu .navbar-vertical.navbar-expand-md:hover {
        width: 248px
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md:focus-within .nav .nav-item .nav-link,
    .lw-minimized-menu .navbar-vertical.navbar-expand-md:hover .nav .nav-item .nav-link {
        padding-left: 2.75rem
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md:focus-within .lw-sidebar-logo-normal,
    .lw-minimized-menu .navbar-vertical.navbar-expand-md:hover .lw-sidebar-logo-normal {
        display: inline-block
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md:focus-within .lw-sidebar-logo-small,
    .lw-minimized-menu .navbar-vertical.navbar-expand-md:hover .lw-sidebar-logo-small {
        display: none
    }

    .lw-minimized-menu .navbar-vertical.navbar-expand-md .navbar-nav .nav-link.active:before {
        border-left: 2px solid #2aac32
    }
}

.lw-qr-image {
    height: 160px
}

.lw-flow-builder-container-holder {
    padding-bottom: 20px
}

.lw-flow-builder-container {
    border: 2px dotted #e5e7eb;
    background: #fafbfc;
    height: 3000px;
    width: 3000px
}

.lw-flow-builder-container .flowchart-operator {
    min-width: 280px;
    width: auto;
}

.lw-flow-builder-container:active {
    cursor: grabbing !important
}

.flowchart-operator-body {
    min-height: 80px
}

.flowchart-operator-inputs .flowchart-operator-connector {
    display: block;
    padding-bottom: 10px;
    padding-top: 0
}

.flowchart-operator-inputs .flowchart-operator-connector:hover .flowchart-operator-connector-arrow:hover {
    border-left: 10px solid #c91f1f
}

.lw-business-profile-image {
    height: 150px;
    margin-bottom: 8px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

.lw-page-description {
    word-wrap: break-word;
    white-space: pre-line
}

.lw-page-dropdown {
    background-color: transparent
}

.lw-page-dropdown button {
    background-color: #fff;
    box-shadow: none !important
}

.lw-page-dropdown button:hover {
    transform: translateY(0)
}

.lw-page-dropdown .dropdown-menu {
    box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15) !important;
    min-width: 10rem
}

textarea.form-control::-webkit-scrollbar {
    width: 10px
}

.pay-box-padding {
    padding: 5rem
}

.lw-white-space-normal {
    white-space: normal
}

#paypal-button-container {
    margin: 0 auto;
    max-width: 100%;
    width: 300px
}