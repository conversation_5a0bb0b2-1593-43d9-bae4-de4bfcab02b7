<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class SSOLogAnalyzer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sso:logs 
                            {action=view : Action to perform (view, stats, projects, users, errors)}
                            {--date= : Specific date to analyze (Y-m-d format)}
                            {--lines=50 : Number of lines to show}
                            {--project= : Filter by specific project}
                            {--user= : Filter by specific user email}
                            {--endpoint= : Filter by specific endpoint}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze SSO API logs to see which APIs are being called from other projects';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $action = $this->argument('action');
        $date = $this->option('date') ?: Carbon::today()->format('Y-m-d');
        
        $logFile = storage_path("logs/sso-api-{$date}.log");
        
        if (!File::exists($logFile)) {
            $this->error("No SSO log file found for date: {$date}");
            $this->info("Looking for file: {$logFile}");
            
            // Show available log files
            $logFiles = File::glob(storage_path('logs/sso-api-*.log'));
            if (!empty($logFiles)) {
                $this->info("\nAvailable SSO log files:");
                foreach ($logFiles as $file) {
                    $this->line("  " . basename($file));
                }
            }
            return 1;
        }

        switch ($action) {
            case 'view':
                $this->viewLogs($logFile);
                break;
            case 'stats':
                $this->showStats($logFile);
                break;
            case 'projects':
                $this->showProjects($logFile);
                break;
            case 'users':
                $this->showUsers($logFile);
                break;
            case 'errors':
                $this->showErrors($logFile);
                break;
            default:
                $this->error("Unknown action: {$action}");
                return 1;
        }

        return 0;
    }

    /**
     * View recent SSO logs
     */
    private function viewLogs($logFile)
    {
        $lines = (int) $this->option('lines');
        $project = $this->option('project');
        $user = $this->option('user');
        $endpoint = $this->option('endpoint');

        $this->info("Showing last {$lines} SSO API log entries:");
        $this->line(str_repeat('=', 80));

        $content = File::get($logFile);
        $logLines = array_filter(explode("\n", $content));
        $logLines = array_slice($logLines, -$lines * 3); // Get more lines to account for multi-line entries

        $filteredEntries = [];
        $currentEntry = '';

        foreach ($logLines as $line) {
            if (preg_match('/^\[\d{4}-\d{2}-\d{2}/', $line)) {
                // Process previous entry
                if ($currentEntry && $this->matchesFilters($currentEntry, $project, $user, $endpoint)) {
                    $filteredEntries[] = $currentEntry;
                }
                $currentEntry = $line;
            } else {
                $currentEntry .= "\n" . $line;
            }
        }

        // Process last entry
        if ($currentEntry && $this->matchesFilters($currentEntry, $project, $user, $endpoint)) {
            $filteredEntries[] = $currentEntry;
        }

        // Show only the requested number of entries
        $filteredEntries = array_slice($filteredEntries, -$lines);

        foreach ($filteredEntries as $entry) {
            $this->displayLogEntry($entry);
            $this->line(str_repeat('-', 80));
        }

        $this->info("\nTotal entries shown: " . count($filteredEntries));
    }

    /**
     * Show statistics
     */
    private function showStats($logFile)
    {
        $content = File::get($logFile);
        $lines = explode("\n", $content);

        $stats = [
            'total_requests' => 0,
            'successful_auth' => 0,
            'failed_auth' => 0,
            'endpoints' => [],
            'projects' => [],
            'users' => [],
            'response_codes' => [],
            'hourly_distribution' => [],
        ];

        foreach ($lines as $line) {
            if (empty($line)) continue;

            if (strpos($line, 'SSO API') !== false) {
                $stats['total_requests']++;

                // Parse JSON context
                if (preg_match('/\{.*\}/', $line, $matches)) {
                    $context = json_decode($matches[0], true);
                    if ($context) {
                        $this->updateStats($stats, $context, $line);
                    }
                }
            }
        }

        $this->displayStats($stats);
    }

    /**
     * Show projects calling the API
     */
    private function showProjects($logFile)
    {
        $content = File::get($logFile);
        $projects = [];

        preg_match_all('/"project_identifier":"([^"]*)"/', $content, $matches);
        foreach ($matches[1] as $project) {
            $projects[$project] = ($projects[$project] ?? 0) + 1;
        }

        arsort($projects);

        $this->info("Projects calling SSO API:");
        $this->line(str_repeat('=', 50));

        $headers = ['Project', 'Requests'];
        $rows = [];
        foreach ($projects as $project => $count) {
            $rows[] = [$project ?: 'Unknown', $count];
        }

        $this->table($headers, $rows);
    }

    /**
     * Show users accessing the API
     */
    private function showUsers($logFile)
    {
        $content = File::get($logFile);
        $users = [];

        preg_match_all('/"email":"([^"]*)"/', $content, $matches);
        foreach ($matches[1] as $email) {
            $users[$email] = ($users[$email] ?? 0) + 1;
        }

        arsort($users);

        $this->info("Users accessing SSO API:");
        $this->line(str_repeat('=', 50));

        $headers = ['User Email', 'Requests'];
        $rows = [];
        foreach (array_slice($users, 0, 20) as $email => $count) {
            $rows[] = [$email, $count];
        }

        $this->table($headers, $rows);
    }

    /**
     * Show errors
     */
    private function showErrors($logFile)
    {
        $content = File::get($logFile);
        $lines = explode("\n", $content);

        $this->info("SSO API Errors:");
        $this->line(str_repeat('=', 80));

        foreach ($lines as $line) {
            if (strpos($line, '.ERROR:') !== false || strpos($line, '.WARNING:') !== false) {
                if (strpos($line, 'SSO') !== false) {
                    $this->displayLogEntry($line);
                    $this->line(str_repeat('-', 80));
                }
            }
        }
    }

    /**
     * Check if log entry matches filters
     */
    private function matchesFilters($entry, $project, $user, $endpoint)
    {
        if ($project && strpos($entry, $project) === false) {
            return false;
        }
        if ($user && strpos($entry, $user) === false) {
            return false;
        }
        if ($endpoint && strpos($entry, $endpoint) === false) {
            return false;
        }
        return true;
    }

    /**
     * Display a single log entry with formatting
     */
    private function displayLogEntry($entry)
    {
        // Extract timestamp
        if (preg_match('/^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $entry, $matches)) {
            $timestamp = $matches[1];
            $this->line("<fg=blue>[{$timestamp}]</fg=blue>");
        }

        // Extract and format JSON context
        if (preg_match('/\{.*\}/', $entry, $matches)) {
            $context = json_decode($matches[0], true);
            if ($context) {
                $this->formatContext($context);
            }
        } else {
            $this->line($entry);
        }
    }

    /**
     * Format context data for display
     */
    private function formatContext($context)
    {
        $important = ['method', 'path', 'project_identifier', 'email', 'auth_status', 'failure_reason', 'response_status', 'execution_time_ms'];
        
        foreach ($important as $key) {
            if (isset($context[$key])) {
                $color = $this->getColorForKey($key, $context[$key]);
                $this->line("  <fg={$color}>{$key}:</fg={$color}> {$context[$key]}");
            }
        }

        if (isset($context['ip_address'])) {
            $this->line("  <fg=yellow>ip:</fg=yellow> {$context['ip_address']}");
        }

        if (isset($context['user_agent'])) {
            $agent = substr($context['user_agent'], 0, 60) . (strlen($context['user_agent']) > 60 ? '...' : '');
            $this->line("  <fg=gray>user_agent:</fg=gray> {$agent}");
        }
    }

    /**
     * Get color for specific keys
     */
    private function getColorForKey($key, $value)
    {
        switch ($key) {
            case 'method':
                return $value === 'GET' ? 'green' : 'cyan';
            case 'auth_status':
                return $value === 'SUCCESS' ? 'green' : 'red';
            case 'response_status':
                return $value < 400 ? 'green' : 'red';
            case 'project_identifier':
                return 'magenta';
            case 'email':
                return 'blue';
            default:
                return 'white';
        }
    }

    /**
     * Update statistics
     */
    private function updateStats(&$stats, $context, $line)
    {
        if (strpos($line, 'Authentication Success') !== false) {
            $stats['successful_auth']++;
        } elseif (strpos($line, 'Authentication Failed') !== false) {
            $stats['failed_auth']++;
        }

        if (isset($context['path'])) {
            $endpoint = $context['path'];
            $stats['endpoints'][$endpoint] = ($stats['endpoints'][$endpoint] ?? 0) + 1;
        }

        if (isset($context['project_identifier'])) {
            $project = $context['project_identifier'];
            $stats['projects'][$project] = ($stats['projects'][$project] ?? 0) + 1;
        }

        if (isset($context['email'])) {
            $user = $context['email'];
            $stats['users'][$user] = ($stats['users'][$user] ?? 0) + 1;
        }

        if (isset($context['response_status'])) {
            $code = $context['response_status'];
            $stats['response_codes'][$code] = ($stats['response_codes'][$code] ?? 0) + 1;
        }

        if (isset($context['timestamp'])) {
            $hour = Carbon::parse($context['timestamp'])->format('H:00');
            $stats['hourly_distribution'][$hour] = ($stats['hourly_distribution'][$hour] ?? 0) + 1;
        }
    }

    /**
     * Display statistics
     */
    private function displayStats($stats)
    {
        $this->info("SSO API Statistics");
        $this->line(str_repeat('=', 50));

        $this->line("Total Requests: {$stats['total_requests']}");
        $this->line("Successful Auth: {$stats['successful_auth']}");
        $this->line("Failed Auth: {$stats['failed_auth']}");
        
        if ($stats['total_requests'] > 0) {
            $successRate = round(($stats['successful_auth'] / $stats['total_requests']) * 100, 2);
            $this->line("Success Rate: {$successRate}%");
        }

        // Top endpoints
        if (!empty($stats['endpoints'])) {
            $this->line("\nTop Endpoints:");
            arsort($stats['endpoints']);
            foreach (array_slice($stats['endpoints'], 0, 10) as $endpoint => $count) {
                $this->line("  {$endpoint}: {$count}");
            }
        }

        // Top projects
        if (!empty($stats['projects'])) {
            $this->line("\nTop Projects:");
            arsort($stats['projects']);
            foreach (array_slice($stats['projects'], 0, 10) as $project => $count) {
                $this->line("  {$project}: {$count}");
            }
        }

        // Response codes
        if (!empty($stats['response_codes'])) {
            $this->line("\nResponse Codes:");
            ksort($stats['response_codes']);
            foreach ($stats['response_codes'] as $code => $count) {
                $color = $code < 400 ? 'green' : 'red';
                $this->line("  <fg={$color}>{$code}</fg={$color}>: {$count}");
            }
        }
    }
}
