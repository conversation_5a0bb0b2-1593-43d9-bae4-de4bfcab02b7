<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class ModuleIntegrationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register module integration services here
        // This can include SSO services, API authentication, etc.
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Bootstrap module integration functionality
        // This can include middleware registration, route bindings, etc.
        
        // Register module access middleware if not already registered
        $this->registerModuleMiddleware();
    }

    /**
     * Register module-specific middleware
     */
    protected function registerModuleMiddleware(): void
    {
        $router = $this->app['router'];
        
        // Register module access middleware if it exists
        if (class_exists(\App\Http\Middleware\CheckModuleAccess::class)) {
            $router->aliasMiddleware('module.access', \App\Http\Middleware\CheckModuleAccess::class);
        }
    }
}
