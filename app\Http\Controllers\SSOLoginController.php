<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Yantrana\Components\Auth\Models\AuthModel;

class SSOLoginController extends Controller
{
    /**
     * Handle SSO login request
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request)
    {
        try {
            $token = $request->input('token');
            
            if (!$token) {
                Log::error('SSO: No token provided');
                return redirect('/')->with('error', 'Invalid SSO token');
            }

            // Decode JWT token
            $payload = JWT::decode($token, new Key(env('SSO_SECRET', 'your-secret-key'), 'HS256'));
            
            Log::info('SSO: JWT decoded', ['email' => $payload->email]);

            // Find user by email using AuthModel
            $user = AuthModel::where('email', $payload->email)->first();
            
            if (!$user) {
                Log::error('SSO: User not found', ['email' => $payload->email]);
                return redirect('/')->with('error', 'User not found');
            }

            Log::info('SSO: User found', [
                '_id' => $user->_id,
                'email' => $user->email,
                'role' => $user->user_roles__id,
                'status' => $user->status ?? 'no status field'
            ]);

            // Check user status like normal login does
            if ($user->status != 1) {
                Log::error('SSO: User account is not active', [
                    'email' => $user->email,
                    'status' => $user->status
                ]);
                return redirect('/')->with('error', 'Your account is not in active mode, please contact administrator.');
            }

            // Use Auth::loginUsingId with the correct primary key (_id)
            Log::info('SSO: Attempting login with ID', [
                '_id' => $user->_id,
                'email' => $user->email,
                'role' => $user->user_roles__id
            ]);

            $loginResult = Auth::loginUsingId($user->_id);
            
            if (!$loginResult) {
                Log::error('SSO: Auth::loginUsingId failed');
                return redirect('/')->with('error', 'Authentication failed');
            }

            Log::info('SSO: Login successful', [
                'auth_user_id' => Auth::id(),
                'auth_check' => Auth::check()
            ]);
            
            // Regenerate session for security
            $request->session()->regenerate();
            
            // Redirect based on role using the same logic as normal login
            if (hasSystemAdminAccess()) {
                Log::info('SSO: Redirecting to system admin console');
                return redirect()->route('central.console');
            } elseif (hasCentralAccess()) {
                Log::info('SSO: Redirecting to central console');
                return redirect()->route('central.console');
            } else {
                Log::info('SSO: Redirecting to vendor console');
                return redirect()->route('vendor.console');
            }

        } catch (\Exception $e) {
            Log::error('SSO Login Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return redirect('/')->with('error', 'SSO login failed: ' . $e->getMessage());
        }
    }
}
