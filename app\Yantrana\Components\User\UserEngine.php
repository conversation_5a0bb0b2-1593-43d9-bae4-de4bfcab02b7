<?php

/**
 * UserEngine.php - Main component file
 *
 * This file is part of the User component.
 *-----------------------------------------------------------------------------*/

namespace App\Yantrana\Components\User;

use Illuminate\Validation\Rule;
use App\Yantrana\Base\BaseEngine;
use App\Yantrana\Components\User\Repositories\UserRepository;
use App\Yantrana\Components\User\Interfaces\UserEngineInterface;
use App\Yantrana\Components\Auth\Repositories\AuthRepository;
use App\Yantrana\Components\Auth\Models\AuthModel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UserEngine extends BaseEngine implements UserEngineInterface
{
    /**
     * @var UserRepository - User Repository
     */
    protected $userRepository;

    /**
     * @var AuthRepository - Auth Repository
     */
    protected $authRepository;

    /**
     * Constructor
     *
     * @param  UserRepository  $userRepository  - User Repository
     * @param  AuthRepository  $authRepository  - Auth Repository
     * @return void
     *-----------------------------------------------------------------------*/
    public function __construct(UserRepository $userRepository, AuthRepository $authRepository)
    {
        $this->userRepository = $userRepository;
        $this->authRepository = $authRepository;
    }

    /**
     * Process profile update request
     *
     * @param  array  $requestData
     * @return array|mixed
     */
    public function processUpdateProfile($requestData)
    {
        //fetch active user by email
        if ($this->userRepository->updateLoggedInUserProfile($requestData)) {
            return $this->engineResponse(21, [
                'messageType' => 'success',
                'reloadPage' => true,
            ], __tr('Your Profile has been updated successfully'));
        }

        return $this->engineResponse(14, null, __tr('Nothing to update'));
    }


    /**
      * User datatable source
      *
      * @return  array
      *---------------------------------------------------------------- */
    public function prepareUserDataTableSource()
    {
        $userCollection = $this->userRepository->fetchUserDataTableSource();
      
        // required columns for DataTables
        $requireColumns = [
            '_id',
            '_uid',
            'first_name',
            'last_name',
            'username',
            'email',
            'mobile_number',
            'status'=>function ($data) {
                if($data['status']==0){
                 return 'Inactive';
                }else{
                    return configItem('status_codes', $data['status']);
                }
            },
            'user_roles__id',
            'user_role' => function ($row) {
                return $row['role']['title'];
            },
            'created_at' => function ($row) {
                return formatDate($row['created_at']);
            },
        ];
        // prepare data for the DataTables
        return $this->dataTableResponse($userCollection, $requireColumns);
    }


    /**
      * User delete process
      *
      * @param  mix $userIdOrUid
      *
      * @return  array
      *---------------------------------------------------------------- */

    public function processUserDelete($userIdOrUid)
    {
        // fetch the record
        $user = $this->userRepository->fetchIt($userIdOrUid);
        // check if the record found
        if (__isEmpty($user)) {
            // if not found
            return $this->engineResponse(18, null, __tr('User not found'));
        }
        $vendorId = getVendorId();
        // check if the user belongs to the current vendor
        if(!$this->userRepository->isVendorUser($user->_id, $vendorId)) {
            return $this->engineFailedResponse([], __tr('Invalid user'));
        }
        // ask to delete the record
        if ($this->userRepository->deleteIt($user)) {
            // if successful
            return $this->engineResponse(1, null, __tr('User deleted successfully'));
        }
        // if failed to delete
        return $this->engineResponse(2, null, __tr('Failed to delete User'));
    }

    /**
      * Process logout as for Team Member
      *
      *
      * @return  EngineResponse
      *---------------------------------------------------------------- */

    public function processLogoutAs()
    {
        Auth::logout();
        Auth::loginUsingId(session('loggedByVendor.id'));
        $hasSuperAdminLogin = session('loggedBySuperAdmin');
        session()->forget('loggedByVendor');
        if($hasSuperAdminLogin) {
            session([
                'loggedBySuperAdmin' => $hasSuperAdminLogin
            ]);
        }
        return $this->engineSuccessResponse([
            'show_message' => true,
        ], __tr('Welcome, back to your account.'));
    }
    /**
      * Process login as for Team Member
      *
      * @param  string $userIdOrUid
      *
      * @return  EngineResponse
      *---------------------------------------------------------------- */

    public function processLoginAs($userIdOrUid)
    {
        // demo
        if(isDemo() and isDemoVendorAccount()) {
            return $this->engineFailedResponse([], __tr('Functionality is disabled for demo'));
        }

        $vendorId = getVendorId();
        $user = $this->userRepository->fetchIt($userIdOrUid);
        // check if the user belongs to the current vendor
        if(!$this->userRepository->isVendorUser($user->_id, $vendorId)) {
            return $this->engineFailedResponse([], __tr('Invalid user'));
        }

        if($user->_id == getUserID()) {
            return $this->engineFailedResponse([], __tr('You can not logged in to your own account.'));
        }
        session([
            'loggedByVendor' => [
                'id' => getUserID(),
                'name' => getUserAuthInfo('profile.full_name'),
            ]
        ]);
        Auth::loginUsingId($user->_id);
        return $this->engineSuccessResponse([
            'show_message' => true,
        ], __tr('Welcome, you are logged as __userName__ successfully.', [
            '__userName__' => $user->full_name
        ]));
    }
    /**
      * User create
      *
      * @param  array $inputData
      *
      * @return  array
      *---------------------------------------------------------------- */

    public function processUserCreate($inputData)
    {
        $vendorId = getVendorId();
        // check the feature limit
        $vendorPlanDetails = vendorPlanDetails('system_users', $this->userRepository->countVendorUsers($vendorId), $vendorId);
        if (!$vendorPlanDetails['is_limit_available']) {
            return $this->engineResponse(22, null, $vendorPlanDetails['message']);
        }

        $inputData['status'] = 1; // Active
        $inputData['user_roles__id'] = 3; //vendor agent
        $inputData['vendors__id'] = $vendorId;
        $inputData['permissions'] = $inputData['permissions'] ?? [];
        $permissions = [];
        
        // assign permissions
        foreach (getListOfPermissions() as $permissionKey => $permission) {
            if(array_key_exists($permissionKey, $inputData['permissions'])) {
                $permissions[$permissionKey] = 'allow';
            } else {
                $permissions[$permissionKey] = 'deny';
            }
        }
        $inputData['permissions'] = $permissions;

        // Handle module permissions - only allow modules that current user can assign
        $assignableModules = getAssignableModules();
        $requestedModules = $inputData['module_permissions'] ?? [];
        $allowedModules = [];
        
        // Filter requested modules to only include those the current user can assign
        foreach ($requestedModules as $module) {
            if (array_key_exists($module, $assignableModules)) {
                $allowedModules[] = $module;
            }
        }
        
        $inputData['module_permissions'] = $allowedModules;

        $transactionResponse = $this->userRepository->processTransaction(function () use ($inputData) {
            // ask to add record
            if ($newUser = $this->userRepository->storeUser($inputData, true)) {
                return $this->userRepository->transactionResponse(1, ['show_message' => true], __tr('User created'));
            }
            return $this->userRepository->transactionResponse(2, ['show_message' => true], __tr('Failed to create user'));
        });
        return $this->engineResponse($transactionResponse);
    }

    /**
      * User prepare update data
      *
      * @param  mix $userIdOrUid
      *
      * @return  array
      *---------------------------------------------------------------- */

    public function prepareUserUpdateData($userIdOrUid)
    {
        $user = $this->userRepository->with('vendorUserDetails')->fetchIt($userIdOrUid);
        // Check if $user not exist then throw not found
        // exception
        if (__isEmpty($user)) {
            return $this->engineResponse(18, null, __tr('User not found.'));
        }

        $vendorId = getVendorId();
        // check if the user belongs to the current vendor
        if(!$this->userRepository->isVendorUser($user->_id, $vendorId)) {
            return $this->engineFailedResponse([], __tr('Invalid user'));
        }

        return $this->engineResponse(1, $user->toArray());
    }

    /**
      * User process update
      *
      * @param  mixed $userIdOrUid
      * @param  array $inputData
      *
      * @return  array
      *---------------------------------------------------------------- */
    public function processUserUpdate($userIdOrUid, $request)
    {
        $user = $this->userRepository->fetchIt($userIdOrUid);

        // Convert stdClass to array if needed
        $inputData = is_object($request) ? (array) $request : $request;

        // Validate email uniqueness
        $validator = Validator::make($inputData, [
            'email' => Rule::unique('users', 'email')->ignore($user, 'email'),
        ]);

        if ($validator->fails()) {
            return $this->engineResponse(2, null, $validator->errors()->first());
        }
        // Check if $user not exist then throw not found
        // exception
        if (__isEmpty($user)) {
            return $this->engineResponse(18, null, __tr('User not found.'));
        }

        $vendorId = getVendorId();
        // check if the user belongs to the current vendor
        if(!$this->userRepository->isVendorUser($user->_id, $vendorId)) {
            return $this->engineFailedResponse([], __tr('Invalid user'));
        }

        $updateData = [
            'first_name' => $inputData['first_name'],
            'last_name' => $inputData['last_name'],
            'mobile_number' => $inputData['mobile_number'],
            'email' => $inputData['email'],
            'status'=>formSwitchValue($inputData['status']),
        ];
        if($inputData['password']) {
            $updateData['password'] = $inputData['password'];
        }

        $inputData['permissions'] = $inputData['permissions'] ?? [];
        $permissions = [];
        // assign permissions
        foreach (getListOfPermissions() as $permissionKey => $permission) {
            if(array_key_exists($permissionKey, $inputData['permissions'])) {
                $permissions[$permissionKey] = 'allow';
            } else {
                $permissions[$permissionKey] = 'deny';
            }
        }
        $inputData['permissions'] = $permissions;

        // Handle module permissions - only allow modules that current user can assign
        $assignableModules = getAssignableModules();
        $requestedModules = $inputData['module_permissions'] ?? [];
        $allowedModules = [];
        
        // Filter requested modules to only include those the current user can assign
        foreach ($requestedModules as $module) {
            if (array_key_exists($module, $assignableModules)) {
                $allowedModules[] = $module;
            }
        }
        
        $updateData['module_permissions'] = $allowedModules;
        $inputData['vendors__id'] = getVendorId();
        
        $transactionResponse = $this->userRepository->processTransaction(function () use ($user, $updateData, $inputData) {
            // ask to add record
            if ($this->userRepository->updateUser($user, $updateData, $inputData)) {
                return $this->userRepository->transactionResponse(1, ['show_message' => true], __tr('User updated'));
            }
            return $this->userRepository->transactionResponse(14, ['show_message' => true], __tr('No updates'));
        });
        return $this->engineResponse($transactionResponse);
    }

    /**
     * Get list of super admins for DataTables
     *
     * @return array
     */
    public function prepareSuperAdminsList()
    {
        $dataTableConfig = [
            'searchable' => [
                'first_name',
                'last_name',
                'email',
                'mobile_number'
            ]
        ];

        $query = AuthModel::where('user_roles__id', 1); // Super Admin role ID
        
        // If logged in user is a super admin, filter by their company_id
        if (getUserAuthInfo('role_id') === 1) {
            $userCompanyId = getUserAuthInfo('company_id');
            if ($userCompanyId) {
                $query->where('company_id', $userCompanyId);
            }
        }

        $superAdmins = $query->select(
                            '_id',
                            '_uid',
                            'first_name',
                            'last_name',
                            'email',
                            'mobile_number',
                            'status',
                            'created_at',
                            'company_id'
                        )
                        ->dataTables($dataTableConfig)
                        ->toArray();

        // Transform data to match vendor list format
        $superAdmins['data'] = collect($superAdmins['data'])->map(function ($superAdmin) {
            return [
                '_id' => $superAdmin['_id'],
                '_uid' => $superAdmin['_uid'],
                'title' => $superAdmin['first_name'] . ' ' . $superAdmin['last_name'],
                'first_name' => $superAdmin['first_name'],
                'last_name' => $superAdmin['last_name'],
                'email' => $superAdmin['email'],
                'mobile_number' => $superAdmin['mobile_number'],
                'status' => $superAdmin['status'],
                'user_status' => $superAdmin['status'],
                'fullName' => $superAdmin['first_name'] . ' ' . $superAdmin['last_name'],
                'created_at' => formatDate($superAdmin['created_at']),
                'userId' => $superAdmin['_id'],
                'userUId' => $superAdmin['_uid'],
                'company_id' => $superAdmin['company_id']
            ];
        })->toArray();

        return $superAdmins;
    }

    /**
     * Process create super admin request
     *
     * @param array $inputData
     * @return array
     */
    public function processCreateSuperAdmin($inputData)
    {
        // Verify system admin access
        if (!hasSystemAdminAccess()) {
            return $this->engineReaction(2, null, __tr('Unauthorized access'));
        }

        $inputData['status'] = 1; // Active
        $inputData['user_roles__id'] = 1; // Super Admin role
        $inputData['username'] = $inputData['email']; // Set username as email

        $transactionResponse = $this->authRepository->processTransaction(function() use($inputData) {
            // First create the company
            $company = \App\Models\Company::create([
                'name' => $inputData['company_name'],
                'description' => $inputData['company_description'] ?? null,
                'status' => 1,
                '_uid' => (string) \Illuminate\Support\Str::uuid(),
                'module_permissions' => isset($inputData['module_permissions']) ? $inputData['module_permissions'] : []
            ]);

            if (!$company) {
                return $this->authRepository->transactionResponse(2, [
                    'show_message' => true
                ], __tr('Failed to create company.'));
            }

            // Add company_id to user data
            $inputData['company_id'] = $company->_id;

            if ($newSuperAdmin = $this->authRepository->storeUser($inputData)) {
                return $this->authRepository->transactionResponse(1, [
                    'show_message' => true
                ], __tr('Super Admin created successfully with company.'));
            }

            return $this->authRepository->transactionResponse(2, [
                'show_message' => true
            ], __tr('Failed to create Super Admin.'));
        });

        return $this->engineResponse($transactionResponse);
    }

    /**
     * Process login as Super Admin
     *
     * @param string $superAdminUid
     * @return array
     */
    public function processLoginAsSuperAdmin($superAdminUid)
    {
        // Verify system admin access
        if (!hasSystemAdminAccess()) {
            return $this->engineFailedResponse([], __tr('Unauthorized access'));
        }

        // Find the super admin user
        $superAdmin = AuthModel::where([
            '_uid' => $superAdminUid,
            'user_roles__id' => 1 // Super Admin role
        ])->first();

        if (!$superAdmin) {
            return $this->engineFailedResponse([], __tr('Super Admin not found'));
        }

        // Check if super admin is active
        if ($superAdmin->status != 1) {
            return $this->engineFailedResponse([], __tr('Super Admin account is not active'));
        }

        // Store current system admin session
        session([
            'loggedBySystemAdmin' => [
                'id' => getUserID(),
                'name' => getUserAuthInfo('profile.full_name'),
            ]
        ]);

        // Login as super admin
        Auth::loginUsingId($superAdmin->_id);

        return $this->engineSuccessResponse([
            'show_message' => true,
        ], __tr('Welcome, you are logged as __userName__ successfully.', [
            '__userName__' => $superAdmin->first_name . ' ' . $superAdmin->last_name
        ]));
    }

    /**
     * Process logout as Super Admin (return to System Admin)
     *
     * @return array
     */
    public function processSuperAdminLogoutAs()
    {
        // Check if logged by system admin
        if (!session('loggedBySystemAdmin')) {
            return $this->engineFailedResponse([], __tr('Invalid session'));
        }

        Auth::logout();
        Auth::loginUsingId(session('loggedBySystemAdmin.id'));
        session()->forget('loggedBySystemAdmin');

        return $this->engineSuccessResponse([
            'show_message' => true,
        ], __tr('Welcome back to your System Admin account.'));
    }

    /**
     * Prepare Super Admin Update Data
     *
     * @param string $superAdminUid
     * @return array
     */
    public function prepareSuperAdminUpdateData($superAdminUid)
    {
        // Verify system admin access
        if (!hasSystemAdminAccess()) {
            return $this->engineReaction(2, null, __tr('Unauthorized access'));
        }

        $superAdmin = AuthModel::where([
            '_uid' => $superAdminUid,
            'user_roles__id' => 1 // Super Admin role
        ])->first();

        if (!$superAdmin) {
            return $this->engineReaction(18, null, __tr('Super Admin not found.'));
        }

        // If logged in user is a super admin, check if they can edit this super admin
        if (getUserAuthInfo('role_id') === 1) {
            $userCompanyId = getUserAuthInfo('company_id');
            if ($userCompanyId && $superAdmin->company_id !== $userCompanyId) {
                return $this->engineReaction(18, null, __tr('Super Admin not found.'));
            }
        }

        $isDemoMode = isDemo();
        $superAdminData = $superAdmin->toArray();
        
        if ($isDemoMode) {
            $superAdminData['email'] = maskForDemo($superAdminData['email'], 'email');
            $superAdminData['first_name'] = maskForDemo($superAdminData['first_name'], 'first_name');
            $superAdminData['last_name'] = maskForDemo($superAdminData['last_name'], 'last_name');
            $superAdminData['mobile_number'] = maskForDemo($superAdminData['mobile_number'], 'mobile_number');
        }

        // Format data to match vendor format for consistency with frontend
        $superAdminData['title'] = $superAdminData['first_name'] . ' ' . $superAdminData['last_name'];
        $superAdminData['userUId'] = $superAdminData['_uid'];
        $superAdminData['store_status'] = 1; // Always active for super admins

        // Get module permissions from company
        if ($superAdmin->company_id) {
            $company = \App\Models\Company::find($superAdmin->company_id);
            if ($company && $company->module_permissions) {
                $modulePermissions = json_decode($company->module_permissions, true);
                $superAdminData['module_permissions'] = is_array($modulePermissions) ? $modulePermissions : [];
            } else {
                $superAdminData['module_permissions'] = [];
            }
        } else {
            $superAdminData['module_permissions'] = [];
        }

        return $this->engineReaction(1, $superAdminData);
    }

    /**
     * Process Super Admin Update
     *
     * @param array $inputData
     * @return array
     */
    public function processSuperAdminUpdate($inputData)
    {
        // Verify system admin access
        if (!hasSystemAdminAccess()) {
            return $this->engineReaction(2, null, __tr('Unauthorized access'));
        }

        $superAdmin = AuthModel::where([
            '_uid' => $inputData['userIdOrUid'],
            'user_roles__id' => 1 // Super Admin role
        ])->first();

        if (!$superAdmin) {
            return $this->engineReaction(18, null, __tr('Super Admin not found.'));
        }

        // If logged in user is a super admin, check if they can edit this super admin
        if (getUserAuthInfo('role_id') === 1) {
            $userCompanyId = getUserAuthInfo('company_id');
            if ($userCompanyId && $superAdmin->company_id !== $userCompanyId) {
                return $this->engineReaction(18, null, __tr('Super Admin not found.'));
            }
        }

        // Convert status to boolean
        $status = isset($inputData['status']) ? filter_var($inputData['status'], FILTER_VALIDATE_BOOLEAN) : false;

        $updateData = [
            'first_name' => $inputData['first_name'],
            'last_name' => $inputData['last_name'],
            'email' => $inputData['email'],
            'mobile_number' => $inputData['mobile_number'],
            'status' => $status ? 1 : 0, // Convert boolean to 1/0
        ];

        $transactionResponse = $this->authRepository->processTransaction(function () use ($superAdmin, $updateData, $inputData) {
            // Update super admin user data
            if ($this->authRepository->updateIt($superAdmin, $updateData)) {
                // Update company module permissions if provided
                if (isset($inputData['module_permissions']) && $superAdmin->company_id) {
                    $company = \App\Models\Company::find($superAdmin->company_id);
                    if ($company) {
                        $company->update([
                            'module_permissions' => $inputData['module_permissions']
                        ]);
                    }
                }
                
                return $this->authRepository->transactionResponse(1, ['show_message' => true], __tr('Super Admin updated successfully.'));
            }
            return $this->authRepository->transactionResponse(14, ['show_message' => true], __tr('Super Admin not updated.'));
        });

        return $this->engineResponse($transactionResponse);
    }
}
