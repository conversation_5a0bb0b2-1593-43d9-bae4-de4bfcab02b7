<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use App\Yantrana\Components\FacebookService\FacebookServiceEngine;
use App\Yantrana\Components\FacebookChatService\FacebookChatServiceEngine;
use App\Yantrana\Components\FacebookChatService\Services\FacebookChatApiService;
use App\Yantrana\Components\FacebookChatService\Controllers\FacebookChatServiceController;

class FacebookServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register Facebook Service Engine
        $this->app->singleton(FacebookServiceEngine::class, function () {
            return new FacebookServiceEngine();
        });

        // Register Facebook controllers
        $this->app->when(\App\Yantrana\Components\FacebookService\Controllers\FacebookServiceController::class)
            ->needs(FacebookServiceEngine::class)
            ->give(function ($app) {
                return $app->make(FacebookServiceEngine::class);
            });

        // Register Facebook Chat API Service
        $this->app->singleton(FacebookChatApiService::class, function ($app) {
            return new FacebookChatApiService();
        });

        // Register Facebook Chat Service Engine
        $this->app->singleton(FacebookChatServiceEngine::class, function ($app) {
            return new FacebookChatServiceEngine(
                $app->make(FacebookChatApiService::class)
            );
        });

        // Register Facebook Chat Service Controller
        $this->app->singleton(FacebookChatServiceController::class, function ($app) {
            return new FacebookChatServiceController(
                $app->make(FacebookChatServiceEngine::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register Facebook-specific configurations
        $this->registerFacebookConfigurations();

        // Register Facebook event listeners
        $this->registerFacebookEventListeners();

        // Register Facebook middleware
        $this->registerFacebookMiddleware();

        // Register Facebook view composers
        $this->registerFacebookViewComposers();
    }

    /**
     * Register Facebook-specific configurations
     */
    protected function registerFacebookConfigurations(): void
    {
        // Add Facebook media restrictions
        config([
            'media-restrictions.facebook_media' => [
                'image' => [
                    'extensions' => ['jpg', 'jpeg', 'png', 'gif'],
                    'max_size' => 25 * 1024 * 1024, // 25MB
                    'mime_types' => ['image/jpeg', 'image/png', 'image/gif']
                ],
                'video' => [
                    'extensions' => ['mp4', 'mov', 'avi'],
                    'max_size' => 25 * 1024 * 1024, // 25MB
                    'mime_types' => ['video/mp4', 'video/quicktime', 'video/x-msvideo']
                ],
                'document' => [
                    'extensions' => ['pdf', 'doc', 'docx', 'txt'],
                    'max_size' => 25 * 1024 * 1024, // 25MB
                    'mime_types' => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
                ]
            ]
        ]);

        // Add Facebook API configuration
        config([
            'facebook.api_version' => 'v18.0',
            'facebook.graph_url' => 'https://graph.facebook.com',
            'facebook.webhook_fields' => [
                'messages',
                'messaging_postbacks',
                'messaging_optins',
                'message_deliveries',
                'message_reads'
            ]
        ]);
    }

    /**
     * Register Facebook event listeners
     */
    protected function registerFacebookEventListeners(): void
    {
        // Listen for Facebook webhook events
        Event::listen('facebook.message.received', function ($data) {
            // Handle incoming Facebook message
            Log::info('Facebook message received', $data);
        });

        Event::listen('facebook.message.sent', function ($data) {
            // Handle outgoing Facebook message
            Log::info('Facebook message sent', $data);
        });

        Event::listen('facebook.webhook.verified', function ($vendorId) {
            // Handle Facebook webhook verification
            Log::info('Facebook webhook verified for vendor: ' . $vendorId);
        });
    }

    /**
     * Register Facebook middleware
     */
    protected function registerFacebookMiddleware(): void
    {
        // Register Facebook-specific middleware if needed
        $router = $this->app['router'];

        // Middleware to check Facebook configuration
        $router->aliasMiddleware('facebook.configured', function ($request, $next) {
            if (!getVendorSettings('enable_facebook_messaging', false)) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => __tr('Facebook messaging is not enabled')
                    ], 403);
                }
                
                return redirect()->route('vendor.settings.read', ['pageType' => 'facebook-api-setup'])
                    ->with('error', __tr('Facebook messaging is not enabled'));
            }

            return $next($request);
        });

        // Middleware to check Facebook API configuration
        $router->aliasMiddleware('facebook.api.configured', function ($request, $next) {
            $requiredSettings = [
                'facebook_page_access_token',
                'facebook_page_id'
            ];

            foreach ($requiredSettings as $setting) {
                if (empty(getVendorSettings($setting))) {
                    if ($request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => __tr('Facebook API is not properly configured')
                        ], 403);
                    }

                    return redirect()->route('vendor.settings.read', ['pageType' => 'facebook-api-setup'])
                        ->with('error', __tr('Facebook API is not properly configured'));
                }
            }

            return $next($request);
        });

        // Middleware to verify Facebook webhook signature
        $router->aliasMiddleware('facebook.webhook.verify', function ($request, $next) {
            if ($request->isMethod('POST')) {
                $signature = $request->header('X-Hub-Signature-256');
                $appSecret = getVendorSettings('facebook_app_secret');
                
                if (!$signature || !$appSecret) {
                    return response('Forbidden', 403);
                }

                $expectedSignature = 'sha256=' . hash_hmac('sha256', $request->getContent(), $appSecret);
                
                if (!hash_equals($expectedSignature, $signature)) {
                    return response('Forbidden', 403);
                }
            }

            return $next($request);
        });
    }

    /**
     * Register Facebook view composers
     */
    protected function registerFacebookViewComposers(): void
    {
        // Share Facebook configuration status with all views
        view()->composer('*', function ($view) {
            $view->with('facebookEnabled', getVendorSettings('enable_facebook_messaging', false));
            $view->with('facebookConfigured', $this->isFacebookConfigured());
        });

        // Share Facebook stats with dashboard views
        view()->composer(['dashboard.*', 'live-chat.*'], function ($view) {
            if (getVendorSettings('enable_facebook_messaging', false)) {
                $facebookStats = $this->getFacebookStats();
                $view->with('facebookStats', $facebookStats);
            }
        });
    }

    /**
     * Check if Facebook is properly configured
     *
     * @return bool
     */
    protected function isFacebookConfigured(): bool
    {
        $requiredSettings = [
            'facebook_page_id',
            'facebook_page_access_token',
            'facebook_app_id',
            'facebook_app_secret'
        ];

        foreach ($requiredSettings as $setting) {
            if (empty(getVendorSettings($setting))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get Facebook statistics
     *
     * @return array
     */
    protected function getFacebookStats(): array
    {
        try {
            if (!$this->isFacebookConfigured()) {
                return [
                    'total_contacts' => 0,
                    'unread_count' => 0,
                    'configured' => false
                ];
            }

            $facebookEngine = app(FacebookServiceEngine::class);
            $conversationsResult = $facebookEngine->getConversations();

            if ($conversationsResult->success()) {
                $data = $conversationsResult->data();
                $conversations = $data['conversations'] ?? [];

                return [
                    'total_contacts' => count($conversations),
                    'unread_count' => 0, // TODO: Implement unread count logic
                    'configured' => true
                ];
            }

            return [
                'total_contacts' => 0,
                'unread_count' => 0,
                'configured' => true
            ];

        } catch (\Exception $e) {
            Log::error('Error getting Facebook stats: ' . $e->getMessage());

            return [
                'total_contacts' => 0,
                'unread_count' => 0,
                'configured' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get Facebook helper functions
     */
    protected function registerFacebookHelpers(): void
    {
        // Register global helper functions for Facebook
        if (!function_exists('isFacebookConfigured')) {
            function isFacebookConfigured(): bool
            {
                return app(FacebookServiceProvider::class)->isFacebookConfigured();
            }
        }

        if (!function_exists('getFacebookStats')) {
            function getFacebookStats(): array
            {
                return app(FacebookServiceProvider::class)->getFacebookStats();
            }
        }
    }
}
