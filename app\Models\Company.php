<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Yantrana\Base\BaseModel;

class Company extends BaseModel
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'companies';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'status',
        'module_permissions'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'module_permissions' => 'array'
    ];

    /**
     * Available modules that can be assigned to companies
     */
    public static $availableModules = [
        'whatsapp_flows' => 'WhatsApp Flows',
        'whatsapp_orders' => 'WhatsApp Orders',
        'campaigns' => 'Campaigns',
        'templates' => 'Templates',
        'chatbot' => 'Chatbot'
    ];

    /**
     * Get the users for the company.
     */
    public function users()
    {
        return $this->hasMany(User::class, 'company_id', '_id');
    }

    /**
     * Get the super admins for the company.
     */
    public function superAdmins()
    {
        return $this->hasMany(User::class, 'company_id', '_id')
                    ->where('user_roles__id', 1);
    }

    /**
     * Check if a specific module is enabled for this company
     *
     * @param string $module
     * @return bool
     */
    public function hasModule($module)
    {
        if (!$this->module_permissions) {
            return false;
        }
        return in_array($module, $this->module_permissions);
    }

    /**
     * Get list of enabled modules
     *
     * @return array
     */
    public function getEnabledModules()
    {
        if (!$this->module_permissions) {
            return [];
        }
        return array_intersect_key(self::$availableModules, array_flip($this->module_permissions));
    }
}
