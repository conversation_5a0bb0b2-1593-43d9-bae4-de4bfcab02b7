/*
 * jquery.flowchart - CSS definitions
 */

.flowchart-container {
    position: relative;
    overflow: hidden;
}

.flowchart-links-layer, .flowchart-operators-layer, .flowchart-temporary-link-layer {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
}

.flowchart-operators-layer, .flowchart-temporary-link-layer {
    pointer-events: none;
}

.flowchart-temporary-link-layer {
    display: none;
}

.flowchart-link, .flowchart-operator {
    cursor: default;
}

.flowchart-operator-connector {
    position: relative;
    padding-top: 5px;
    padding-bottom: 5px;
}

.flowchart-operator-connector-label {
    font-size: small;
    /* display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 12px 16px !important;
    background: #dbeafe !important;
    border: 1px solid #3b82f6 !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important; */
}

.flowchart-operator-inputs .flowchart-operator-connector-label {
    margin-left: 14px;
}

.flowchart-operator-outputs .flowchart-operator-connector-label {
    text-align: right;
    margin-right: 5px;
}

.flowchart-operator-connector-arrow {
    width: 0px;
    height: 0px;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-left: 10px solid rgb(204, 204, 204);
    position: absolute;
    top: 0px;
}

.flowchart-operator-connector-small-arrow {
    width: 0px;
    height: 0px;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 5px solid transparent; /*rgb(100, 100, 100);*/
    position: absolute;
    top: 5px;
    pointer-events: none;
}

.flowchart-operator-connector:hover .flowchart-operator-connector-arrow {
    border-left: 10px solid rgb(153, 153, 153);
}

.flowchart-operator-inputs .flowchart-operator-connector-arrow {
    left: -1px;
}

.flowchart-operator-outputs .flowchart-operator-connector-arrow {
    right: -10px;
}

.flowchart-operator-inputs .flowchart-operator-connector-small-arrow {
    left: -1px;
}

.flowchart-operator-outputs .flowchart-operator-connector-small-arrow {
    right: -7px;
}

.unselectable {
   -moz-user-select: none;
   -khtml-user-select: none;
   -webkit-user-select: none;

   /*
     Introduced in IE 10.
     See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
   */
   -ms-user-select: none;
   user-select: none;
}


/* Default Operator */

.flowchart-operator {
    position: absolute;
    width: 280px;
    min-width: 280px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    overflow: hidden;
    pointer-events: auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.flowchart-operator.hover {
    border-color: #3b82f6;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.flowchart-operator.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.flowchart-operator-title {
    padding: 12px 16px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    font-weight: 600;
    font-size: 14px;
    border-radius: 12px 12px 0 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.flowchart-operator-title::before {
    /* content: "💬"; */
    font-size: 16px;
    opacity: 0.9;
}

.flowchart-operator.condition .flowchart-operator-title::before {
    content: "❓";
}

.flowchart-operator.goto .flowchart-operator-title::before {
    content: "↗️";
}

.flowchart-operator.start .flowchart-operator-title::before {
    content: "🚀";
}

.flowchart-operator-title-text {
    font-size: 14px;
    color: white;
    font-weight: 600;
    flex: 1;
}

.flowchart-operator .flowchart-operator-title {
    width: 100%;
    padding: 12px 16px;
    font-weight: 600;
    box-sizing: border-box;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    height: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: move;
    border-radius: 12px 12px 0 0;
    border: none;
}

.flowchart-operator .flowchart-operator-inputs-outputs {
    display: table;
    width: 100%;
    margin-top: 5px;
    margin-bottom: 5px;
}

.flowchart-operator .flowchart-operator-inputs, .flowchart-default-operator .flowchart-operator-outputs {
    display: table-cell;
    width: 50%;
}

/*
 * flowchart-vertical
 */

.flowchart-vertical .flowchart-operator-inputs,
.flowchart-vertical .flowchart-operator-outputs {
    position: relative;
    text-align: center;
    display: table;
    width: 100%;
}
.flowchart-vertical .flowchart-operator-connector-set {
    display: table-cell;
}
.flowchart-vertical .flowchart-operator-connector {
    position: relative;
}
.flowchart-vertical .flowchart-operator-connector-label {
    position: relative;
    text-align: center;
    width: 100%;
}
.flowchart-vertical .flowchart-operator-inputs .flowchart-operator-connector-label {
    margin-left: auto;
}
.flowchart-vertical .flowchart-operator-connector-arrow {
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #ccc;
    left: calc(50% - 10px);
}
.flowchart-vertical .flowchart-operator-connector:hover .flowchart-operator-connector-arrow {
    border-left-color: transparent;
    border-top-color: #999;
}
.flowchart-vertical .flowchart-operator-connector-small-arrow {
    border-right: 5px solid transparent;
    top: 2px;
    left: calc(50% - 5px);
}
.flowchart-vertical .flowchart-operator-connector-arrow {
    top: 0px;
}
.flowchart-vertical .flowchart-operator-outputs .flowchart-operator-connector-arrow {
    bottom: -20px;
    top: auto;
}
.flowchart-vertical .flowchart-operator-outputs .flowchart-operator-connector-small-arrow {
    left: calc(50% - 5px);
    bottom: -12px;
    top: auto;
}
.flowchart-vertical .flowchart-link rect {
    display: none;
}

/*
 * flowchart-operator-body
 */
 .flowchart-operator-body {
    padding: 16px;
    background: #f8fafc;
    color: #374151;
    font-size: 13px;
    line-height: 1.6;
    border-radius: 0 0 12px 12px;
    min-height: 80px;
}

.flowchart-operator-body-content {
    margin: 0 0 12px 0;
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
}

.flowchart-operator-body-description {
    margin: 0 0 12px 0;
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
    font-style: italic;
}

/* Different node types */
.flowchart-operator.message .flowchart-operator-title {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.flowchart-operator.condition .flowchart-operator-title {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.flowchart-operator.goto .flowchart-operator-title {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.flowchart-operator.start .flowchart-operator-title {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.flowchart-operator.action .flowchart-operator-title {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.flowchart-operator.input .flowchart-operator-title {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* Connectors */
.flowchart-operator-connector {
    padding: 5px 0;
}

.flowchart-operator-connector-arrow {
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid #cbd5e0;
    transition: all 0.2s ease;
}

.flowchart-operator-inputs .flowchart-operator-connector-arrow {
    left: -1px;
}

.flowchart-operator-outputs .flowchart-operator-connector-arrow {
    right: -8px;
    transform: rotate(180deg);
}

.flowchart-operator-connector:hover .flowchart-operator-connector-arrow {
    border-left-color: #4f46e5;
}

/* Buttons inside nodes */
.flowchart-operator .btn {
    padding: 6px 12px;
    font-size: 11px;
    border-radius: 6px;
    margin: 2px 4px 2px 0;
    transition: all 0.2s ease;
    font-weight: 500;
    border: 1px solid transparent;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.flowchart-operator .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.flowchart-operator .btn-primary,
.flowchart-operator .btn-default {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.flowchart-operator .btn-primary:hover,
.flowchart-operator .btn-default:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.flowchart-operator .btn-danger {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
}

.flowchart-operator .btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.flowchart-operator .btn-light {
    background: #f8fafc;
    border-color: #e5e7eb;
    color: #374151;
}

.flowchart-operator .btn-light:hover {
    background: #f1f5f9;
    border-color: #d1d5db;
}

.flowchart-operator .btn-warning {
    background: #f59e0b;
    border-color: #f59e0b;
    color: white;
}

.flowchart-operator .btn-warning:hover {
    background: #d97706;
    border-color: #d97706;
}

.flowchart-operator .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
}

.flowchart-operator .btn-block {
    width: 100%;
    margin-top: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .flowchart-operator {
        width: 260px !important;
        min-width: 260px !important;
    }

    .flowchart-operator .btn {
        font-size: 10px;
        padding: 4px 8px;
    }
}

@media (max-width: 480px) {
    .flowchart-operator {
        width: 240px !important;
        min-width: 240px !important;
    }
}