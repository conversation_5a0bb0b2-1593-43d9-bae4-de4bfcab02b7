<?php

/**
 * UserRepository.php - Repository file
 *
 * This file is part of the User component.
 *-----------------------------------------------------------------------------*/

namespace App\Yantrana\Components\User\Repositories;

use Illuminate\Support\Facades\Auth;
use App\Yantrana\Base\BaseRepository;
use App\Yantrana\Components\Auth\Models\AuthModel;
use App\Yantrana\Components\Auth\Repositories\AuthRepository;
use App\Yantrana\Components\User\Interfaces\UserRepositoryInterface;
use App\Yantrana\Components\Vendor\Models\VendorUserModel;

class UserRepository extends AuthRepository implements UserRepositoryInterface
{
    /**
     * primary model instance
     *
     * @var object
     */
    protected $primaryModel = AuthModel::class;

    public function updateLoggedInUserProfile($updateData)
    {
        
        $user = Auth::user();
        $dataToUpdate = [
            'first_name' => $updateData['first_name'],
            'last_name' => $updateData['last_name'],
            'mobile_number'=> $updateData['mobile_number'],
        ];
        if ($user->email !== $updateData['email']) {
            $dataToUpdate['email'] = $updateData['email'];
            $dataToUpdate['email_verified_at'] = null;
        }

        return $this->updateIt($user, $dataToUpdate);
    }

    public function updateUserData($userData, $requireColumnsForUser)
    {
        // Check if page updated then return positive response
        if ($userData->modelUpdate($requireColumnsForUser)) {
            return true;
        }

        return false;
    }

    /**
      * Fetch user datatable source
      *
      * @return  mixed
      *---------------------------------------------------------------- */
    public function fetchUserDataTableSource()
    {
        // basic configurations for dataTables data
        $dataTableConfig = [
            // searchable columns
            'searchable' => [
                'first_name',
                'last_name',
                'username',
                'email',
            ]
        ];

        $query = $this->primaryModel::select([
            'users.*',
            'vendor_users.users__id',
            'vendor_users.vendors__id',
            'vendors.company_id'
        ])
        ->leftJoin('vendor_users', 'users._id', '=', 'vendor_users.users__id')
        ->leftJoin('vendors', 'vendor_users.vendors__id', '=', 'vendors._id');

        // If logged in as super admin, filter by company_id
        if (getUserAuthInfo('role_id') === 1) {
            $userCompanyId = getUserAuthInfo('company_id');
            if ($userCompanyId) {
                $query->where('vendors.company_id', $userCompanyId);
            }
        } else {
            // For vendor admin, show only their vendor's users
            $query->where('vendor_users.vendors__id', getVendorId());
        }

        return $query->with('role')
                    ->dataTables($dataTableConfig)
                    ->toArray();
    }

    /**
      * Delete $user record and return response
      *
      * @param  object $inputData
      *
      * @return  mixed
      *---------------------------------------------------------------- */

    public function deleteUser($user)
    {
        // Check if $user deleted
        if ($user->deleteIt()) {
            // if deleted
            return true;
        }
        // if failed to delete
        return false;
    }

    /**
     * Get vendor users count
     *
     * @param int $vendorId
     * @return number
     */
    function countVendorUsers($vendorId) {
        return VendorUserModel::where('vendors__id', $vendorId)->count();
    }
    /**
     * Get vendor active users count
     *
     * @param int $vendorId
     * @return number
     */
    function countVendorsActiveUsers($vendorId) {
        return $this->primaryModel::leftJoin('vendor_users', 'users._id', '=', 'vendor_users.users__id')
        ->where('users.status','=', 1)
        ->where('vendor_users.vendors__id', $vendorId)->count();
    }
    /**
     * check if it is a vendor user
     *
     * @param int $userId
     * @return number
     */
    function isVendorUser($userId, $vendorId = null) {
        $vendorId = $vendorId ?: getVendorId();
        return VendorUserModel::where([
            'vendors__id' => $vendorId,
            'users__id' => $userId,
        ])->count();
    }
    /**
     * Get vendor users who have the Messaging Permission
     *
     * @param int $vendorId
     * @return Eloquent Collection
     */
    function getVendorMessagingUsers($vendorId) {
        // Get vendor admins (role_id = 2) who have direct vendor relationship
        $vendorAdmins = $this->fetchItAll([
            'vendors__id' => $vendorId,
            'user_roles__id' => 2, // Vendor admin role
        ]);

        // Get all vendor agents (role_id = 3) through vendor_users table
        $vendorUserRecords = VendorUserModel::where('vendors__id', $vendorId)->get();
        
        $vendorAgentIds = [];
        foreach ($vendorUserRecords as $vendorUser) {
            // Get the actual user record to check role
            $user = $this->fetchIt($vendorUser->users__id, null, '_id');
            
            // Only process if this is actually a vendor agent (role_id = 3)
            if (!__isEmpty($user) && $user->user_roles__id == 3) {
                // Check if messaging permission is explicitly set to 'allow'
                $permissions = $vendorUser->__data['permissions'] ?? [];
                $messagingPermission = $permissions['messaging'] ?? null;
                
                // Include user if messaging permission is 'allow' OR if permissions are not set (backward compatibility)
                // This provides backward compatibility for users created before permissions were implemented
                if ($messagingPermission === 'allow' || (empty($permissions) || !isset($permissions['messaging']))) {
                    $vendorAgentIds[] = $vendorUser->users__id;
                }
            }
        }

        // Combine both collections
        $allUsers = collect();

        // Add vendor agents with messaging permission
        if(!empty($vendorAgentIds)) {
            $vendorAgents = $this->fetchItAll($vendorAgentIds, null, '_id');
            $allUsers = $allUsers->merge($vendorAgents);
        }

        // Add vendor admins (they have messaging permission by default)
        $allUsers = $allUsers->merge($vendorAdmins);

        // Log for debugging
        \Illuminate\Support\Facades\Log::info('Vendor messaging users query result', [
            'vendor_id' => $vendorId,
            'vendor_admin_count' => $vendorAdmins->count(),
            'vendor_agent_ids_with_messaging' => $vendorAgentIds,
            'total_messaging_users' => $allUsers->count(),
            'messaging_user_uids' => $allUsers->pluck('_uid')->toArray(),
            'vendor_user_records_count' => $vendorUserRecords->count()
        ]);

        return $allUsers;
    }

    /**
     * Get vendor user by UID
     *
     * @param string $userUid
     * @param int $vendorId
     * @return Eloquent Model|null
     */
    function getVendorUserByUid($userUid, $vendorId) {
        // First get the user by UID
        $user = $this->fetchIt(['_uid' => $userUid]);

        if (__isEmpty($user)) {
            return null;
        }

        // Check if this is a vendor admin (role_id = 2) with direct vendor relationship
        if ($user->user_roles__id == 2 && $user->vendors__id == $vendorId) {
            return $user;
        }

        // Check if this is a vendor agent (role_id = 3) through vendor_users table
        $isVendorAgent = VendorUserModel::where([
            'vendors__id' => $vendorId,
            'users__id' => $user->_id,
        ])->exists();

        if ($isVendorAgent) {
            return $user;
        }

        return null;
    }

    /**
     * Ensure user has messaging permission - utility method for fixing permissions
     *
     * @param string $userUid
     * @param int $vendorId
     * @return bool
     */
    function ensureUserHasMessagingPermission($userUid, $vendorId = null) {
        $vendorId = $vendorId ?: getVendorId();
        
        // Get the user
        $user = $this->fetchIt(['_uid' => $userUid]);
        if (__isEmpty($user)) {
            \Illuminate\Support\Facades\Log::error('User not found when ensuring messaging permission', [
                'user_uid' => $userUid,
                'vendor_id' => $vendorId
            ]);
            return false;
        }

        // If user is vendor admin, they have messaging permission by default
        if ($user->user_roles__id == 2 && $user->vendors__id == $vendorId) {
            \Illuminate\Support\Facades\Log::info('Vendor admin has messaging permission by default', [
                'user_uid' => $userUid,
                'vendor_id' => $vendorId
            ]);
            return true;
        }

        // For vendor agents, check and update permissions in vendor_users table
        if ($user->user_roles__id == 3) {
            $vendorUser = VendorUserModel::where([
                'vendors__id' => $vendorId,
                'users__id' => $user->_id,
            ])->first();

            if (!__isEmpty($vendorUser)) {
                $permissions = $vendorUser->__data['permissions'] ?? [];
                
                // If messaging permission is not set or is denied, set it to allow
                if (!isset($permissions['messaging']) || $permissions['messaging'] !== 'allow') {
                    $permissions['messaging'] = 'allow';
                    
                    $vendorUser->__data = array_merge($vendorUser->__data ?? [], [
                        'permissions' => $permissions
                    ]);
                    
                    $vendorUser->save();
                    
                    \Illuminate\Support\Facades\Log::info('Updated messaging permission for user', [
                        'user_uid' => $userUid,
                        'vendor_id' => $vendorId,
                        'updated_permissions' => $permissions
                    ]);
                    
                    return true;
                }
                
                return true;
            } else {
                // Create vendor_users record if it doesn't exist for vendor agent
                \Illuminate\Support\Facades\Log::warning('Creating missing vendor_users record for agent', [
                    'user_uid' => $userUid,
                    'user_id' => $user->_id,
                    'vendor_id' => $vendorId
                ]);
                
                $newVendorUser = new VendorUserModel();
                $newVendorUser->vendors__id = $vendorId;
                $newVendorUser->users__id = $user->_id;
                $newVendorUser->__data = [
                    'permissions' => [
                        'messaging' => 'allow'
                    ]
                ];
                $newVendorUser->save();
                
                \Illuminate\Support\Facades\Log::info('Created vendor_users record with messaging permission', [
                    'user_uid' => $userUid,
                    'vendor_id' => $vendorId
                ]);
                
                return true;
            }
        }

        \Illuminate\Support\Facades\Log::error('Unable to ensure messaging permission - user role not supported', [
            'user_uid' => $userUid,
            'user_role_id' => $user->user_roles__id ?? 'unknown',
            'vendor_id' => $vendorId
        ]);

        return false;
    }
}
