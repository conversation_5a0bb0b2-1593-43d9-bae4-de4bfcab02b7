@extends('layouts.app', ['title' => __tr('Bot Flows')])
@section('content')
@include('users.partials.header', [
'title' => __tr(''),
'description' => '',
'class' => 'col-lg-7'
])
<div class="container-fluid mt-lg--6">
    <div class="row mt-3">
        <!-- button -->
        <div class="col-xl-12 mb-3 mt-5">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title mb-0" style="color: #22A755; margin-left:10px">
                    <i class="fas fa-robot"></i> {{ __tr(' Bot Flows') }}
                </h1>
                <button type="button" class="lw-btn btn" style="background-color: #1f7a35; color: #fff; border: none; margin-right:10px" 
                    data-toggle="modal" data-target="#lwAddNewBotFlow">
                    <i class="fa fa-plus-circle"></i> {{ __tr(' Add New Bot Flow') }}
                </button>
            </div>
        </div>

        <!--/ button -->
        <!-- Add New Bot Flow Modal -->
        <x-lw.modal id="lwAddNewBotFlow" :header="__tr('Add New Bot Flow')" :hasForm="true">
            <!--  Add New Bot Flow Form -->
            <x-lw.form id="lwAddNewBotFlowForm" :action="route('vendor.bot_reply.bot_flow.write.create')"
                :data-callback-params="['modalId' => '#lwAddNewBotFlow', 'datatableId' => '#lwBotFlowList']"
                data-callback="appFuncs.modelSuccessCallback" x-data="{triggerType:'is'}">
                <!-- form body -->
                <div class="lw-form-modal-body">
                    <!-- form fields form fields -->
                    <!-- Title -->
                    <x-lw.input-field type="text" id="lwTitleField" data-form-group-class="" :label="__tr('Title')"
                        name="title" required="true" minlength="1" maxlength="150" />
                    <!-- /Title -->

                    <!-- Trigger Type -->
                    <x-lw.input-field x-model="triggerType" type="selectize" id="lwTriggerTypeField"
                        data-form-group-class="" data-selected="is" :label="__tr('Trigger Type')" name="trigger_type"
                        required="true">
                        <x-slot name="selectOptions">
                            <option value="">{{ __tr('How do you want to trigger this flow?') }}</option>
                            @foreach (configItem('bot_reply_trigger_types') as $replyBotTypeKey => $replyBotType)
                            <option value="{{ $replyBotTypeKey }}">{{ $replyBotType['title'] }} </option>
                            @endforeach
                        </x-slot>
                    </x-lw.input-field>
                    <!-- /Trigger Type -->

                    @foreach (configItem('bot_reply_trigger_types') as $replyBotTypeKey => $replyBotType)
                    <div x-show="triggerType == '{{ $replyBotTypeKey }}'" class="alert alert-dark">{{
                        $replyBotType['description'] }}</div>
                    @endforeach

                    <!-- Start Trigger -->
                    <div x-show="triggerType != 'welcome' && triggerType != 'new_message'">
                        <x-lw.input-field type="text" id="lwStartTriggerField" data-form-group-class="" :label="__tr('Start Trigger Subject')" name="start_trigger" required="true" minlength="1" maxlength="255" />
                        <div><small class="text-muted">{{ __tr('You can have comma separated multiple triggers.') }}</small></div>
                    </div>
                    <!-- /Start Trigger -->
                </div>
                <!-- form footer -->
                <div class="modal-footer">
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary">{{ __('Submit') }}</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
                </div>
            </x-lw.form>
            <!--/  Add New Bot Flow Form -->
        </x-lw.modal>
        <!--/ Add New Bot Flow Modal -->
        <!-- Edit Bot Flow Modal -->
        <x-lw.modal id="lwEditBotFlow" :header="__tr('Edit Bot Flow')" :hasForm="true">
            <!--  Edit Bot Flow Form -->
            <x-lw.form id="lwEditBotFlowForm" :action="route('vendor.bot_reply.bot_flow.write.update')"
                :data-callback-params="['modalId' => '#lwEditBotFlow', 'datatableId' => '#lwBotFlowList']"
                data-callback="appFuncs.modelSuccessCallback">
                <!-- form body -->
                <div id="lwEditBotFlowBody" class="lw-form-modal-body"></div>
                <script type="text/template" id="lwEditBotFlowBody-template">
                    <div x-data="{triggerType:'<%- __tData.trigger_type || 'is' %>'}">
                    <input type="hidden" name="botFlowIdOrUid" value="<%- __tData._uid %>" />
                        <!-- form fields -->
                        <!-- Title -->
           <x-lw.input-field type="text" id="lwTitleEditField" data-form-group-class="" :label="__tr('Title')" value="<%- __tData.title %>" name="title"  required="true"      minlength="1"      maxlength="150"           />
                <!-- /Title -->

                        <!-- Trigger Type -->
                        <x-lw.input-field x-model="triggerType" type="selectize" id="lwTriggerTypeEditField"
                            data-form-group-class="" data-selected="<%- __tData.trigger_type || 'is' %>" :label="__tr('Trigger Type')" name="trigger_type"
                            required="true">
                            <x-slot name="selectOptions">
                                <option value="">{{ __tr('How do you want to trigger this flow?') }}</option>
                                @foreach (configItem('bot_reply_trigger_types') as $replyBotTypeKey => $replyBotType)
                                <option value="{{ $replyBotTypeKey }}">{{ $replyBotType['title'] }} </option>
                                @endforeach
                            </x-slot>
                        </x-lw.input-field>
                        <!-- /Trigger Type -->

                        @foreach (configItem('bot_reply_trigger_types') as $replyBotTypeKey => $replyBotType)
                        <div x-show="triggerType == '{{ $replyBotTypeKey }}'" class="alert alert-dark">{{
                            $replyBotType['description'] }}</div>
                        @endforeach

                        <!-- Start Trigger -->
                        <div x-show="triggerType != 'welcome' && triggerType != 'new_message'">
           <x-lw.input-field type="text" id="lwStartTriggerEditField" data-form-group-class="" :label="__tr('Start Trigger Subject')" value="<%- __tData.start_trigger %>" name="start_trigger"  required="true"    minlength="1"      maxlength="255"           />
                        <div><small class="text-muted">{{ __tr('You can have comma separated multiple triggers.') }}</small></div>
                        </div>
                <!-- /Start Trigger -->
                <div class="form-group pt-3">
                    <input type="checkbox" id="lwEditBotFlowStatus" <%- __tData.status == 1 ? 'checked' : '' %> data-lw-plugin="lwSwitchery" value="1" name="status">
                    <label for="lwEditBotFlowStatus">{{  __tr('Status') }}</label>
                </div>
                    </div>
                     </script>
                <!-- form footer -->
                <div class="modal-footer">
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary">{{ __('Submit') }}</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
                </div>
            </x-lw.form>
            <!--/  Edit Bot Flow Form -->
        </x-lw.modal>
        <!--/ Edit Bot Flow Modal -->
        <div class="col-xl-12">
            <x-lw.datatable id="lwBotFlowList" :url="route('vendor.bot_reply.bot_flow.read.list')">
                <th data-orderable="true" data-name="title">{{ __tr('Title') }}</th>
                <th data-orderable="true" data-name="trigger_type" data-template="#triggerTypeColumnTemplate">{{ __tr('Trigger Type') }}</th>
                <th data-orderable="true" data-name="start_trigger">{{ __tr('Start Trigger Subject') }}</th>
                <th data-orderable="true" data-name="status">{{ __tr('Status') }}</th>
                <th data-template="#botFlowActionColumnTemplate" name="null">{{ __tr('Action') }}</th>
            </x-lw.datatable>
        </div>
        <!-- trigger type template -->
        <script type="text/template" id="triggerTypeColumnTemplate">
            <%
                var triggerTypes = {
                    @foreach (configItem('bot_reply_trigger_types') as $key => $type)
                        '{{ $key }}': '{{ $type['title'] }}',
                    @endforeach
                };
                var triggerType = __tData.trigger_type || 'is';
                var displayName = triggerTypes[triggerType] || triggerType;
            %>
            <span class="badge badge-info"><%= displayName %></span>
        </script>
        <!-- /trigger type template -->
        <!-- action template -->
        <script type="text/template" id="botFlowActionColumnTemplate">
            <!-- Edit Button -->
            <a data-pre-callback="appFuncs.clearContainer"
            title="{{ __tr('Edit') }}"
            class="btn btn-sm lw-ajax-link-action"
            style="background-color: #f8f9fa; color: #333; border: 1px solid #ced4da; border-radius: 8px; padding: 6px 12px; margin-right: 6px; transition: all 0.3s ease; box-shadow: 0 2px 6px rgba(0,0,0,0.04);"
            onmouseover="this.style.backgroundColor='#e2e6ea'; this.style.transform='translateY(-2px)';"
            onmouseout="this.style.backgroundColor='#f8f9fa'; this.style.transform='none';"
            data-response-template="#lwEditBotFlowBody"
            href="<%= __Utils.apiURL('{{ route('vendor.bot_reply.bot_flow.read.update.data', ['botFlowIdOrUid']) }}', {'botFlowIdOrUid': __tData._uid}) %>"
            data-toggle="modal"
            data-target="#lwEditBotFlow">
                <i class="fa fa-edit"></i> {{ __tr('Edit') }}
            </a>

            <!-- Delete Button -->
            <a data-method="post" 
            href="<%= __Utils.apiURL('{{ route('vendor.bot_reply.bot_flow.write.delete', ['botFlowIdOrUid']) }}', {'botFlowIdOrUid': __tData._uid}) %>" 
            class="btn btn-sm lw-ajax-link-action-via-confirm"
            style="background-color: #fff; color: #dc3545; border: 1px solid #dc3545; border-radius: 8px; padding: 6px 12px; margin-right: 6px; transition: all 0.3s ease; box-shadow: 0 2px 6px rgba(0,0,0,0.04);"
            onmouseover="this.style.backgroundColor='#dc3545'; this.style.color='#fff'; this.style.transform='translateY(-2px)';"
            onmouseout="this.style.backgroundColor='#fff'; this.style.color='#dc3545'; this.style.transform='none';"
            data-confirm="#lwDeleteBotFlow-template" 
            title="{{ __tr('Delete') }}" 
            data-callback-params="{{ json_encode(['datatableId' => '#lwBotFlowList']) }}" 
            data-callback="appFuncs.modelSuccessCallback">
                <i class="fa fa-trash"></i> {{ __tr('Delete') }}
            </a>

            <!-- Flow Builder Button -->
            <a title="{{ __tr('Flow Builder') }}" 
                class="btn btn-sm"
                style="
                    background-color: #0C8129; 
                    color: #fff; 
                    border: none; 
                    border-radius: 8px; 
                    padding: 6px 12px; 
                    transition: all 0.3s ease; 
                    box-shadow: 0 2px 6px rgba(0,0,0,0.04);
                "
                onmouseover="this.style.backgroundColor='#1f3b64'; this.style.transform='translateY(-2px)';"
                onmouseout="this.style.backgroundColor='#0C8129'; this.style.transform='none';"
                href="<%= __Utils.apiURL('{{ route('vendor.bot_reply.bot_flow.builder.read.view', ['botFlowIdOrUid']) }}', {'botFlowIdOrUid': __tData._uid}) %>">
                    <i class="fas fa-project-diagram" style="color: #fff; margin-right: 6px;"></i>
                    <span style="color: #fff;">{{ __tr('Flow Builder') }}</span>
            </a>
        </script>

        <!-- /action template -->

        <!-- Bot Flow delete template -->
        <script type="text/template" id="lwDeleteBotFlow-template">
            <h2>{{ __tr('Are You Sure!') }}</h2>
            <p>{{ __tr('You want to delete this Bot Flow?') }}</p>
    </script>
        <!-- /Bot Flow delete template -->
    </div>
</div>
@endsection()