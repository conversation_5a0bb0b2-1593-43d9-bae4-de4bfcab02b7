@php
/**
* Component : <PERSON>t<PERSON>eply
* Controller : BotReplyController
* File : BotReply.list.blade.php
* ----------------------------------------------------------------------------- */
@endphp
@extends('layouts.app', ['title' => __tr('Chatbot')])
@section('content')
@include('users.partials.header', [
'title' => __tr(''),
'description' => '',
'class' => 'col-lg-7'
])
<div class="container-fluid mt-lg--6">
    <div class="row mt-3" x-data="{isAdvanceBot:'interactive',botFlowUid:null}">
        <!-- button -->
        <div class="col-xl-12 mb-3">
            <div class="mt-5 d-flex justify-content-between align-items-center flex-wrap">
                <!-- Left: Title -->
                <h1 class="page-title mb-0" style="color: #22A755;">
                    <i class="fas fa-robot"></i> {{ __tr('Chatbot') }}
                </h1>
                <!-- Right: Button Dropdown -->
                <div class="btn-group mt-2 mt-md-0 d-flex align-items-center">
                    <!-- Create New Bot Button -->
                    <button type="button"
                        class="btn dropdown-toggle btn-dark-green mr-10" style="margin-right: 8px; border-radius: 8px;"
                        data-toggle="dropdown"
                        aria-expanded="false">
                        {{ __tr('Create New Bot') }}
                    </button>

                    <!-- Dropdown menu -->
                    <div class="dropdown-menu dropdown-menu-right">
                        <button type="button"
                            @click="isAdvanceBot = 'simple'"
                            class="dropdown-item btn"
                            data-toggle="modal"
                            data-target="#lwAddNewAdvanceBotReply">
                            {{ __tr('Simple Chatbot') }}
                        </button>
                        <button type="button"
                            @click="isAdvanceBot = 'media'"
                            class="dropdown-item btn"
                            data-toggle="modal"
                            data-target="#lwAddNewAdvanceBotReply">
                            {{ __tr('Media Chatbot') }}
                        </button>
                        <button type="button"
                            @click="isAdvanceBot = 'interactive'"
                            class="dropdown-item btn"
                            data-toggle="modal"
                            data-target="#lwAddNewAdvanceBotReply">
                            {{ __tr('Advance Interactive Chatbot') }}
                        </button>
                    </div>

                    <!-- Help Modal Button -->
                    <x-lw.help-modal :subject="__tr('What are the Chatbots and How to use it?')" 
                        class="btn btn-success text-white shadow-sm px-4 py-2 rounded"         
                        style="background-color: #22A755; border: none;">
                        
                        <template #trigger>
                            <i class="fas fa-info-circle mr-2"></i> {{ __tr('Help') }}
                        </template>

                        <!-- Modal content with green outline -->
                        <div class="p-4 bg-light rounded border border-success">
                            <h3 class="mb-3" style="color: #1c5c3a;">{{ __tr('What are Bots') }}</h3>                            
                            <p class="text-dark">
                                {{ __tr('Bots are instructions given to the system so when you get a message, a reply is triggered automatically.') }}
                            </p>
                        </div>
                    </x-lw.help-modal>

                </div>

            </div>
        </div>

        <!--/ button -->
       
        <div class="col-xl-12">
            <x-lw.datatable data-page-length="200" id="lwBotReplyList" class="table-striped table-hover table-bordered" :url="route('vendor.bot_reply.read.list')">
                <th data-orderable="true" data-name="name">{{ __tr('Name') }}</th>
                <th data-name="bot_type">{{ __tr('Bot Type') }}</th>
                <th data-orderable="true" data-name="trigger_type">{{ __tr('Trigger Type') }}</th>
                <th data-orderable="true" data-name="reply_trigger">{{ __tr('Trigger Subject') }}</th>
                <th data-orderable="true" data-name="status">{{ __tr('Status') }}</th>
                <th data-orderable="true" data-name="created_at">{{ __tr('Created At') }}</th>
                <th data-template="#botReplyActionColumnTemplate" name="null">{{ __tr('Action') }}</th>
            </x-lw.datatable>
            @include('bot-reply.bot-forms-partial')
        </div>
        <!-- action template -->
        <script type="text/template" id="botReplyActionColumnTemplate">
            <!-- Edit Button -->
            <a 
                data-pre-callback="appFuncs.clearContainer"
                title="{{ __tr('Edit') }}"
                style="
                    background-color: #f8f9fa;
                    color: #333;
                    border: 1px solid #ced4da;
                    border-radius: 8px;
                    padding: 12px 12px;
                    margin-right: 6px;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                    display: inline-flex;
                    align-items: center;
                    gap: 6px;"
                onmouseover="this.style.backgroundColor='#e2e6ea'; this.style.transform='scale(1.05)'"
                onmouseout="this.style.backgroundColor='#f8f9fa'; this.style.transform='scale(1)'"
                data-response-template="#lwEditBotReplyBody"
                href="<%= __Utils.apiURL('{{ route('vendor.bot_reply.read.update.data', ['botReplyIdOrUid']) }}', {'botReplyIdOrUid': __tData._uid}) %>"
                data-toggle="modal"
                data-target="#lwEditBotReply"
                class="lw-ajax-link-action"
            >
                <i class="fa fa-edit me-1"></i> {{ __tr('') }}
            </a>
            <!-- Delete Button -->
            <a 
                data-method="post"
                href="<%= __Utils.apiURL('{{ route('vendor.bot_reply.write.delete', [ 'botReplyIdOrUid']) }}', {'botReplyIdOrUid': __tData._uid}) %>"
                class="btn lw-ajax-link-action-via-confirm"
                style="
                    background-color: #fff;
                    color: #dc3545;
                    border: 1px solid #dc3545;
                    border-radius: 8px;
                    padding: 12px 12px;
                    margin-right: 6px;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                    display: inline-flex;
                    align-items: center;
                    gap: 6px;"
                onmouseover="this.style.backgroundColor='#dc3545'; this.style.color='#fff'; this.style.transform='scale(1.05)'"
                onmouseout="this.style.backgroundColor='#fff'; this.style.color='#dc3545'; this.style.transform='scale(1)'"
                data-confirm="#lwDeleteBotReply-template"
                title="{{ __tr('Delete') }}"
                data-callback-params="{{ json_encode(['datatableId' => '#lwBotReplyList']) }}"
                data-callback="appFuncs.modelSuccessCallback"
            >
                <i class="fa fa-trash me-1"></i> {{ __tr('') }}
            </a>
            <!-- Clone Button -->
            <a 
                data-method="post"
                href="<%= __Utils.apiURL('{{ route('vendor.bot_reply.write.duplicate', [ 'botReplyIdOrUid']) }}', {'botReplyIdOrUid': __tData._uid}) %>"
                class="btn lw-ajax-link-action-via-confirm"
                style="
                    background-color: #e9ecef;
                    color: #333;
                    border: 1px solid #ced4da;
                    border-radius: 8px;
                    padding: 12px 12px;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                    display: inline-flex;
                    align-items: center;
                    gap: 6px;"
                onmouseover="this.style.backgroundColor='#dee2e6'; this.style.transform='scale(1.05)'"
                onmouseout="this.style.backgroundColor='#e9ecef'; this.style.transform='scale(1)'"
                data-confirm="#lwDuplicateBotReply-template"
                title="{{ __tr('Duplicate') }}"
                data-callback-params="{{ json_encode(['datatableId' => '#lwBotReplyList']) }}"
                data-callback="appFuncs.modelSuccessCallback"
            >
                <i class="fa fa-copy me-1" style="font-size: 14px;"></i> {{ __tr('') }}
            </a>

        </script>

        <!-- /action template -->

        <!-- Bot Reply delete template -->
        <script type="text/template" id="lwDeleteBotReply-template">
            <h2>{{ __tr('Are You Sure!') }}</h2>
            <p>{{ __tr('You want to delete this Chatbot?') }}</p>
    </script>
        <!-- /Bot Reply delete template -->
        <!-- Bot Reply duplicate template -->
        <script type="text/template" id="lwDuplicateBotReply-template">
            <h2>{{ __tr('Are You Sure!') }}</h2>
            <p>{{ __tr('You want to duplicate this Chatbot?') }}</p>
    </script>
        <!-- /Bot Reply duplicate template -->
    </div>
</div>
<style>
    .btn-dark-green {
        background-color: #146c43 !important; /* Dark green */
        color: #fff !important;
        border: none;
        transition: background-color 0.3s ease;
    }

    .btn-dark-green:hover {
        background-color: #198754 !important; /* Lighter green on hover */
    }

    .mr-10 {
        margin-right: 10px;
    }
    /* DataTable White-Blue Theme */
    table.dataTable {
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
    }

    table.dataTable thead {
        background-color: #e3f2fd; /* Light blue */
        color: #0d47a1; /* Dark blue text */
    }

    table.dataTable thead th {
        padding: 12px;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
    }

    table.dataTable tbody td {
        padding: 12px;
        font-size: 14px;
        color: #212529;
    }

    table.dataTable tbody tr:hover {
        background-color: #f1f9ff;
        cursor: pointer;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 6px 10px;
        margin: 2px;
        color: #0d6efd !important;
        transition: all 0.2s ease-in-out;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background-color: #0d6efd;
        color: #fff !important;
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 6px;
        font-size: 14px;
    }

    .dataTables_wrapper .dataTables_filter label {
        font-weight: 500;
        color: #0d47a1;
    }

    .dataTables_wrapper .dataTables_info {
        color: #6c757d;
        font-size: 14px;
    }

</style>

@endsection()