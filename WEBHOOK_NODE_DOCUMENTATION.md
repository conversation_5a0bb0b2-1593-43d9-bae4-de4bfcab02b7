# Webhook Node Documentation

## Overview
The Webhook Node allows your WhatsApp bot flows to call external APIs and process their responses. This enables integration with third-party services, databases, and custom applications.

## Features
- **HTTP Methods**: Supports GET, POST, PUT, PATCH, DELETE
- **Dynamic Variables**: Use contact data and user input in requests
- **Response Mapping**: Map API response data to flow variables
- **Error Handling**: Separate flow paths for success and failure
- **Timeout Control**: Configurable request timeouts (5-120 seconds)

## Available Dynamic Variables

### Contact Variables
- `{first_name}` - <PERSON>'s first name
- `{last_name}` - <PERSON>'s last name  
- `{full_name}` - <PERSON>'s full name
- `{phone_number}` - <PERSON>'s phone number
- `{email}` - <PERSON>'s email address
- `{country}` - Contact's country
- `{language_code}` - Contact's language code

### Special Variables
- `{user_input}` - User's last input/message
- Custom field variables (if configured)

## Configuration

### Basic Settings
1. **Webhook URL**: The external API endpoint to call
2. **HTTP Method**: Choose from GET, POST, PUT, PATCH, DELETE
3. **Request Body**: JSON template with dynamic variables
4. **Timeout**: Request timeout in seconds (5-120)

### Messages
- **Success Message**: Shown when webhook succeeds
- **Error Message**: Shown when webhook fails

### Response Mapping (Optional)
Map API response data to variables for use in subsequent nodes:
- **Source Path**: Use dot notation (e.g., `data.user.name`)
- **Target Variable**: Variable name to store the value

## Example Configurations

### Simple POST Request
```json
{
  "message": "{full_name} sent: {user_input}",
  "phone": "{phone_number}",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Order Processing
```json
{
  "customer": {
    "name": "{full_name}",
    "phone": "{phone_number}",
    "email": "{email}"
  },
  "order": {
    "item": "{user_input}",
    "quantity": 1
  }
}
```

### Response Mapping Example
If your API returns:
```json
{
  "success": true,
  "data": {
    "order_id": "ORD-1234",
    "status": "confirmed",
    "total": 25.99
  }
}
```

You can map:
- Source: `data.order_id` → Target: `order_id`
- Source: `data.status` → Target: `order_status`
- Source: `data.total` → Target: `order_total`

Then use `{order_id}`, `{order_status}`, `{order_total}` in subsequent nodes.

## Flow Outputs

### Success Output
Connected when the webhook call succeeds (HTTP 2xx response).

### Failed Output  
Connected when the webhook call fails due to:
- Network errors
- HTTP error responses (4xx, 5xx)
- Timeouts
- Invalid URL

## Best Practices

1. **Always handle failures**: Connect the "Failed" output to provide user feedback
2. **Use appropriate timeouts**: Don't set too high to avoid blocking the flow
3. **Validate URLs**: Ensure webhook URLs are accessible and secure
4. **Test thoroughly**: Use the provided test webhook for initial testing
5. **Secure sensitive data**: Don't include sensitive information in request bodies
6. **Use HTTPS**: Always use secure URLs for production webhooks

## Testing

Use the included `test-webhook.php` file for testing:

1. Upload the file to a web server
2. Use the URL in your webhook node
3. Test different scenarios by including `test_scenario` in your request:
   - `"test_scenario": "error"` - Simulates an error response
   - `"test_scenario": "timeout"` - Simulates a slow response

## Troubleshooting

### Common Issues

1. **"Undefined constant" errors**: 
   - Check variable names match available dynamic variables
   - Use `{variable}` format, not `{{variable}}`

2. **Webhook not triggering**:
   - Verify URL is accessible
   - Check HTTP method matches API requirements
   - Ensure request body is valid JSON

3. **Variables not replaced**:
   - Verify contact data is available in flow context
   - Check variable names are spelled correctly

4. **Response mapping not working**:
   - Verify API returns JSON response
   - Check source paths use correct dot notation
   - Ensure target variable names are valid

### Debug Information
Check application logs for detailed webhook execution information including:
- Request details
- Response data
- Variable values
- Error messages

## Security Considerations

1. **URL Validation**: Only use trusted webhook URLs
2. **Data Sanitization**: Be careful with user input in requests
3. **Rate Limiting**: Consider API rate limits
4. **Authentication**: Use proper authentication headers if required
5. **HTTPS Only**: Use secure connections for sensitive data

## Integration Examples

### CRM Integration
```json
{
  "contact": {
    "name": "{full_name}",
    "phone": "{phone_number}",
    "source": "whatsapp_bot"
  },
  "interaction": {
    "message": "{user_input}",
    "timestamp": "{{current_timestamp}}"
  }
}
```

### Order Management
```json
{
  "customer_phone": "{phone_number}",
  "order_details": "{user_input}",
  "channel": "whatsapp"
}
```

### Support Ticket Creation
```json
{
  "customer": {
    "name": "{full_name}",
    "contact": "{phone_number}"
  },
  "issue": {
    "description": "{user_input}",
    "priority": "normal",
    "source": "whatsapp_bot"
  }
}
```
