<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Yantrana\Components\InstagramService\Services\InstagramApiService;
use App\Yantrana\Components\InstagramService\InstagramServiceEngine;
use App\Yantrana\Components\InstagramService\Repositories\InstagramMessageLogRepository;
use App\Yantrana\Components\InstagramService\Controllers\InstagramServiceController;
use App\Yantrana\Components\InstagramService\Controllers\InstagramWebhookController;
use App\Yantrana\Components\LiveChat\Controllers\LiveChatController;

class InstagramServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register Instagram API Service
        $this->app->singleton(InstagramApiService::class, function ($app) {
            return new InstagramApiService();
        });

        // Register Instagram Message Log Repository
        $this->app->singleton(InstagramMessageLogRepository::class, function ($app) {
            return new InstagramMessageLogRepository();
        });

        // Register Instagram Service Engine
        $this->app->singleton(InstagramServiceEngine::class, function ($app) {
            return new InstagramServiceEngine(
                $app->make(InstagramApiService::class),
                $app->make(InstagramMessageLogRepository::class),
                $app->make(\App\Yantrana\Components\Contact\Repositories\ContactRepository::class),
                $app->make(\App\Yantrana\Components\Media\MediaEngine::class)
            );
        });

        // Register Instagram Service Controller
        $this->app->singleton(InstagramServiceController::class, function ($app) {
            return new InstagramServiceController(
                $app->make(InstagramServiceEngine::class)
            );
        });

        // Register Instagram Webhook Controller
        $this->app->singleton(InstagramWebhookController::class, function ($app) {
            return new InstagramWebhookController(
                $app->make(InstagramServiceEngine::class)
            );
        });

        // Register Live Chat Controller
        $this->app->singleton(LiveChatController::class, function ($app) {
            return new LiveChatController(
                $app->make(\App\Yantrana\Components\Contact\Repositories\ContactRepository::class),
                $app->make(\App\Yantrana\Components\WhatsAppService\Repositories\WhatsAppMessageLogRepository::class),
                $app->make(InstagramMessageLogRepository::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register Instagram-specific configurations
        $this->registerInstagramConfigurations();

        // Register Instagram event listeners
        $this->registerInstagramEventListeners();

        // Register Instagram middleware
        $this->registerInstagramMiddleware();

        // Register Instagram view composers
        $this->registerInstagramViewComposers();
    }

    /**
     * Register Instagram-specific configurations
     */
    protected function registerInstagramConfigurations(): void
    {
        // Add Instagram media restrictions
        config([
            'media-restrictions.instagram_media' => [
                'image' => [
                    'extensions' => ['jpg', 'jpeg', 'png', 'gif'],
                    'max_size' => 10240, // 10MB
                    'mime_types' => ['image/jpeg', 'image/png', 'image/gif']
                ],
                'video' => [
                    'extensions' => ['mp4', 'mov', 'avi'],
                    'max_size' => 51200, // 50MB
                    'mime_types' => ['video/mp4', 'video/quicktime', 'video/x-msvideo']
                ],
                'audio' => [
                    'extensions' => ['mp3', 'wav', 'aac'],
                    'max_size' => 10240, // 10MB
                    'mime_types' => ['audio/mpeg', 'audio/wav', 'audio/aac']
                ],
                'file' => [
                    'extensions' => ['pdf', 'doc', 'docx', 'txt'],
                    'max_size' => 10240, // 10MB
                    'mime_types' => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
                ]
            ]
        ]);

        // Add Instagram-specific cache tags
        config([
            'cache.instagram_tags' => [
                'instagram_contacts',
                'instagram_messages',
                'instagram_stats',
                'instagram_config'
            ]
        ]);
    }

    /**
     * Register Instagram event listeners
     */
    protected function registerInstagramEventListeners(): void
    {
        // Listen for Instagram message events
        \Illuminate\Support\Facades\Event::listen(
            'instagram.message.received',
            function ($messageData) {
                // Handle incoming Instagram message
                \Illuminate\Support\Facades\Log::info('Instagram message received', $messageData);
            }
        );

        \Illuminate\Support\Facades\Event::listen(
            'instagram.message.sent',
            function ($messageData) {
                // Handle outgoing Instagram message
                \Illuminate\Support\Facades\Log::info('Instagram message sent', $messageData);
            }
        );

        \Illuminate\Support\Facades\Event::listen(
            'instagram.webhook.verified',
            function ($vendorId) {
                // Handle Instagram webhook verification
                \Illuminate\Support\Facades\Log::info('Instagram webhook verified', ['vendor_id' => $vendorId]);
            }
        );
    }

    /**
     * Register Instagram middleware
     */
    protected function registerInstagramMiddleware(): void
    {
        // Register Instagram-specific middleware if needed
        $router = $this->app['router'];

        // Middleware to check Instagram configuration
        $router->aliasMiddleware('instagram.configured', function ($request, $next) {
            if (!getVendorSettings('enable_instagram_messaging', false)) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => __tr('Instagram messaging is not enabled')
                    ], 403);
                }
                
                return redirect()->route('vendor.settings.index')
                    ->with('error', __tr('Instagram messaging is not enabled'));
            }

            return $next($request);
        });

        // Middleware to check Instagram API configuration
        $router->aliasMiddleware('instagram.api.configured', function ($request, $next) {
            $requiredSettings = [
                'instagram_access_token',
                'instagram_account_id'
            ];

            foreach ($requiredSettings as $setting) {
                if (empty(getVendorSettings($setting))) {
                    if ($request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => __tr('Instagram API is not properly configured')
                        ], 403);
                    }

                    return redirect()->route('vendor.settings.index')
                        ->with('error', __tr('Instagram API is not properly configured'));
                }
            }

            return $next($request);
        });
    }

    /**
     * Register Instagram view composers
     */
    protected function registerInstagramViewComposers(): void
    {
        // Share Instagram configuration status with all views
        view()->composer('*', function ($view) {
            $view->with('instagramEnabled', getVendorSettings('enable_instagram_messaging', false));
            $view->with('instagramConfigured', $this->isInstagramConfigured());
        });

        // Share Instagram stats with dashboard views
        view()->composer(['dashboard.*', 'live-chat.*'], function ($view) {
            if (getVendorSettings('enable_instagram_messaging', false)) {
                $instagramStats = $this->getInstagramStats();
                $view->with('instagramStats', $instagramStats);
            }
        });
    }

    /**
     * Check if Instagram is properly configured
     */
    protected function isInstagramConfigured(): bool
    {
        $requiredSettings = [
            'instagram_app_id',
            'instagram_app_secret',
            'instagram_access_token',
            'instagram_page_id'
        ];

        foreach ($requiredSettings as $setting) {
            if (empty(getVendorSettings($setting))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get Instagram statistics
     */
    protected function getInstagramStats(): array
    {
        try {
            $vendorId = getVendorId();
            
            if (!$vendorId) {
                return [];
            }

            $instagramRepository = app(InstagramMessageLogRepository::class);
            
            return [
                'total_messages' => $instagramRepository->getMessageStats($vendorId, 'month')['total_messages'] ?? 0,
                'unread_messages' => $instagramRepository->getUnreadMessagesCount($vendorId),
                'contacts_count' => app(\App\Yantrana\Components\Contact\Repositories\ContactRepository::class)
                    ->fetchIt(['vendors__id' => $vendorId, 'platform' => 'instagram'])->count(),
            ];
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to get Instagram stats', [
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            InstagramApiService::class,
            InstagramServiceEngine::class,
            InstagramMessageLogRepository::class,
            InstagramServiceController::class,
            InstagramWebhookController::class,
            LiveChatController::class,
        ];
    }
}
