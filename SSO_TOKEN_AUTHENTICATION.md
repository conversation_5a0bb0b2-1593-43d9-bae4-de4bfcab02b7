# SSO Token Authentication for External Access

This document explains how to use SSO token authentication to access specific routes without the sidebar and navbar.

## Overview

The SSO token authentication allows external systems to access specific vendor console routes using a JWT token passed as a query parameter. This bypasses the normal authentication flow and provides a clean interface without navigation elements.

**Key Feature:** Once authenticated with an SSO token, users remain logged in for the session duration. You only need to provide the SSO token once - subsequent requests to the same routes will work without the token as long as the session is active.

## Available Routes

The following routes are available for SSO token authentication:

### WhatsApp Routes
- `/vendor-console/whatsapp/contact/chat?sso_token=YOUR_TOKEN` - WhatsApp Chat Interface
- `/vendor-console/whatsapp/templates?sso_token=YOUR_TOKEN` - WhatsApp Templates Management
- `/vendor-console/whatsapp/flows?sso_token=YOUR_TOKEN` - WhatsApp Flows Management

### Facebook Routes
- `/vendor-console/facebook/chat?sso_token=YOUR_TOKEN`

### Instagram Routes
- `/vendor-console/instagram/chat?sso_token=YOUR_TOKEN`

### Contact Routes
- `/vendor-console/contacts/list?sso_token=YOUR_TOKEN`
- `/vendor-console/contacts/groups?sso_token=YOUR_TOKEN`
- `/vendor-console/contacts/custom-fields?sso_token=YOUR_TOKEN`

### User Routes
- `/vendor-console/users?sso_token=YOUR_TOKEN`

### Settings Routes
- `/vendor-console/settings/whatsapp-cloud-api-setup?sso_token=YOUR_TOKEN`
- `/vendor-console/settings/instagram-api-setup?sso_token=YOUR_TOKEN`
- `/vendor-console/settings/facebook-api-setup?sso_token=YOUR_TOKEN`
- `/vendor-console/settings/ai-chat-bot-setup?sso_token=YOUR_TOKEN`
- `/vendor-console/settings/whatsapp-orders-setup?sso_token=YOUR_TOKEN`
- `/vendor-console/settings/api-access?sso_token=YOUR_TOKEN`

### Google Sheet Script
- `/vendor-console/google-sheet-script?sso_token=YOUR_TOKEN`

## How to Generate SSO Token

### 1. Using the existing SSO system

The SSO token should be generated using the same JWT secret (`SSO_SECRET`) that's used for the main SSO login system.

### 2. Token Payload Structure

```json
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "company_id": 123,
  "role": "vendor_admin",
  "first_name": "John",
  "last_name": "Doe",
  "vendor_id": 456,
  "company_name": "Example Company",
  "exp": 1640995200
}
```

### 3. JWT Token Generation

```php
use Firebase\JWT\JWT;

$payload = [
    'email' => '<EMAIL>',
    'name' => 'John Doe',
    'company_id' => 123,
    'role' => 'vendor_admin',
    'first_name' => 'John',
    'last_name' => 'Doe',
    'vendor_id' => 456,
    'company_name' => 'Example Company',
    'exp' => time() + (60 * 60 * 24) // 24 hours from now
];

$token = JWT::encode($payload, env('SSO_SECRET', 'your-secret-key'), 'HS256');
```

## Session Management

### One-Time Authentication

The SSO token authentication works as a one-time authentication mechanism:

1. **First Access:** User visits a route with `?sso_token=YOUR_TOKEN`
2. **Authentication:** System validates the token and creates a session
3. **Subsequent Access:** User can access any SSO-protected route without the token
4. **Session Duration:** Authentication persists until the session expires or user logs out

### Example Flow

```
1. User visits: /vendor-console/whatsapp/contact/chat?sso_token=TOKEN
   → User is authenticated and session is created

2. User can now visit: /vendor-console/whatsapp/templates
   → No token needed, session is maintained

3. User can visit: /vendor-console/contacts/list
   → No token needed, session is maintained
```

## Usage Examples

### 1. Initial Authentication (with token)

```
https://your-domain.com/vendor-console/whatsapp/contact/chat?sso_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### 2. Subsequent Access (no token needed)

```
https://your-domain.com/vendor-console/whatsapp/templates
https://your-domain.com/vendor-console/contacts/list
https://your-domain.com/vendor-console/settings/whatsapp-cloud-api-setup
```

### 3. JavaScript Redirect

```javascript
const ssoToken = 'your-jwt-token-here';
const targetUrl = `/vendor-console/whatsapp/contact/chat?sso_token=${ssoToken}`;
window.location.href = targetUrl;
```

### 4. Iframe Embedding

```html
<iframe 
    src="https://your-domain.com/vendor-console/whatsapp/contact/chat?sso_token=YOUR_TOKEN"
    width="100%" 
    height="600px"
    frameborder="0">
</iframe>
```

## Security Features

1. **Token Validation**: JWT tokens are validated using the shared `SSO_SECRET`
2. **User Verification**: Users must exist in the database and have active status
3. **Token Expiration**: Tokens have an expiration time for security
4. **Logging**: All authentication attempts are logged for monitoring
5. **Error Handling**: Proper error responses for invalid or expired tokens

## Error Handling

The SSO token authentication system provides comprehensive error handling for both API and web requests.

### Web Route Errors

For web routes, users will see a beautiful error page with:
- Clear error title and description
- Detailed information about the issue
- Action buttons to go back or navigate to homepage
- Responsive design that works on all devices

### API Route Errors

For API requests, JSON responses are returned with error details.

### Common Error Scenarios

#### 401 - Authentication Required
**Web:** Shows error page with "Authentication Required" message
**API:** Returns JSON with authentication error details
**Note:** This occurs when no SSO token is provided AND user is not already authenticated

#### 401 - Token Expired
**Web:** Shows error page with "Token Expired" message and auto-refresh option
**API:** Returns JSON with expiration details

#### 401 - Invalid Token
**Web:** Shows error page with "Invalid Token" message
**API:** Returns JSON with validation error

#### 404 - User Not Found
**Web:** Shows error page with user email details
**API:** Returns JSON with user lookup error

#### 403 - Account Not Active
**Web:** Shows error page with activation instructions
**API:** Returns JSON with account status error

#### 500 - Authentication Failed
**Web:** Shows error page with general authentication error
**API:** Returns JSON with server error details

### Error Page Features

- **Responsive Design:** Works on desktop, tablet, and mobile
- **User-Friendly Messages:** Clear explanations of what went wrong
- **Action Buttons:** Easy navigation options
- **Auto-Refresh:** For expired tokens, offers to refresh after 30 seconds
- **Status Codes:** Shows HTTP status code for debugging

## Configuration

### Environment Variables

Make sure these are set in your `.env` file:

```env
SSO_SECRET=your_shared_sso_secret_here
```

### Middleware Registration

The `SSOTokenAuth` middleware is automatically registered in `app/Http/Kernel.php`:

```php
'sso.token.auth' => \App\Http\Middleware\SSOTokenAuth::class,
```

## Integration with External Systems

### 1. CRM Integration

External CRM systems can generate SSO tokens and redirect users to specific pages:

```php
// In your CRM system
$ssoToken = generateSSOToken($userEmail);
$targetUrl = "https://your-module.com/vendor-console/whatsapp/contact/chat?sso_token=" . $ssoToken;
redirect($targetUrl);
```

### 2. API Integration

For programmatic access, you can make API calls with the SSO token:

```bash
curl -H "Authorization: Bearer YOUR_SSO_TOKEN" \
     https://your-domain.com/vendor-console/whatsapp/contact/chat
```

## Troubleshooting

### Common Issues

1. **Token Not Working**: Ensure the `SSO_SECRET` matches between systems
2. **User Not Found**: Verify the user exists and has the correct email
3. **Token Expired**: Generate a new token with a later expiration time
4. **Permission Denied**: Check if the user has the required role and status

### Debugging

Check the Laravel logs for detailed error messages:

```bash
tail -f storage/logs/laravel.log | grep "SSO Token Auth"
```

## Notes

- The sidebar and navbar are hidden for all users (including SSO token authenticated users)
- All routes return the same views as the normal authenticated routes
- The authentication is session-based, so users remain logged in for the duration of their session
- Tokens should be generated with appropriate expiration times based on your security requirements 