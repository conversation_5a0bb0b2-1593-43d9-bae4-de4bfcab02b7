<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Company;
use App\Yantrana\Components\Vendor\Models\VendorModel;
use App\Yantrana\Components\Vendor\Models\VendorUserModel;

class UserSyncController extends Controller
{
    /**
     * Handle sync request based on HTTP method
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleSync(Request $request)
    {
        $method = $request->method();
        
        switch ($method) {
            case 'POST':
                return $this->store($request);
            case 'PUT':
                // Extract email from request payload for update
                $email = $request->input('email');
                if (!$email) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Email is required in payload for update operation'
                    ], 422);
                }
                return $this->update($request, $email);
            case 'DELETE':
                // Extract email from request payload for delete
                $email = $request->input('email');
                if (!$email) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Email is required in payload for delete operation'
                    ], 422);
                }
                return $this->destroy($request, $email);
            default:
                return response()->json([
                    'status' => 'error',
                    'message' => 'Method not allowed. Use POST for create, PUT for update, DELETE for delete.'
                ], 405);
        }
    }

    /**
     * Sync user data from CRM
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get request ID from middleware if available
        $requestId = $request->get('_request_id', uniqid('sync_', true));
        
        // Log the incoming payload at the start of processing
        Log::info('UserSync: Processing incoming payload', [
            'request_id' => $requestId,
            'endpoint' => '/sync-user',
            'method' => $request->method(),
            'payload' => $request->all(),
            'content_type' => $request->header('Content-Type'),
            'content_length' => $request->header('Content-Length'),
            'ip' => $request->ip(),
            'timestamp' => now()->toISOString()
        ]);

        try {
            // Parse module_permissions if it's a JSON string
            $requestData = $request->all();
            if (isset($requestData['module_permissions']) && is_string($requestData['module_permissions'])) {
                $decodedPermissions = json_decode($requestData['module_permissions'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedPermissions)) {
                    $requestData['module_permissions'] = $decodedPermissions;
                    
                    Log::info('Parsed module_permissions from JSON string', [
                        'request_id' => $requestId,
                        'original' => $request->get('module_permissions'),
                        'parsed' => $decodedPermissions
                    ]);
                }
            }

            // Pre-process name field to first_name and last_name if needed
            if (isset($requestData['name']) && !empty($requestData['name']) && !isset($requestData['first_name'])) {
                $nameParts = explode(' ', trim($requestData['name']), 2);
                $requestData['first_name'] = $nameParts[0];
                $requestData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
                
                Log::info('Split name field into first_name and last_name', [
                    'request_id' => $requestId,
                    'original_name' => $requestData['name'],
                    'first_name' => $requestData['first_name'],
                    'last_name' => $requestData['last_name']
                ]);
            }

            // Validate request data
            $validator = Validator::make($requestData, [
                'email' => 'required|email',
                'name' => 'nullable|string|max:255', // Keep for backward compatibility but don't use in database
                'company_id' => 'nullable|integer', // Made nullable since we can create company from name
                'role' => 'required|string',
                'first_name' => 'required|string|max:255',
                'last_name' => 'nullable|string|max:255',
                'password' => 'nullable|string', // Accept password if provided
                'vendor_id' => 'nullable|integer',
                'module_permissions' => 'nullable|array',
                'status' => 'nullable|integer|in:0,1',
                'company_name' => 'nullable|string|max:255',
                'company_description' => 'nullable|string|max:1000', // Added validation for company description
                'super_admin_email' => 'nullable|email', // Required for company_admin role
                'company_email' => 'nullable|email', // Required for employee role to find vendor
            ]);

            // Custom validation: either 'name' or 'first_name' must be provided
            if (!isset($requestData['name']) && !isset($requestData['first_name'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => ['name' => ['Either name or first_name field is required']]
                ], 422);
            }

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Handle company creation first (before role-specific logic)
            $finalCompanyId = null;
            $company = null;
            
            // If company_name is provided, create or find the company first
            if (isset($data['company_name']) && !empty($data['company_name'])) {
                // First try to find existing company by name
                $company = Company::where('name', $data['company_name'])->first();
                
                if (!$company) {
                    // Create new company with name and description
                    $companyData = [
                        'name' => $data['company_name'],
                        'status' => 1,
                    ];
                    
                    // Add description if provided
                    if (isset($data['company_description']) && !empty($data['company_description'])) {
                        $companyData['description'] = $data['company_description'];
                    }
                    
                    // Add module permissions if provided
                    if (isset($data['module_permissions']) && is_array($data['module_permissions'])) {
                        $companyData['module_permissions'] = $data['module_permissions'];
                    }
                    
                    $company = Company::create($companyData);
                    
                    Log::info('Created new company from sync request', [
                        'request_id' => $requestId,
                        'company_id' => $company->_id,
                        'company_name' => $company->name,
                        'company_description' => $company->description,
                        'module_permissions' => $company->module_permissions
                    ]);
                } else {
                    Log::info('Found existing company by name', [
                        'request_id' => $requestId,
                        'company_id' => $company->_id,
                        'company_name' => $company->name
                    ]);
                }
                
                $finalCompanyId = $company->_id;
            } elseif (isset($data['company_id'])) {
                // Fallback to company_id if no company_name provided
                $company = Company::where('_id', $data['company_id'])->first();
                
                if (!$company) {
                    // Create company with fallback name if it doesn't exist
                    $company = Company::create([
                        'name' => 'Company ' . $data['company_id'],
                        'status' => 1,
                    ]);
                    
                    Log::info('Created company from company_id fallback', [
                        'request_id' => $requestId,
                        'external_company_id' => $data['company_id'],
                        'generated_company_id' => $company->_id,
                        'company_name' => $company->name
                    ]);
                }
                
                $finalCompanyId = $company->_id;
            }

            // Handle role-specific logic
            $vendorId = null;
            
            if (strtolower($data['role']) === 'company_admin') {
                // For company_admin, we need super_admin_email to find the company
                if (!isset($data['super_admin_email']) || empty($data['super_admin_email'])) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'super_admin_email is required for company_admin role'
                    ], 422);
                }
                
                // Find super admin by email
                $superAdmin = User::where('email', $data['super_admin_email'])
                                  ->where('user_roles__id', 1) // super_admin role
                                  ->first();
                
                if (!$superAdmin) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Super admin not found with email: ' . $data['super_admin_email']
                    ], 404);
                }
                
                $finalCompanyId = $superAdmin->company_id;
                
                Log::info('Found super admin for company_admin', [
                    'super_admin_email' => $data['super_admin_email'],
                    'super_admin_id' => $superAdmin->_id,
                    'company_id' => $finalCompanyId
                ]);
                
                // Find or create vendor for this company
                $vendor = VendorModel::where('company_id', $finalCompanyId)->first();
                
                if (!$vendor) {
                    // Create vendor for the company
                    $vendor = VendorModel::create([
                        '_uid' => Str::random(32),
                        'title' => $superAdmin->company->name ?? 'Vendor for Company ' . $finalCompanyId,
                        'slug' => Str::slug(($superAdmin->company->name ?? 'vendor-company-' . $finalCompanyId) . '-' . Str::random(8)),
                        'company_id' => $finalCompanyId,
                        'status' => 1,
                    ]);
                    
                    Log::info('Created vendor for company_admin', [
                        'vendor_id' => $vendor->_id,
                        'vendor_uid' => $vendor->_uid,
                        'company_id' => $finalCompanyId,
                        'vendor_title' => $vendor->title
                    ]);
                } else {
                    Log::info('Found existing vendor for company_admin', [
                        'vendor_id' => $vendor->_id,
                        'vendor_uid' => $vendor->_uid,
                        'company_id' => $finalCompanyId
                    ]);
                }
                
                $vendorId = $vendor->_id;
            } elseif (strtolower($data['role']) === 'employee' && isset($data['company_email'])) {
                // For employees, find vendor by company_email (which should be a company admin or super admin)
                $companyUser = User::where('email', $data['company_email'])
                                   ->whereIn('user_roles__id', [1, 2]) // super_admin or vendor_admin
                                   ->first();
                
                if (!$companyUser) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Company user not found with email: ' . $data['company_email']
                    ], 404);
                }
                
                // If it's a super admin, get their company and find/create vendor
                if ($companyUser->user_roles__id == 1) {
                    $finalCompanyId = $companyUser->company_id;
                    
                    // Find or create vendor for this company
                    $vendor = VendorModel::where('company_id', $finalCompanyId)->first();
                    
                    if (!$vendor) {
                        // Create vendor for the company
                        $vendor = VendorModel::create([
                            '_uid' => Str::random(32),
                            'title' => $companyUser->company->name ?? 'Vendor for Company ' . $finalCompanyId,
                            'slug' => Str::slug(($companyUser->company->name ?? 'vendor-company-' . $finalCompanyId) . '-' . Str::random(8)),
                            'company_id' => $finalCompanyId,
                            'status' => 1,
                        ]);
                        
                        Log::info('Created vendor for employee via super admin', [
                            'vendor_id' => $vendor->_id,
                            'vendor_uid' => $vendor->_uid,
                            'company_id' => $finalCompanyId,
                            'company_email' => $data['company_email']
                        ]);
                    }
                    
                    $vendorId = $vendor->_id;
                } else {
                    // If it's a vendor admin, use their vendor directly
                    $finalCompanyId = $companyUser->company_id;
                    $vendorId = $companyUser->vendors__id;
                    
                    Log::info('Found vendor for employee via company admin', [
                        'vendor_id' => $vendorId,
                        'company_id' => $finalCompanyId,
                        'company_email' => $data['company_email']
                    ]);
                }
            }
            
            // Ensure we have a company_id for user creation
            if (!$finalCompanyId) {
                Log::warning('No company information provided in sync request', [
                    'request_id' => $requestId,
                    'email' => $data['email'],
                    'role' => $data['role']
                ]);
                
                return response()->json([
                    'status' => 'error',
                    'message' => 'Company information is required. Please provide either company_name or company_id.'
                ], 422);
            }

            // Prepare user data - removed 'name' field as it doesn't exist in database
            $userData = [
                '_uid' => Str::random(32), // Generate UID for new users
                'email' => $data['email'],
                'username' => $data['email'], // Generate username from email if not provided
                'company_id' => $finalCompanyId,
                'user_roles__id' => $this->mapRole($data['role']),
                'status' => $data['status'] ?? 1,
                'first_name' => $data['first_name'] ?? ($data['name'] ? explode(' ', $data['name'])[0] : ''),
                'last_name' => $data['last_name'] ?? ($data['name'] ? (explode(' ', $data['name'])[1] ?? '') : ''),
            ];

            // Add vendor_id if provided or if it's a company_admin
            if (isset($data['vendor_id'])) {
                $userData['vendors__id'] = $data['vendor_id'];
            } elseif ($vendorId) {
                // For company_admin, assign the created/found vendor
                $userData['vendors__id'] = $vendorId;
            }

            // Add module permissions if provided
            if (isset($data['module_permissions'])) {
                $userData['module_permissions'] = $data['module_permissions'];
            }

            // Find existing user or create new one
            $user = User::where('email', $data['email'])->first();

            if ($user) {
                // Update existing user - remove _uid from update data since it shouldn't change
                unset($userData['_uid']);
                
                // If password is provided, use it; otherwise don't update password
                if (isset($data['password']) && !empty($data['password'])) {
                    // Check if password is already hashed (starts with $2y$)
                    if (strpos($data['password'], '$2y$') === 0) {
                        $userData['password'] = $data['password']; // Already hashed
                    } else {
                        $userData['password'] = Hash::make($data['password']); // Hash plain password
                    }
                } else {
                    // Don't update password if not provided
                    unset($userData['password']);
                }
                
                $user->update($userData);
                $action = 'updated';
            } else {
                // Create new user
                if (isset($data['password']) && !empty($data['password'])) {
                    // Check if password is already hashed (starts with $2y$)
                    if (strpos($data['password'], '$2y$') === 0) {
                        $userData['password'] = $data['password']; // Already hashed
                    } else {
                        $userData['password'] = Hash::make($data['password']); // Hash plain password
                    }
                } else {
                    // Generate random password if not provided
                    $userData['password'] = Hash::make(Str::random(16));
                }
                
                $user = User::create($userData);
                $action = 'created';
            }

            // Create or update vendor_users entry if user has a vendor
            if ($user->vendors__id) {
                $vendorUser = VendorUserModel::where([
                    'users__id' => $user->_id,
                    'vendors__id' => $user->vendors__id
                ])->first();
                
                if (!$vendorUser) {
                    // Create vendor_user entry
                    $vendorUserData = [
                        'users__id' => $user->_id,
                        'vendors__id' => $user->vendors__id,
                        '__data' => [
                            'permissions' => [
                                'messaging' => 'allow',
                                'contacts' => 'allow',
                                'campaigns' => 'allow',
                                'bot_replies' => 'allow',
                                'templates' => 'allow',
                            ]
                        ]
                    ];
                    
                    VendorUserModel::create($vendorUserData);
                    
                    Log::info('Created vendor_user entry', [
                        'user_id' => $user->_id,
                        'vendor_id' => $user->vendors__id,
                        'email' => $user->email
                    ]);
                } else {
                    Log::info('Vendor_user entry already exists', [
                        'user_id' => $user->_id,
                        'vendor_id' => $user->vendors__id,
                        'email' => $user->email
                    ]);
                }
            }

            Log::info('User synced from CRM', [
                'action' => $action,
                'user_id' => $user->id,
                'email' => $user->email,
                'company_id' => $user->company_id,
                'vendor_id' => $user->vendors__id,
                'role' => $this->getRoleName($user->user_roles__id),
                'module_permissions' => $user->module_permissions
            ]);

            return response()->json([
                'status' => 'user synced',
                'action' => $action,
                'user' => [
                    'id' => $user->id,
                    'email' => $user->email,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'full_name' => trim($user->first_name . ' ' . $user->last_name),
                    'company_id' => $user->company_id,
                    'vendor_id' => $user->vendors__id,
                    'role' => $this->getRoleName($user->user_roles__id),
                ]
            ], 200);

        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('Database error during user sync: ' . $e->getMessage(), [
                'request_id' => $requestId,
                'request_data' => $request->all(),
                'sql_error_code' => $e->getCode(),
                'sql_message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Database error during user sync',
                'request_id' => $requestId,
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        } catch (\Exception $e) {
            Log::error('User sync error: ' . $e->getMessage(), [
                'request_id' => $requestId,
                'request_data' => $request->all(),
                'error_type' => get_class($e),
                'error_message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'User sync failed',
                'request_id' => $requestId,
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Map external role to internal role ID
     *
     * @param string $role
     * @return int
     */
    private function mapRole($role)
    {
        $roleMap = [
            'system_admin' => 4,
            'super_admin' => 1,
            'vendor_admin' => 2,
            'company_admin' => 2, // company_admin maps to vendor_admin role
            'employee' => 3, // employee maps to user role
            'user' => 3,
            'admin' => 2, // fallback for admin
        ];

        return $roleMap[strtolower($role)] ?? 3; // default to user role
    }

    /**
     * Update user data from CRM
     *
     * @param Request $request
     * @param string $userEmail
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $userEmail)
    {
        // Get request ID from middleware if available
        $requestId = $request->get('_request_id', uniqid('update_', true));
        
        // Log the incoming payload at the start of processing
        Log::info('UserSync: Processing update request', [
            'request_id' => $requestId,
            'user_email' => $userEmail,
            'endpoint' => '/sync-user/update',
            'method' => $request->method(),
            'payload' => $request->all(),
            'ip' => $request->ip(),
            'timestamp' => now()->toISOString()
        ]);

        try {
            // Find the user by email
            $user = User::where('email', $userEmail)->first();
            
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User not found',
                    'user_email' => $userEmail
                ], 404);
            }

            // Parse module_permissions if it's a JSON string
            $requestData = $request->all();
            if (isset($requestData['module_permissions']) && is_string($requestData['module_permissions'])) {
                $decodedPermissions = json_decode($requestData['module_permissions'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedPermissions)) {
                    $requestData['module_permissions'] = $decodedPermissions;
                    
                    Log::info('Parsed module_permissions from JSON string', [
                        'request_id' => $requestId,
                        'original' => $request->get('module_permissions'),
                        'parsed' => $decodedPermissions
                    ]);
                }
            }

            // Pre-process name field to first_name and last_name if needed for updates
            if (isset($requestData['name']) && !empty($requestData['name']) && !isset($requestData['first_name'])) {
                $nameParts = explode(' ', trim($requestData['name']), 2);
                $requestData['first_name'] = $nameParts[0];
                $requestData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
                
                Log::info('Split name field into first_name and last_name during update', [
                    'request_id' => $requestId,
                    'original_name' => $requestData['name'],
                    'first_name' => $requestData['first_name'],
                    'last_name' => $requestData['last_name']
                ]);
            }

            // Validate request data - all fields are optional for updates
            $validator = Validator::make($requestData, [
                'email' => 'sometimes|email|unique:users,email,' . $user->_id . ',_id',
                'name' => 'nullable|string|max:255',
                'company_id' => 'nullable|integer',
                'role' => 'sometimes|string',
                'first_name' => 'sometimes|string|max:255',
                'last_name' => 'nullable|string|max:255',
                'password' => 'nullable|string',
                'vendor_id' => 'nullable|integer',
                'module_permissions' => 'nullable|array',
                'status' => 'nullable|integer|in:0,1',
                'company_name' => 'nullable|string|max:255',
                'company_description' => 'nullable|string|max:1000',
                'super_admin_email' => 'nullable|email',
                'company_email' => 'nullable|email',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Handle company updates if provided
            $finalCompanyId = $user->company_id; // Keep existing company by default
            $company = null;
            
            if (isset($data['company_name']) && !empty($data['company_name'])) {
                $company = Company::where('name', $data['company_name'])->first();
                
                if (!$company) {
                    $companyData = [
                        'name' => $data['company_name'],
                        'status' => 1,
                    ];
                    
                    if (isset($data['company_description']) && !empty($data['company_description'])) {
                        $companyData['description'] = $data['company_description'];
                    }
                    
                    if (isset($data['module_permissions']) && is_array($data['module_permissions'])) {
                        $companyData['module_permissions'] = $data['module_permissions'];
                    }
                    
                    $company = Company::create($companyData);
                    
                    Log::info('Created new company during user update', [
                        'request_id' => $requestId,
                        'company_id' => $company->_id,
                        'company_name' => $company->name,
                        'user_id' => $user->_id
                    ]);
                }
                
                $finalCompanyId = $company->_id;
            } elseif (isset($data['company_id'])) {
                $company = Company::where('_id', $data['company_id'])->first();
                
                if (!$company) {
                    $company = Company::create([
                        'name' => 'Company ' . $data['company_id'],
                        'status' => 1,
                    ]);
                }
                
                $finalCompanyId = $company->_id;
            }

            // Handle role-specific logic if role is being updated
            $vendorId = $user->vendors__id; // Keep existing vendor by default
            
            if (isset($data['role'])) {
                if (strtolower($data['role']) === 'company_admin') {
                    if (!isset($data['super_admin_email']) || empty($data['super_admin_email'])) {
                        return response()->json([
                            'status' => 'error',
                            'message' => 'super_admin_email is required for company_admin role'
                        ], 422);
                    }
                    
                    $superAdmin = User::where('email', $data['super_admin_email'])
                                      ->where('user_roles__id', 1)
                                      ->first();
                    
                    if (!$superAdmin) {
                        return response()->json([
                            'status' => 'error',
                            'message' => 'Super admin not found with email: ' . $data['super_admin_email']
                        ], 404);
                    }
                    
                    $finalCompanyId = $superAdmin->company_id;
                    
                    $vendor = VendorModel::where('company_id', $finalCompanyId)->first();
                    
                    if (!$vendor) {
                        $vendor = VendorModel::create([
                            '_uid' => Str::random(32),
                            'title' => $superAdmin->company->name ?? 'Vendor for Company ' . $finalCompanyId,
                            'slug' => Str::slug(($superAdmin->company->name ?? 'vendor-company-' . $finalCompanyId) . '-' . Str::random(8)),
                            'company_id' => $finalCompanyId,
                            'status' => 1,
                        ]);
                    }
                    
                    $vendorId = $vendor->_id;
                } elseif (strtolower($data['role']) === 'employee' && isset($data['company_email'])) {
                    $companyUser = User::where('email', $data['company_email'])
                                       ->whereIn('user_roles__id', [1, 2])
                                       ->first();
                    
                    if (!$companyUser) {
                        return response()->json([
                            'status' => 'error',
                            'message' => 'Company user not found with email: ' . $data['company_email']
                        ], 404);
                    }
                    
                    if ($companyUser->user_roles__id == 1) {
                        $finalCompanyId = $companyUser->company_id;
                        
                        $vendor = VendorModel::where('company_id', $finalCompanyId)->first();
                        
                        if (!$vendor) {
                            $vendor = VendorModel::create([
                                '_uid' => Str::random(32),
                                'title' => $companyUser->company->name ?? 'Vendor for Company ' . $finalCompanyId,
                                'slug' => Str::slug(($companyUser->company->name ?? 'vendor-company-' . $finalCompanyId) . '-' . Str::random(8)),
                                'company_id' => $finalCompanyId,
                                'status' => 1,
                            ]);
                        }
                        
                        $vendorId = $vendor->_id;
                    } else {
                        $finalCompanyId = $companyUser->company_id;
                        $vendorId = $companyUser->vendors__id;
                    }
                }
            }

            // Prepare user update data
            $userData = [];
            
            if (isset($data['email'])) {
                $userData['email'] = $data['email'];
                $userData['username'] = $data['email'];
            }
            
            if ($finalCompanyId !== $user->company_id) {
                $userData['company_id'] = $finalCompanyId;
            }
            
            if (isset($data['role'])) {
                $userData['user_roles__id'] = $this->mapRole($data['role']);
            }
            
            if (isset($data['status'])) {
                $userData['status'] = $data['status'];
            }
            
            if (isset($data['first_name'])) {
                $userData['first_name'] = $data['first_name'];
            }
            
            if (isset($data['last_name'])) {
                $userData['last_name'] = $data['last_name'];
            }
            
            if (isset($data['vendor_id'])) {
                $userData['vendors__id'] = $data['vendor_id'];
            } elseif ($vendorId !== $user->vendors__id) {
                $userData['vendors__id'] = $vendorId;
            }
            
            if (isset($data['module_permissions'])) {
                $userData['module_permissions'] = $data['module_permissions'];
            }

            // Handle password update
            if (isset($data['password']) && !empty($data['password'])) {
                if (strpos($data['password'], '$2y$') === 0) {
                    $userData['password'] = $data['password'];
                } else {
                    $userData['password'] = Hash::make($data['password']);
                }
            }

            // Update user if there are changes
            if (!empty($userData)) {
                $user->update($userData);
            }

            // Update or create vendor_users entry if vendor changed
            if (isset($userData['vendors__id']) && $userData['vendors__id']) {
                // Remove old vendor_user entry if exists
                VendorUserModel::where('users__id', $user->_id)->delete();
                
                // Create new vendor_user entry
                $vendorUserData = [
                    'users__id' => $user->_id,
                    'vendors__id' => $userData['vendors__id'],
                    '__data' => [
                        'permissions' => [
                            'messaging' => 'allow',
                            'contacts' => 'allow',
                            'campaigns' => 'allow',
                            'bot_replies' => 'allow',
                            'templates' => 'allow',
                        ]
                    ]
                ];
                
                VendorUserModel::create($vendorUserData);
                
                Log::info('Updated vendor_user entry during user update', [
                    'user_id' => $user->_id,
                    'old_vendor_id' => $user->vendors__id,
                    'new_vendor_id' => $userData['vendors__id'],
                    'email' => $user->email
                ]);
            }

            // Refresh user model to get updated data
            $user->refresh();

            Log::info('User updated from CRM', [
                'request_id' => $requestId,
                'user_id' => $user->_id,
                'email' => $user->email,
                'company_id' => $user->company_id,
                'vendor_id' => $user->vendors__id,
                'role' => $this->getRoleName($user->user_roles__id),
                'changes' => $userData
            ]);

            return response()->json([
                'status' => 'user updated',
                'user' => [
                    'id' => $user->_id,
                    'email' => $user->email,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'full_name' => trim($user->first_name . ' ' . $user->last_name),
                    'company_id' => $user->company_id,
                    'vendor_id' => $user->vendors__id,
                    'role' => $this->getRoleName($user->user_roles__id),
                ]
            ], 200);

        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('Database error during user update: ' . $e->getMessage(), [
                'request_id' => $requestId,
                'user_email' => $userEmail,
                'request_data' => $request->all(),
                'sql_error_code' => $e->getCode(),
                'sql_message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Database error during user update',
                'request_id' => $requestId,
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        } catch (\Exception $e) {
            Log::error('User update error: ' . $e->getMessage(), [
                'request_id' => $requestId,
                'user_email' => $userEmail,
                'request_data' => $request->all(),
                'error_type' => get_class($e),
                'error_message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'User update failed',
                'request_id' => $requestId,
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Delete user from CRM
     *
     * @param Request $request
     * @param string $userEmail
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $userEmail)
    {
        // Get request ID from middleware if available
        $requestId = $request->get('_request_id', uniqid('delete_', true));
        
        // Log the incoming request
        Log::info('UserSync: Processing delete request', [
            'request_id' => $requestId,
            'user_email' => $userEmail,
            'endpoint' => '/sync-user/delete',
            'method' => $request->method(),
            'ip' => $request->ip(),
            'timestamp' => now()->toISOString()
        ]);

        try {
            // Find the user by email
            $user = User::where('email', $userEmail)->first();
            
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User not found',
                    'user_email' => $userEmail
                ], 404);
            }

            // Check if user is a super admin and has dependent users
            if ($user->user_roles__id == 1) { // super_admin
                $dependentUsers = User::where('company_id', $user->company_id)
                                     ->where('_id', '!=', $user->_id)
                                     ->whereIn('user_roles__id', [2, 3]) // vendor_admin and users
                                     ->count();
                
                if ($dependentUsers > 0) {
                    // Check if force delete is requested
                    $forceDelete = $request->input('force_delete', false);
                    
                    if (!$forceDelete) {
                        return response()->json([
                            'status' => 'error',
                            'message' => 'Cannot delete super admin with dependent users. This will affect ' . $dependentUsers . ' users.',
                            'dependent_users_count' => $dependentUsers,
                            'suggestion' => 'Use force_delete=true to delete anyway, or transfer users to another company first.'
                        ], 422);
                    }
                    
                    Log::warning('Force deleting super admin with dependent users', [
                        'request_id' => $requestId,
                        'super_admin_id' => $user->_id,
                        'super_admin_email' => $user->email,
                        'company_id' => $user->company_id,
                        'dependent_users_count' => $dependentUsers
                    ]);
                }
            }

            // Store user info for logging before deletion
            $userInfo = [
                'id' => $user->_id,
                'email' => $user->email,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'company_id' => $user->company_id,
                'vendor_id' => $user->vendors__id,
                'role' => $this->getRoleName($user->user_roles__id),
            ];

            // Remove vendor_user entries first
            if ($user->vendors__id) {
                VendorUserModel::where('users__id', $user->_id)->delete();
                
                Log::info('Removed vendor_user entries for deleted user', [
                    'request_id' => $requestId,
                    'user_id' => $user->_id,
                    'vendor_id' => $user->vendors__id
                ]);
            }

            // If this is a super admin being deleted, handle company cleanup
            if ($user->user_roles__id == 1) {
                $company = Company::find($user->company_id);
                
                if ($company) {
                    // Check if there are other super admins for this company
                    $otherSuperAdmins = User::where('company_id', $user->company_id)
                                           ->where('user_roles__id', 1)
                                           ->where('_id', '!=', $user->_id)
                                           ->count();
                    
                    if ($otherSuperAdmins == 0) {
                        // This is the last super admin, handle dependent users
                        $dependentUsers = User::where('company_id', $user->company_id)
                                             ->where('_id', '!=', $user->_id)
                                             ->get();
                        
                        foreach ($dependentUsers as $dependentUser) {
                            // Remove vendor associations for dependent users
                            if ($dependentUser->vendors__id) {
                                VendorUserModel::where('users__id', $dependentUser->_id)->delete();
                                $dependentUser->update(['vendors__id' => null]);
                            }
                            
                            // Set company_id to null (will be handled by foreign key constraint)
                            $dependentUser->update(['company_id' => null]);
                            
                            Log::info('Cleaned up dependent user after super admin deletion', [
                                'request_id' => $requestId,
                                'dependent_user_id' => $dependentUser->_id,
                                'dependent_user_email' => $dependentUser->email,
                                'removed_company_id' => $user->company_id
                            ]);
                        }
                        
                        // Clean up vendors associated with this company
                        $vendors = VendorModel::where('company_id', $user->company_id)->get();
                        foreach ($vendors as $vendor) {
                            // Remove all vendor_user entries for this vendor
                            VendorUserModel::where('vendors__id', $vendor->_id)->delete();
                            
                            Log::info('Cleaned up vendor after super admin deletion', [
                                'request_id' => $requestId,
                                'vendor_id' => $vendor->_id,
                                'vendor_title' => $vendor->title,
                                'company_id' => $user->company_id
                            ]);
                        }
                        
                        // Note: Company will be handled by foreign key constraint (set null)
                        Log::info('Last super admin deleted, company relationships cleaned up', [
                            'request_id' => $requestId,
                            'company_id' => $user->company_id,
                            'company_name' => $company->name,
                            'affected_users' => $dependentUsers->count(),
                            'affected_vendors' => $vendors->count()
                        ]);
                    }
                }
            }

            // Delete the user
            $user->delete();

            Log::info('User deleted from CRM', [
                'request_id' => $requestId,
                'deleted_user' => $userInfo,
                'deletion_timestamp' => now()->toISOString()
            ]);

            return response()->json([
                'status' => 'user deleted',
                'deleted_user' => $userInfo,
                'message' => 'User successfully deleted'
            ], 200);

        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('Database error during user deletion: ' . $e->getMessage(), [
                'request_id' => $requestId,
                'user_email' => $userEmail,
                'sql_error_code' => $e->getCode(),
                'sql_message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Database error during user deletion',
                'request_id' => $requestId,
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        } catch (\Exception $e) {
            Log::error('User deletion error: ' . $e->getMessage(), [
                'request_id' => $requestId,
                'user_email' => $userEmail,
                'error_type' => get_class($e),
                'error_message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'User deletion failed',
                'request_id' => $requestId,
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get role name from role ID
     *
     * @param int $roleId
     * @return string
     */
    private function getRoleName($roleId)
    {
        $roleNames = [
            4 => 'system_admin',
            1 => 'super_admin',
            2 => 'vendor_admin',
            3 => 'user',
        ];

        return $roleNames[$roleId] ?? 'user';
    }
}
