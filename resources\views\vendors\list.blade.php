@extends('layouts.app', ['title' => hasSystemAdminAccess() ? __tr('Super Admins') : __tr('Users')])

@section('content')
@include('users.partials.header', [
'title' => (hasSystemAdminAccess() ? __tr('Super Admins') : __tr('Users')) . ' '. auth()->user()->name,
'description' => '',
'class' => 'col-lg-7'
])
<style>
    th{
        background-color:rgb(11, 119, 83) !important;
    }
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-xl-12 mb-3 mt-md--5">
            <button type="button" class="btn btn-primary mt-5" data-toggle="modal" data-target="#addVendorModal">
                @if(hasSystemAdminAccess())
                    <?= __tr('Add New Super Admin') ?>
                @else
                    <?= __tr('Add New User') ?>
                @endif
            </button>
        </div>
    <div class="d-flex justify-content-between align-items-center flex-wrap mt-5">
        <!-- Left: Title -->
        <h1 class="page-title mb-0" style="color: #22A755;">
            <i class="fa fa-users"></i> {{ __tr(' Users') }}
        </h1>

        <!-- Right: Button -->
        <button type="button" class="btn mt-3 mt-md-0 text-white"
                data-toggle="modal"
                data-target="#addVendorModal"
                style="background-color: #1e7c4a; border-color: #1e7c4a; transition: all 0.3s ease;">
            <i class="fas fa-user-plus me-2"></i>{{ __tr(' Add New User') }}
        </button>

        <!-- Optional Hover Animation -->
        <style>
            button[data-target="#addVendorModal"]:hover {
                background-color: #18643c;
                border-color: #18643c;
                transform: scale(1.02);
            }
        </style>

    </div>
</div>

        <div class="col-xl-12">
           <x-lw.datatable id="lwManageVendorsTable" :url="route('central.vendors.read.list')" data-page-length="100">
                <th data-template="#titleExtendedButtons" data-orderable="true" data-name="title">
                    <?= hasSystemAdminAccess() ? __tr('Super Admin') : __tr('User') ?>
                </th>
                <th data-template="#lwQuickActionButtons" data-orderable="true" data-name="title">
                    <?= __tr('Quick Actions') ?>
                </th>
                <th data-orderable="true" data-name="fullName">
                    <?= hasSystemAdminAccess() ? __tr('Name') : __tr('Admin Name') ?>
                </th>
                
                <th data-orderable="true" data-name="email">
                    <?= __tr('email') ?>
                </th>
                <th data-orderable="true" data-name="status">
                    <?= __tr('status') ?>
                </th>
                <th data-orderable="true" data-name="mobile_number">
                    <?= __tr('Mobile Number') ?>
                </th>
                <th data-orderable="true" data-name="user_status">
                    <?= hasSystemAdminAccess() ? __tr('Super Admin Status') : __tr('Admin Status') ?>
                </th>
                <th data-orderable="true" data-name="created_at">
                    <?= __tr('Created On') ?>
                </th>
                <th data-template="#actionButtons" name="null">
                    <?= __tr('Action') ?>
                </th>
            </x-lw.datatable>
        </div>
    </div>
    <script type="text/template" id="titleExtendedButtons">
        <a  href ="<%= __Utils.apiURL("{{ route('vendor.dashboard',['vendorIdOrUid'=>'vendorIdOrUid'])}}", {'vendorIdOrUid':__tData._uid}) %>"> <%-__tData.title %> </a> 
    </script>
    <script type="text/template" id="lwQuickActionButtons">
        @if(hasSystemAdminAccess())
        <a data-method="post" href="<%= __Utils.apiURL("{{ route('central.vendors.user.write.login_as', [ 'vendorUid']) }}", {'vendorUid': __tData._uid}) %>" class="btn btn-outline-success btn-sm lw-ajax-link-action" title="{{ __tr('Login as Super Admin') }}"><i class="fa fa-sign-in-alt"></i> {{  __tr('Login') }}</a>
        @else
        <a data-method="post" href="<%= __Utils.apiURL("{{ route('central.vendors.user.write.login_as', [ 'vendorUid']) }}", {'vendorUid': __tData._uid}) %>" class="btn btn-outline-success btn-sm lw-ajax-link-action"  title="{{ __tr('Login as Vendor Admin') }}"><i class="fa fa-sign-in-alt"></i> {{  __tr('Login') }}</a>
        <a class="btn btn-info btn-sm" href ="<%= __Utils.apiURL("{{ route('central.vendor.details',['vendorIdOrUid'=>'vendorIdOrUid'])}}", {'vendorIdOrUid':__tData._uid}) %>"> {{  __tr('Subscription') }} </a>
        @endif
    </script>
    <script type="text/template" id="actionButtons">
        <!-- EDIT ACTION -->
        @if(hasSystemAdminAccess())
        <a data-pre-callback="appFuncs.clearContainer" title="{{ __tr('Edit') }}" class="lw-btn btn btn-sm btn-default lw-ajax-link-action" data-response-template="#lwEditVendorBody" href="<%= __Utils.apiURL("{{ route('system.admin.super_admins.read.update.data', ['superAdminUid']) }}", {'superAdminUid': __tData._uid}) %>" data-toggle="modal" data-target="#lwEditVendor"><i class="fa fa-edit"></i> {{ __tr('Edit') }}</a>
        @else
        <a data-pre-callback="appFuncs.clearContainer" title="{{ __tr('Edit') }}" class="lw-btn btn btn-sm btn-default lw-ajax-link-action" data-response-template="#lwEditVendorBody" href="<%= __Utils.apiURL("{{ route('vendor.read.update.data', ['vendorIdOrUid']) }}", {'vendorIdOrUid': __tData._uid}) %>" data-toggle="modal" data-target="#lwEditVendor"><i class="fa fa-edit"></i> {{ __tr('Edit') }}</a>
        @endif
        <% if(__tData.status_code != 5 ) { %>
        <!--  DELETE ACTION -->
        <a data-method="post" href="<%= __Utils.apiURL("{{ route('vendor.delete', ['vendorIdOrUid']) }}", {'vendorIdOrUid': __tData._uid}) %>" class="btn btn-outline-warning btn-sm lw-ajax-link-action-via-confirm" data-confirm="#lwSoftDeleteVendor-template" title="{{ __tr('Soft Delete') }}" data-toggle="modal" data-target="#deletePlan" data-callback-params="{{ json_encode(['modalId' => '#lwSoftDeleteVendor-template','datatableId' => '#lwManageVendorsTable']) }}" data-callback="appFuncs.modelSuccessCallback"><i class="fa fa-trash"></i> {{ __tr('Soft Delete') }}</a>
        <!--  PASSWORD ACTION -->
        <% } %>
        <a data-pre-callback="appFuncs.clearContainer" title="{{ __tr('Change Password') }}" class="lw-btn btn btn-sm btn-dark lw-ajax-link-action" data-response-template="#lwChangePasswordBody" href="<%= __Utils.apiURL(" {{ route('vendor.change.password.data',['vendorIdOrUid']) }}", {'vendorIdOrUid': __tData.userId}) %>" data-toggle="modal" data-target="#lwChangePasswordAuthor"><i class="fas fa-key"></i> {{ __tr('Change Password') }}</a>
           <!-- PERMANANT DELTE ACTION -->
        <a data-method="post" href="<%= __Utils.apiURL("{{ route('vendor.permanant.delete', ['vendorIdOrUid']) }}", {'vendorIdOrUid': __tData._uid}) %>" class="btn btn-outline-danger btn-sm lw-ajax-link-action-via-confirm" data-confirm="#lwDeleteVendor-template" title="{{ __tr('Delete') }}" data-toggle="modal" data-target="#deletePlan" data-callback-params="{{ json_encode(['modalId' => '#lwDeleteVendor-template','datatableId' => '#lwManageVendorsTable']) }}" data-callback="appFuncs.modelSuccessCallback"><i class="fa fa-trash"></i> {{ __tr('Delete') }}</a>
           <!--  /PERMANANT DELTE ACTION -->
    </script>
    
    <!-- VENDOR DELETE TEMPLATE -->
    <script type="text/template" id="lwSoftDeleteVendor-template">
        <h2><?= __tr('Are You Sure!') ?></h2>
            <p><?= hasSystemAdminAccess() ? __tr('You want to Soft delete this Super Admin?') : __tr('You want to Soft delete this Vendor?') ?></p>
    </script>
    <!-- /VENDOR DELETE TEMPLATE -->
     <!-- VENDOR PERMANENT DELETE TEMPLATE -->
     <script type="text/template" id="lwDeleteVendor-template">
        <h2><?= __tr('Are You Sure!') ?></h2>
            <p><?= hasSystemAdminAccess() ? __tr('You want to delete this Super Admin permanently?') : __tr('You want to delete this Vendor, It will delete vendor and its data permanently ?') ?></p>
    </script>
    <!-- /VENDOR PERMANENT DELETE TEMPLATE -->
    {{-- ADD VENDOR/SUPER ADMIN MODAL --}}
    <x-lw.modal id="addVendorModal" :header="hasSystemAdminAccess() ? __tr('Add New Super Admin') : __tr('Add New User')" :hasForm="true">
        {{-- FORM START --}}
        <x-lw.form :action="route('central.vendors.write.add')" data-callback="afterSuccessfullyCreated">
            <div class="lw-form-modal-body">
                {{-- VENDOR TITLE --}}
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user-alt"></i></span>
                        </div>
                        <input class="form-control" placeholder="{{ __tr('User Title') }}" type="text"
                            name="vendor_title" value="{{ old('vendor_title') }}" required>
                    </div>
                </div>
                
                @if(hasSystemAdminAccess())
                {{-- COMPANY NAME --}}
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-building"></i></span>
                        </div>
                        <input class="form-control" placeholder="{{ __tr('Company Name') }}" type="text"
                            name="company_name" value="{{ old('company_name') }}" required>
                    </div>
                </div>
                {{-- COMPANY DESCRIPTION --}}
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                        </div>
                        <textarea class="form-control" placeholder="{{ __tr('Company Description') }}" 
                            name="company_description" rows="3">{{ old('company_description') }}</textarea>
                    </div>
                </div>
                @endif
                {{-- USER TYPE TITLE --}}
                <div class="text-center text-muted mb-4 ">
                    @if(hasSystemAdminAccess())
                        {{ __tr('Super Admin User') }}
                    @else
                        {{ __tr('Admin User') }}
                    @endif
                </div>
                {{-- USERNAME --}}
                {{-- FIRSTNAME --}}
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                        </div>
                        <input class="form-control" placeholder="{{ __tr('First Name') }}" type="text" name="first_name"
                            value="{{ old('first_name') }}" required>
                    </div>
                </div>
                {{-- FIRSTNAME --}}
                {{-- LASTNAME --}}
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                        </div>
                        <input class="form-control" placeholder="{{ __tr('Last Name') }}" type="text" name="last_name"
                            value="{{ old('last_name') }}" required>
                    </div>
                </div>
                {{-- /LASTNAME --}}
                {{-- MOBILE NUMBER --}}
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fas fa-mobile-alt"></i></span>
                        </div>
                        <input class="form-control" placeholder="{{ __tr('Mobile Number') }}" type="number" name="mobile_number" required >
                    </div>
                    <h5><span class="text-muted">{{__tr("Mobile number should be with country code without 0 or +")}}</span></h5>
                </div>
               
                {{-- /MOBILE NUMBER --}}
                {{-- EMAIL --}}
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-at"></i></span>
                        </div>
                        <input class="form-control" placeholder="{{ __tr('Email') }}" type="email" name="email" required >
                    </div>
                </div>
                {{-- /EMAIL --}}
                {{-- PASSWORD --}}
                <div class="form-group">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-key"></i></span>
                        </div>
                        <input class="form-control" name="password" placeholder="{{ __tr('Password') }}" type="password"
                            required>
                    </div>
                </div>
                {{-- PASSWORD --}}
                {{-- CONFIRM PASSWORD --}}
                <div class="form-group">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-key"></i></span>
                        </div>
                        <input class="form-control" placeholder="{{ __tr('Confirm Password') }}" type="password"
                            name="password_confirmation" required>
                    </div>
                </div>
                {{-- CONFIRM PASSWORD --}}

                @if(hasCentralAccess())
                {{-- MODULE PERMISSIONS SECTION --}}
                <div class="form-group mt-4">
                    <label class="form-control-label">{{ __tr('Module Permissions') }}</label>
                    <div class="card border">
                        <div class="card-body p-3">
                            <div class="row">
                                @if(hasSystemAdminAccess())
                                    {{-- System Admin sees all modules --}}
                                    @foreach(\App\Yantrana\Components\Auth\Models\AuthModel::$availableModules as $moduleKey => $moduleTitle)
                                        <div class="col-md-6 mb-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" name="module_permissions[]" 
                                                    value="{{ $moduleKey }}" id="module{{ ucfirst($moduleKey) }}">
                                                <label class="custom-control-label" for="module{{ ucfirst($moduleKey) }}">
                                                    {{ $moduleTitle }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    {{-- Super Admin sees only their assigned modules --}}
                                    @foreach(getAssignableModules() as $moduleKey => $moduleTitle)
                                        <div class="col-md-6 mb-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" name="module_permissions[]" 
                                                    value="{{ $moduleKey }}" id="module{{ ucfirst($moduleKey) }}">
                                                <label class="custom-control-label" for="module{{ ucfirst($moduleKey) }}">
                                                    {{ $moduleTitle }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                            <small class="text-muted">
                                @if(hasSystemAdminAccess())
                                    {{ __tr('Select the modules that this Super Admin will have access to. Their vendors will only see the selected modules.') }}
                                @else
                                    {{ __tr('Select the modules that this user will have access to. You can only assign modules that you have access to.') }}
                                @endif
                            </small>
                        </div>
                    </div>
                </div>
                {{-- /MODULE PERMISSIONS SECTION --}}
                @endif
            </div>
            {{-- Form footer --}}
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">{{ __tr('Add') }}</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <?= __tr('Close') ?>
                </button>
            </div>
        </x-lw.form>
    </x-lw.modal>
    <!-- EDIT VENDOR/SUPER ADMIN MODAL -->
    <x-lw.modal id="lwEditVendor" :header="hasSystemAdminAccess() ? __tr('Edit Super Admin') : __tr('Edit User')" :hasForm="true">
        <!-- EDIT VENDOR FORM  -->
        <x-lw.form id="lwEditPlanForm" :action="hasSystemAdminAccess() ? route('system.admin.super_admins.write.update') : route('vendor.write.update')"
            :data-callback-params="['modalId' => '#lwEditVendor', 'datatableId' => '#lwManageVendorsTable']"
            data-callback="appFuncs.modelSuccessCallback">
            <!-- form body -->
            <div data-default-text="{{ __tr('Please wait while we fetch data') }}" id="lwEditVendorBody"
                class="lw-form-modal-body"></div>
            <script type="text/template" id="lwEditVendorBody-template">
                <input type="hidden" name="vendorIdOrUid" value="<%- __tData._uid %>" />
                    <input type="hidden" name="userIdOrUid" value="<%- __tData.userUId %>" />
                <!-- FORM FIELDS -->
                <!-- TITLE -->
                <div class="form-group">
                    <label for="lwTitleField"><?= __tr('Title') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user-alt"></i></span>
                        </div>
                        <input type="text" class="lw-form-field form-control" placeholder="<?= __tr('Title') ?>" id="lwTitleField" value="<%- __tData.title %>" name="title"/>
                    </div>
                    <input type="text" class="form-control border-success shadow-green"
                        id="lwTitleField" placeholder="<?= __tr('Title') ?>"
                        value="<%- __tData.title %>" name="title" />
                </div>
                <!-- /Title -->
                {{-- USER TYPE TITLE --}}
                <div class="text-center text-muted mt-4 mb-0 ">
                    @if(hasSystemAdminAccess())
                        {{ __tr('Super Admin User') }}
                    @else
                        {{ __tr('Admin User') }}
                    @endif
                </div>
                <!-- UserName  -->
                <div class="form-group">
                    <label for="lwUserNameEditField"><?= __tr('Username') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-id-card"></i></span>
                        </div>
                        <input type="text"class="lw-form-field form-control" placeholder="<?= __tr('Email ') ?>" id="lwEmailEditField" value="<%- __tData.email%>" name="email" />
                    </div>
                </div>
                <!-- /UserName  -->
                <!-- FIRST NAME -->
                <div class="form-group">
                    <label for="lwDescriptionField"><?= __tr('First Name') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user-alt"></i></span>
                        </div>
                        <input type="text" class="lw-form-field form-control" placeholder="<?= __tr('First Name') ?>" id="lwFirstNameField" value="<%- __tData.first_name %>" name="first_name"/>
                    </div>
                </div>
                <!-- /FIRST NAME -->
                <!-- LAST NAME -->
                <div class="form-group">
                    <label for="lwDescriptionField"><?= __tr('Last Name') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user-alt"></i></span>
                        </div>
                        <input type="text" class="lw-form-field form-control" placeholder="<?= __tr('Last Name') ?>" id="lwLastNameField" value="<%- __tData.last_name %>" name="last_name"/>
                    </div>
                </div>
                <!-- /LAST NAME -->
                 <!-- MOBILE NUMBER -->
                <div class="form-group mb-3">
                    <label for="lwMobileNumberField"><?= __tr('Mobile Number') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fas fa-mobile-alt"></i></span>
                        </div>
                        <input class="form-control" placeholder="{{ __tr('Mobile Number') }}" value="<%- __tData.mobile_number %>" type="number" name="mobile_number" required >
                    </div>
                    <h5><span class="text-muted ">{{__tr("Mobile number should be with country code without 0 or +")}}</span></h5>
                </div>
                 <!-- /MOBILE NUMBER -->
                <!-- EMAIL  -->
                <div class="form-group">
                    <label for="lwEmailEditField"><?= __tr('Email') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-at"></i></span>
                        </div>
                        <input type="text" class="lw-form-field form-control" placeholder="<?= __tr('Email ') ?>" id="lwEmailEditField" value="<%- __tData.email%>" name="email" required="true" />
                    </div>
                </div>
                <!-- /EMAIL  -->
                <!-- STATUS -->
                <div class="form-group pt-3">
                    @if(!hasSystemAdminAccess())
                    <label for="lwIsVendorActiveEditField">{{  __tr('Vendor Status') }}</label>
                    <input type="checkbox" id="lwIsVendorActiveEditField" <%- __tData.store_status == 1 ? 'checked' : '' %> data-lw-plugin="lwSwitchery" name="store_status">
                    @endif
                </div>
                <!-- /STATUS -->
                <!-- STATUS -->
                <div class="form-group pt-3">
                    <label for="lwIsActiveEditField">{{  hasSystemAdminAccess() ? __tr('Super Admin Status') : __tr('Admin User Status') }}</label>
                    <input type="hidden" name="status" value="0" />
                    <input type="checkbox" id="lwIsActiveEditField" value="1" <%- __tData.status == 1 ? 'checked' : '' %> data-lw-plugin="lwSwitchery" name="status">
                </div>
                <!-- /STATUS -->

                @if(hasCentralAccess())
                <!-- MODULE PERMISSIONS -->
                <div class="form-group mt-4">
                    <label class="form-control-label">{{ __tr('Module Permissions') }}</label>
                    <div class="card border">
                        <div class="card-body p-3">
                            <div class="row">
                                @if(hasSystemAdminAccess())
                                    {{-- System Admin sees all modules --}}
                                    @foreach(\App\Yantrana\Components\Auth\Models\AuthModel::$availableModules as $moduleKey => $moduleTitle)
                                        <div class="col-md-6 mb-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" name="module_permissions[]" 
                                                    value="{{ $moduleKey }}" id="module{{ ucfirst($moduleKey) }}Edit"
                                                    <%- __tData.module_permissions && __tData.module_permissions.includes('{{ $moduleKey }}') ? 'checked' : '' %>>
                                                <label class="custom-control-label" for="module{{ ucfirst($moduleKey) }}Edit">
                                                    {{ $moduleTitle }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    {{-- Super Admin sees only their assigned modules --}}
                                    @foreach(getAssignableModules() as $moduleKey => $moduleTitle)
                                        <div class="col-md-6 mb-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" name="module_permissions[]" 
                                                    value="{{ $moduleKey }}" id="module{{ ucfirst($moduleKey) }}Edit"
                                                    <%- __tData.module_permissions && __tData.module_permissions.includes('{{ $moduleKey }}') ? 'checked' : '' %>>
                                                <label class="custom-control-label" for="module{{ ucfirst($moduleKey) }}Edit">
                                                    {{ $moduleTitle }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                            <small class="text-muted">
                                @if(hasSystemAdminAccess())
                                    {{ __tr('Select the modules that this Super Admin will have access to. Their vendors will only see the selected modules.') }}
                                @else
                                    {{ __tr('Select the modules that this user will have access to. You can only assign modules that you have access to.') }}
                                @endif
                            </small>
                        </div>
                    </div>
                </div>
                <!-- /MODULE PERMISSIONS -->
                @endif
            </script>
            <!-- FORM FOOTER -->
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">{{ __tr('Submit') }}</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
            </div>

            <!-- Section Title -->
            <div class="text-center text-success mt-4 mb-3 font-weight-bold">
                {{ __tr('Admin User') }}
            </div>

            <!-- Username / Email -->
            <div class="form-group mb-3">
                <label for="lwUserNameEditField" class="font-weight-bold text-dark"><?= __tr('Username') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fa fa-id-card me-2"></i></span>
                    </div>
                    <input type="text" class="form-control border-success shadow-green"
                        id="lwUserNameEditField" placeholder="<?= __tr('Username') ?>"
                        value="<%- __tData.username %>" name="username" />
                </div>
            </div>

            <!-- First Name -->
            <div class="form-group mb-3">
                <label for="lwFirstNameField" class="font-weight-bold text-dark"><?= __tr('First Name') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fa fa-user-alt me-2"></i></span>
                    </div>
                    <input type="text" class="form-control border-success shadow-green"
                        id="lwFirstNameField" placeholder="<?= __tr('First Name') ?>"
                        value="<%- __tData.first_name %>" name="first_name" />
                </div>
            </div>

            <!-- Last Name -->
            <div class="form-group mb-3">
                <label for="lwLastNameField" class="font-weight-bold text-dark"><?= __tr('Last Name') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fa fa-user-alt me-2"></i></span>
                    </div>
                    <input type="text" class="form-control border-success shadow-green"
                        id="lwLastNameField" placeholder="<?= __tr('Last Name') ?>"
                        value="<%- __tData.last_name %>" name="last_name" />
                </div>
            </div>

            <!-- Mobile Number -->
            <div class="form-group mb-3">
                <label for="lwMobileNumberField" class="font-weight-bold text-dark"><?= __tr('Mobile Number') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fas fa-mobile-alt me-2"></i></span>
                    </div>
                    <input type="number" class="form-control border-success shadow-green"
                        name="mobile_number" value="<%- __tData.mobile_number %>"
                        placeholder="{{ __tr('Mobile Number') }}" required />
                </div>
                <small class="text-muted d-block mt-1">{{ __tr('Mobile number should be with country code without 0 or +') }}</small>
            </div>

            <!-- Email -->
            <div class="form-group mb-3">
                <label for="lwEmailEditField" class="font-weight-bold text-dark"><?= __tr('Email') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fa fa-at me-2"></i></span>
                    </div>
                    <input type="email" class="form-control border-success shadow-green"
                        id="lwEmailEditField" placeholder="<?= __tr('Email') ?>"
                        value="<%- __tData.email %>" name="email" required />
                </div>
            </div>

            <!-- Vendor Status -->
            <div class="form-group pt-3">
                <label for="lwIsVendorActiveEditField" class="font-weight-bold text-dark">
                    {{  __tr('Vendor Status') }}
                </label>
                <input type="checkbox" id="lwIsVendorActiveEditField"
                    <%- __tData.store_status == 1 ? 'checked' : '' %>
                    data-lw-plugin="lwSwitchery" name="store_status">
            </div>

            <!-- Admin User Status -->
            <div class="form-group pt-3">
                <label for="lwIsActiveEditField" class="font-weight-bold text-dark">
                    {{  __tr('Admin User Status') }}
                </label>
                <input type="checkbox" id="lwIsActiveEditField"
                    <%- __tData.status == 1 ? 'checked' : '' %>
                    data-lw-plugin="lwSwitchery" name="status">
            </div>
        </script>

        <!-- FORM FOOTER -->
        <div class="modal-footer">
            <button type="submit" class="btn btn-success font-weight-bold">{{ __tr('Submit ') }} <i class="fa fa-paper-plane"></i></button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
        </div>
    </x-lw.form>
</x-lw.modal>

    <!-- EDIT VENDOR MODAL END -->
    <!-- FOR CHANGE PASSWORD FOR VENDOR -->
    <x-lw.modal id="lwChangePasswordAuthor" :header="__tr('Change Password')" :hasForm="true">
        <!-- EDIT ACCOUNT FORM -->
        <x-lw.form class="mb-0" id="lwChangeAuthorPassword" :action="route('auth.vendor.change.password')"
            :data-callback-params="['modalId' => '#lwChangePasswordAuthor','datatableId' => '#lwAccountList']"
            data-callback="appFuncs.modelSuccessCallback" data-secured="true">
            <!-- FORM BODY -->
            <div id="lwChangePasswordBody" class="lw-form-modal-body"></div>
            <script type="text/template" id="lwChangePasswordBody-template">
                <!-- FORM FIELDS -->
                <input type="hidden" name="users_id" value="<%-__tData._id %>" />
                <!-- for new password -->
                <div class="form-group">
                    <label for="lwNewPasswordField">
                        <?= __tr('New Password') ?>
                    </label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-key"></i></span>
                        </div>
                        <input type="password" class="lw-form-field form-control {{ $errors->has('password') ? ' is-invalid' : '' }}" placeholder="<?= __tr('New Password') ?>" id="lwNewPasswordField" value="" name="password" />
                    </div>
                </div>
                <!-- /NEW PASSWORD -->
                <!-- CONFIRM NEW PASSWORD -->
                <div class="form-group">
                    <label for="lwConfirmNewPasswordField">
                        <?= __tr('Confirm New Password') ?>
                    </label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-key"></i></span>
                        </div>
                        <input type="password" class="lw-form-field form-control"
                        placeholder="<?= __tr('Confirm New Password') ?>" id="lwConfirmNewPasswordField" value=""
                        name="password_confirmation" />
                    </div>
                </div>
                <!-- /CONFIRM NEW PASSWORD-->
            </script>
            <!-- FORM FOOTER -->
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">{{ __tr('Change Password') }}</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
            </div>
        </x-lw.form>
        <!--/  EDIT VENDOR FORM -->
    </x-lw.modal>

    <!--/ EDIT VENDOR MODAL -->
    @push('footer')
    @endpush
    @push('appScripts')
    <script>
        (function($) {
            'use strict';
            window.afterSuccessfullyCreated = function (responseData) {
            if (responseData.reaction == 1) {
                __Utils.viewReload();
            }
        }
        })(jQuery);
    </script>
    @endpush

</div>
@endsection
