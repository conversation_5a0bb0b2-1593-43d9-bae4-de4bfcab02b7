<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Yantrana\Base\BaseModel;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The custom primary key.
     *
     * @var string
     */
    protected $primaryKey = '_id';

    /**
     * The generate UID or not
     *
     * @var string
     */
    protected $isGenerateUID = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        '_uid',
        'email',
        'username',
        'password',
        'company_id',
        'user_roles__id',
        'vendors__id',
        'status',
        'first_name',
        'last_name',
        'mobile_number',
        'module_permissions',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'module_permissions' => 'array',
    ];

    /**
     * Get the company that owns the user.
     */
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', '_id');
    }

    /**
     * Get the role that owns the user.
     */
    public function role()
    {
        return $this->belongsTo(\App\Yantrana\Components\Auth\Models\AuthRoleModel::class, 'user_roles__id', '_id');
    }

    /**
     * Get the user's full name.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Available modules that can be assigned to users
     */
    public static $availableModules = [
        'whatsapp_flows' => 'WhatsApp Flows',
        'whatsapp_orders' => 'WhatsApp Orders',
        'campaigns' => 'Campaigns',
        'templates' => 'Templates',
        'chatbot' => 'Chatbot'
    ];

    /**
     * Check if user has access to a specific module
     *
     * @param string $module
     * @return bool
     */
    public function hasModuleAccess($module)
    {
        // System admins have access to all modules
        if ($this->user_roles__id === 4) {
            return true;
        }

        // Super admins get modules from their company
        if ($this->user_roles__id === 1) {
            return $this->company ? $this->company->hasModule($module) : false;
        }

        // Vendor admins and users get modules from their individual permissions
        if (in_array($this->user_roles__id, [2, 3])) {
            if (!$this->module_permissions) {
                return false;
            }
            return in_array($module, $this->module_permissions);
        }

        return false;
    }

    /**
     * Get list of enabled modules for this user
     *
     * @return array
     */
    public function getEnabledModules()
    {
        // System admins have access to all modules
        if ($this->user_roles__id === 4) {
            return self::$availableModules;
        }

        // Super admins get modules from their company
        if ($this->user_roles__id === 1) {
            return $this->company ? $this->company->getEnabledModules() : [];
        }

        // Vendor admins and users get modules from their individual permissions
        if (in_array($this->user_roles__id, [2, 3])) {
            if (!$this->module_permissions) {
                return [];
            }
            return array_intersect_key(self::$availableModules, array_flip($this->module_permissions));
        }

        return [];
    }

    /**
     * Get modules that this user can assign to others
     * (Only super admins can assign modules to vendor users)
     *
     * @return array
     */
    public function getAssignableModules()
    {
        // Only super admins can assign modules
        if ($this->user_roles__id === 1) {
            return $this->getEnabledModules();
        }

        return [];
    }
}
