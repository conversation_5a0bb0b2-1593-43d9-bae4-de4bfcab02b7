<?php
/**
* ContactCustomFieldRepository.php - Repository file
*
* This file is part of the Contact component.
*-----------------------------------------------------------------------------*/

namespace App\Yantrana\Components\Contact\Repositories;

use App\Yantrana\Base\BaseRepository;
use App\Yantrana\Components\Contact\Models\ContactCustomFieldModel;
use App\Yantrana\Components\Contact\Models\ContactCustomFieldValueModel;
use App\Yantrana\Components\Contact\Interfaces\ContactCustomFieldRepositoryInterface;

class ContactCustomFieldRepository extends BaseRepository implements ContactCustomFieldRepositoryInterface
{
    /**
     * primary model instance
     *
     * @var  object
     */
    protected $primaryModel = ContactCustomFieldModel::class;


    /**
      * Fetch customField datatable source
      *
      * @return  mixed
      *---------------------------------------------------------------- */
    public function fetchCustomFieldDataTableSource()
    {
        // basic configurations for dataTables data
        $dataTableConfig = [
            // searchable columns
            'searchable' => [
                'input_name',
                'input_type'

            ]
        ];
        // get Model result for dataTables
        return ContactCustomFieldModel::where([
            'vendors__id' => getVendorId()
        ])->dataTables($dataTableConfig)->toArray();
    }

    /**
      * Delete $customField record and return response
      *
      * @param  object $inputData
      *
      * @return  mixed
      *---------------------------------------------------------------- */

    public function deleteCustomField($customField)
    {
        // Check if $customField deleted
        if ($customField->deleteIt()) {
            // if deleted
            return true;
        }
        // if failed to delete
        return false;
    }

    /**
      * Store new customField record and return response
      *
      * @param  array $inputData
      *
      * @return  mixed
      *---------------------------------------------------------------- */

    public function storeCustomField($inputData)
    {
        // prepare data to store
        $keyValues = [
            'input_name',
            'input_type',
            'vendors__id' => getVendorId(),
        ];
        return $this->storeIt($inputData, $keyValues);
    }

    function storeCustomValues($values, $index = null, $whereIn = null) {
        try {
            if (empty($values)) {
                return false;
            }

            // For bulk operations with index and whereIn
            if ($index && $whereIn) {
                $results = [];
                foreach ($values as $value) {
                    // Check if record exists
                    $existing = ContactCustomFieldValueModel::where([
                        'contacts__id' => $value['contacts__id'],
                        'contact_custom_fields__id' => $value['contact_custom_fields__id'],
                        'vendors__id' => $value['vendors__id']
                    ])->first();

                    if ($existing) {
                        // Update existing record
                        $existing->field_value = $value['field_value'];
                        $results[] = $existing->save();
                    } else {
                        // Create new record
                        $results[] = ContactCustomFieldValueModel::create($value);
                    }
                }
                return !in_array(false, $results);
            }

            // For simple insertion/update
            $results = [];
            foreach ($values as $value) {
                // Check if record exists
                $existing = ContactCustomFieldValueModel::where([
                    'contacts__id' => $value['contacts__id'],
                    'contact_custom_fields__id' => $value['contact_custom_fields__id'],
                    'vendors__id' => $value['vendors__id']
                ])->first();

                if ($existing) {
                    // Update existing record
                    $existing->field_value = $value['field_value'];
                    $results[] = $existing->save();
                } else {
                    // Create new record
                    $results[] = ContactCustomFieldValueModel::create($value);
                }
            }

            return !in_array(false, $results);
        } catch (\Exception $e) {
            \Log::error('Error in storeCustomValues', [
                'error' => $e->getMessage(),
                'values' => $values
            ]);
            return false;
        }
    }
}
