<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ModuleApiAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();
        
        if (!$token) {
            Log::warning('Module API request without token', [
                'url' => $request->url(),
                'method' => $request->method(),
                'ip' => $request->ip()
            ]);
            return response()->json([
                'error' => 'Authorization token required'
            ], 401);
        }

        $expectedToken = env('MODULE_API_SECRET');
        
        if (!$expectedToken) {
            Log::error('MODULE_API_SECRET not configured');
            return response()->json([
                'error' => 'Module API not configured'
            ], 500);
        }

        if ($token !== $expectedToken) {
            Log::warning('Module API request with invalid token', [
                'url' => $request->url(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'provided_token_length' => strlen($token)
            ]);
            return response()->json([
                'error' => 'Unauthorized'
            ], 403);
        }

        // Generate a unique request ID for traceability
        $requestId = uniqid('sync_', true);
        
        // Log the complete incoming payload
        Log::info('Module API request authenticated successfully', [
            'request_id' => $requestId,
            'url' => $request->url(),
            'method' => $request->method(),
            'headers' => $request->headers->all(),
            'payload' => $request->all(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toISOString()
        ]);

        // Add request ID to the request for downstream use
        $request->merge(['_request_id' => $requestId]);

        return $next($request);
    }
}
