
<?php $__env->startSection('content'); ?>

<?php $__env->startPush('head'); ?>
<?php echo __yesset('dist/css/whatsapp-chat.css', true); ?>

<style>
    .lw-contact-list {
        max-height: 70vh;
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-width: thin;
        position: relative;
    }
    
    /* Add a loading indicator for when more contacts are being loaded */
    .lw-contact-list-loading {
        text-align: center;
        padding: 10px;
        font-size: 12px;
        color: #666;
    }
   /* Modern WhatsApp Chat Interface CSS */

.lw-contact-list {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    position: relative;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.lw-contact-list-loading {
    text-align: center;
    padding: 16px;
    font-size: 14px;
    color: #667781;
    background: #f8f9fa;
}

.card.lw-whatsapp-chat-block-container {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    background: #f0f2f5;
    
    @media (max-width: 767px) {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        width: 100vw;
        border-radius: 0;

        .lw-whatsapp-chat-window {
            padding: 0;
            
            .chat-container {
                .card-body {
                    padding: 0;
                }
                
                .conversation {
                    padding: 0 12px;
                    
                    .conversation-container {
                        height: calc(100vh - 380px);
                    }
                }
                
                .user-bar {
                    min-height: 80px;
                    padding: 20px;
                    margin-top: -1px;
                }
            }
        }
        
        .lw-contacts-header {
            padding: 16px;
            background: linear-gradient(135deg, #00a884 0%, #008f72 100%);
            color: white;
            font-weight: 500;
        }

        .lw-contact-list-block,
        .lw-contact-crm-block {
            position: fixed;
            height: 100vh;
            width: 100vw;
            z-index: 2;
            margin: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
        }
        
        .lw-contact-crm-block {
            right: 0;
            padding: 24px;
        }
    }
    
    @media (min-width: 768px) {
        .lw-contact-list-block,
        .lw-contact-crm-block {
            display: block !important;
        }
    }
    
    .lw-whatsapp-chat-window {
        background: #f0f2f5;
        
        .marvel-device .screen {
            text-align: left;
            border-radius: 16px;
            overflow: hidden;
        }

        .screen-container {
            height: 100%;
            border-radius: 16px;
        }

        /* Status Bar */
        .status-bar {
            height: 32px;
            background: linear-gradient(135deg, #00a884 0%, #008f72 100%);
            color: #fff;
            font-size: 14px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .status-bar div {
            font-weight: 500;
            font-size: 13px;
        }

        /* Chat */
        .chat {
            height: calc(100% - 32px);
            background: #ffffff;
        }

        .chat-container {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* User Bar */
        .user-bar {
            min-height: 64px;
            background: linear-gradient(135deg, #00a884 0%, #008f72 100%);
            color: #fff;
            padding: 12px 16px;
            font-size: 16px;
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .user-bar .avatar {
            width: 40px;
            height: 40px;
            margin-right: 12px;
            border-radius: 50%;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .user-bar .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-bar .name {
            font-size: 16px;
            font-weight: 500;
            margin: 0;
            flex: 1;
            line-height: 1.2;
        }

        .user-bar .status {
            display: block;
            font-size: 13px;
            font-weight: 400;
            opacity: 0.9;
            margin-top: 2px;
        }

        .user-bar .actions {
            margin-left: auto;
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .user-bar .lw-whatsapp-bar-icon-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            color: white;
            text-decoration: none;
        }

        .user-bar .lw-whatsapp-bar-icon-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        /* Conversation */
        .conversation {
            flex: 1;
            position: relative;
            background: #e5ddd5 url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='pattern' x='0' y='0' width='20' height='20' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='10' cy='10' r='0.5' fill='%23d1c7b8' opacity='0.3'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23pattern)'/%3E%3C/svg%3E") repeat;
            overflow: hidden;
            padding: 0;
        }

        .conversation ::-webkit-scrollbar {
            width: 6px;
        }

        .conversation ::-webkit-scrollbar-track {
            background: transparent;
        }

        .conversation ::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }

        .conversation .conversation-container {
            height: calc(90vh - 140px);
            overflow-y: auto;
            padding: 0 8px;
            display: flex;
            flex-direction: column-reverse;
            scroll-behavior: smooth;
        }

        .conversation-container-chats {
            width: 100%;
        }

        /* Messages */
        .message {
            color: #111b21;
            clear: both;
            line-height: 1.4;
            font-size: 14px;
            padding: 8px 12px;
            position: relative;
            margin: 4px 0;
            max-width: 65%;
            word-wrap: break-word;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: transform 0.1s ease;
        }

        .message:hover {
            transform: translateY(-1px);
        }

       

        .metadata {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 4px;
            margin-top: 4px;
            padding-left: 16px;
        }

        .metadata .time {
            color: rgba(17, 27, 33, 0.6);
            font-size: 11px;
            font-weight: 400;
        }

        .metadata .tick {
            display: flex;
            align-items: center;
            width: 16px;
            height: 16px;
        }

        .message.received {
            background: #ffffff;
            border-radius: 0 8px 8px 8px;
            float: left;
            position: relative;
        }

        .message.received:before {
            content: '';
            position: absolute;
            left: -8px;
            top: 0;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-right-color: #ffffff;
            border-left: 0;
            border-top: 0;
        }

        .message.sent {
            background: #d9fdd3;
            border-radius: 8px 0 8px 8px;
            float: right;
            position: relative;
        }

        .message.sent:before {
            content: '';
            position: absolute;
            right: -8px;
            top: 0;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-left-color: #d9fdd3;
            border-right: 0;
            border-top: 0;
        }

        .lw-highlight-replied-message {
            background: rgba(194, 237, 114, 0.6);
        }

        .lw-plain-message-text {
            white-space: pre-line;
            font-size: 14px;
            line-height: 1.4;
        }

        /* Compose */
        .conversation-compose {
            display: flex;
            align-items: flex-end;
            padding: 12px 16px;
            
            gap: 8px;
            
        }

        .conversation-compose .emoji-container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            position: relative;
        }

        .conversation-compose .emoji-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: #f0f2f5;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #54656f;
            font-size: 18px;
        }

        .conversation-compose .emoji-btn:hover {
            background: #e4e6ea;
        }

        .conversation-compose .emoji-btn:focus {
            outline: none;
            background: #e4e6ea;
        }

        .conversation-compose .input-msg {
            flex: 1;
            border: none;
            background: white;
            border-radius: 20px;
            
            font-size: 15px;
            outline: none;
            resize: none;
            max-height: 50px;
            min-height: 40px;
            line-height: 1.4;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .conversation-compose .photo {
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .conversation-compose .photo:hover {
            background: #f5f6f6;
        }

        .conversation-compose .photo .lw-whatsapp-bar-icon-btn i {
            color: #54656f;
            font-size: 20px;
        }

        .conversation-compose .send {
            background: transparent;
            border: 0;
            cursor: pointer;
            padding: 0;
            outline: none;
        }

        .conversation-compose .send .circle {
            background: #00a884;
            border-radius: 50%;
            color: #fff;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
        }

        .conversation-compose .send .circle:hover {
            background: #008f72;
        }

        .conversation-compose .send .circle i {
            font-size: 18px;
        }

        /* Modern Contact List Container */
        .lw-contact-list {
            height: calc(80vh - 160px);
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9edef;
            
            .lw-contact {
                border-bottom: 1px solid #e9edef;
                padding: 12px 16px;
                transition: background-color 0.15s ease;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 12px;
                
                &:hover {
                    background: #f5f6f6;
                }
                
                &.list-group-item-light {
                    background: #e5f3f0;
                }
                
                .lw-contact-avatar {
                    width: 48px;
                    height: 48px;
                    border-radius: 50%;
                    background: #00a884;
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 600;
                    font-size: 16px;
                    flex-shrink: 0;
                }
                
                .ms-2 {
                    flex: 1;
                    min-width: 0;
                }
                
                h3 {
                    font-size: 16px;
                    font-weight: 500;
                    margin: 0 0 4px 0;
                    color: #111b21;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                
                .lw-contact-labels {
                    margin-bottom: 4px;
                    
                    .badge {
                        font-size: 11px;
                        padding: 2px 6px;
                        border-radius: 4px;
                        margin-right: 4px;
                        font-weight: 500;
                    }
                }
                
                .lw-last-message-at {
                    font-size: 12px;
                    color: #667781;
                }
                
                .badge.bg-success {
                    background: #00a884 !important;
                    color: white;
                    font-size: 12px;
                    padding: 2px 6px;
                    border-radius: 50%;
                    min-width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 8px;
                }
            }
        }

        /* Reference Layout - Avatar Left, Content Right */
        .lw-contact {
            border: none !important;
            border-bottom: 1px solid #e9edef !important;
            padding: 12px 16px !important;
            transition: all 0.2s ease;
            cursor: pointer;
            display: flex !important;
            align-items: flex-start !important;
            gap: 12px !important;
            position: relative;
            text-decoration: none !important;
            min-height: 80px;
            background: #ffffff !important;
            border-radius: 0 !important;
        }

        .lw-contact:hover {
            background: #f0f2f5 !important;
        }

        .lw-contact.list-group-item-light {
            background: #e7f3ff !important;
            border-left: 4px solid #00a884 !important;
        }

        .lw-contact:last-child {
            border-bottom: none !important;
        }

        /* Avatar - Modern Design */
        .lw-contact .float-left {
            float: none !important;
            margin: 0 !important;
            display: block !important;
        }

        .lw-contact .lw-contact-avatar {
            width: 52px !important;
            height: 52px !important;
            border-radius: 50% !important;
            background: linear-gradient(135deg, #00a884 0%, #008f72 100%) !important;
            color: white !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-weight: 700 !important;
            font-size: 18px !important;
            flex-shrink: 0 !important;
            margin: 0 !important;
            box-shadow: 0 2px 8px rgba(0, 168, 132, 0.2) !important;
            border: 2px solid rgba(255, 255, 255, 0.9) !important;
        }

        /* Content Area - Right of Avatar */
        .lw-contact .ms-2.me-auto.w-100.mt-1 {
            display: block !important;
            flex: 1 !important;
            min-width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            padding-right: 50px !important; /* Space for unread badge */
        }

        /* Content inside main area */
        .lw-contact .ms-2 > div:not(.float-left) {
            margin: 0 !important;
            display: block !important;
        }

        /* Name styling - Line 1 */
        .lw-contact h2,
        .lw-contact h3 {
            margin: 0 0 2px 0 !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            color: #111b21 !important;
            line-height: 1.2 !important;
            display: block !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Phone number styling - Line 2 */
        .lw-contact h3 span:last-child {
            display: block !important;
            font-size: 14px !important;
            font-weight: 400 !important;
            color: #667781 !important;
            margin-top: 2px !important;
            line-height: 1.2 !important;
        }

        /* Hide the dash between name and number */
        .lw-contact h3 span:nth-child(2) {
            display: none !important;
        }

        /* Modern Labels in Contact List */
        .lw-contact .lw-contact-labels {
            margin: 2px 0 2px 0 !important;
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 4px !important;
        }

        .lw-contact .lw-contact-labels .badge,
        .lw-contact .lw-contact-labels .label-badge {
            font-size: 10px !important;
            padding: 2px 6px !important;
            border-radius: 12px !important;
            margin: 0 !important;
            font-weight: 500 !important;
            letter-spacing: 0.1px;
            opacity: 0.9;
            border: none !important;
            display: inline-block !important;
        }

        /* Labels in Contact Info Section */
        .lw-contact-crm-block .label-badge {
            font-size: 11px !important;
            padding: 4px 8px !important;
            border-radius: 12px !important;
            margin: 2px 4px 2px 0 !important;
            font-weight: 500 !important;
            letter-spacing: 0.2px;
            display: inline-block !important;
            border: none !important;
        }

        /* Gap utility for labels */
        .gap-2 > * {
            margin-right: 8px !important;
            margin-bottom: 4px !important;
        }

        .gap-2 > *:last-child {
            margin-right: 0 !important;
        }

        /* Override any other timestamp styling */
        .lw-contact .lw-last-message-at:not(.lw-contact-info .lw-last-message-at) {
            display: none !important;
        }

        /* Unread Badge - Top Right Corner Only */
        .lw-contact .mt-2.text-right.w-100.mt-3 {
            position: absolute !important;
            top: 12px !important;
            right: 16px !important;
            margin: 0 !important;
            width: auto !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        /* Hide timestamp from top right - it's now in content area */
        .lw-contact .mt-2.text-right .lw-last-message-at {
            display: none !important;
        }

        /* Unread Badge Styling */
        .lw-contact .badge.bg-success.rounded-pill {
            background: #00a884 !important;
            color: white !important;
            font-size: 11px !important;
            min-width: 20px !important;
            height: 20px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 50% !important;
            margin: 0 !important;
            font-weight: 600 !important;
            box-shadow: 0 2px 4px rgba(0, 168, 132, 0.3) !important;
        }

        /* Contact Info - Right Side of Avatar */
        .lw-contact .lw-contact-info {
            display: block !important;
            margin: 0 !important;
            width: 100% !important;
            
        }

        /* Line 1: Name - Top */
        .lw-contact .lw-contact-name {
            margin: 0 0 2px 0 !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            color: #111b21 !important;
            line-height: 1.4 !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block !important;
        }

        /* Line 2: Phone Number - Middle */
        .lw-contact .lw-contact-phone {
            margin: 0 0 2px 0 !important;
            font-size: 14px !important;
            font-weight: 400 !important;
            color: #667781 !important;
            line-height: 1.3 !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block !important;
        }

        .lw-contact .lw-contact-phone span {
            display: inline !important;
        }

        /* Line 3: Timestamp - Bottom */
        .lw-contact .lw-contact-info .lw-last-message-at {
            margin: 0 !important;
            font-size: 12px !important;
            color: #8696a0 !important;
            line-height: 1.3 !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block !important;
        }

        /* Template Messages */
        .lw-template-message {
            .lw-plain-message-text {
                white-space: pre-line;
            }

            .lw-whatsapp-preview .card {
                border: none;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                border-radius: 8px;
            }
            
            .lw-whatsapp-preview-message-container {
                max-width: 300px;
                margin-top: 8px;
            }
        }

        /* Emoji Area Fixes */
        .emojionearea {
            margin-left: 0;
            position: relative;
            width: 100%;
            border-radius: 20px;
            overflow: visible !important;
            background: white;
            border: 1px solid #e4e6ea;
        }

        .emojionearea.focused {
            box-shadow: 0 0 0 2px #00a884;
            border-color: #00a884;
        }

        .emojionearea .emojionearea-editor {
            padding: 12px 50px 12px 16px;
            border: none;
            background: transparent;
            font-size: 15px;
            line-height: 1.4;
            min-height: 44px;
            max-height: 120px;
            overflow-y: auto;
            resize: none;
            outline: none;
        }

        .emojionearea .emojionearea-button {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 20px;
            color: #54656f;
            cursor: pointer;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .emojionearea .emojionearea-button:hover {
            background: #f0f2f5;
        }

        .emojionearea .emojionearea-picker {
            position: absolute !important;
            left: 0 !important;
            bottom: calc(100% + 8px) !important;
            right: auto !important;
            top: auto !important;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            border: 1px solid #e4e6ea;
            background: white;
            z-index: 1000;
            max-width: 350px;
            width: 350px;
        }

        .emojionearea .emojionearea-picker .emojionearea-wrapper {
            border-radius: 12px;
            overflow: hidden;
        }

        .emojionearea .emojionearea-picker .emojionearea-tabs {
            background: #f8f9fa;
            border-bottom: 1px solid #e4e6ea;
        }

        .emojionearea .emojionearea-picker .emojionearea-tab {
            border-radius: 8px;
            margin: 4px;
        }

        .emojionearea .emojionearea-picker .emojionearea-tab.active {
            background: #00a884;
        }

        /* Mobile responsive emoji picker */
        @media (max-width: 768px) {
            .emojionearea .emojionearea-picker {
                max-width: 300px;
                width: 300px;
                left: -50px !important;
            }
        }

        @media (max-width: 480px) {
            .emojionearea .emojionearea-picker {
                max-width: 280px;
                width: 280px;
                left: -100px !important;
            }
        }

        /* Dropdown Menus */
        .dropdown-menu {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
            padding: 8px;
            margin-top: 8px;
        }

        .dropdown-item {
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            color: #111b21;
            transition: background-color 0.15s ease;
        }

        .dropdown-item:hover,
        .dropdown-item.active {
            background: #f5f6f6;
            color: #111b21;
        }

        .dropdown-item i {
            margin-right: 12px;
            width: 16px;
            color: #667781;
        }
    }
}

/* Navigation Tabs */
.lw-chat-navigation .nav-tabs {
    background: white;
    border: none;
    border-radius: 12px 12px 0 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 8px;
    display: flex;
    gap: 4px;
    margin-bottom: 0;
}

.lw-chat-navigation .nav-tabs .nav-link {
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    background: transparent;
    color: #667781;
    border: none;
    border-radius: 8px;
    transition: all 0.15s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.lw-chat-navigation .nav-tabs .nav-link:hover {
    background: #f5f6f6;
    color: #111b21;
}

.lw-chat-navigation .nav-tabs .nav-link.active {
    background: #00a884;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 168, 132, 0.3);
}

.lw-chat-navigation .nav-tabs .nav-link .badge {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lw-chat-navigation .nav-tabs .nav-link:not(.active) .badge {
    background: #ff9800;
    color: white;
}

/* Form Controls */
.form-control {
    border: 1px solid #e9edef;
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    transition: border-color 0.15s ease;
}

.form-control:focus {
    border-color: #00a884;
    box-shadow: 0 0 0 2px rgba(0, 168, 132, 0.2);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.15s ease;
}

.btn-primary {
    background: #00a884;
    border-color: #00a884;
}

.btn-primary:hover {
    background: #008f72;
    border-color: #008f72;
}

.btn-dark {
    background: #111b21;
    border-color: #111b21;
}

.btn-dark:hover {
    background: #2a3942;
    border-color: #2a3942;
}

.btn-light {
    background: #f5f6f6;
    border-color: #e9edef;
    color: #111b21;
}

.btn-light:hover {
    background: #e9edef;
    color: #111b21;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .lw-chat-navigation .nav-tabs {
        padding: 6px;
        gap: 2px;
        flex-wrap: wrap;
    }

    .lw-chat-navigation .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 13px;
        flex: 1;
        justify-content: center;
        min-width: 0;
    }

    .conversation .conversation-container {
        height: calc(100vh - 200px);
    }

    .conversation-compose {
        padding: 8px 12px;
    }

    .message {
        max-width: 80%;
        font-size: 14px;
        padding: 6px 10px;
    }
}

@media (max-width: 576px) {
    .lw-chat-navigation .nav-tabs .nav-link {
        padding: 6px 8px;
        font-size: 12px;
    }

    .lw-chat-navigation .nav-tabs .nav-link .badge {
        font-size: 10px;
        min-width: 16px;
        height: 16px;
    }

    /* Hide text on very small screens, show icons only */
    .lw-chat-navigation .nav-tabs .nav-link span:not(.badge) {
        display: none;
    }
}

/* Loading States */
.lw-disabled-block-content {
    opacity: 0.6;
    pointer-events: none;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message {
    animation: fadeIn 0.2s ease-out;
}

/* Contact CRM Block */
.lw-contact-crm-block {
    background: white;
    border-radius: 12px;
    padding: 20px;
    
    fieldset {
        border: 1px solid #e9edef;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        
        legend {
            font-size: 14px;
            font-weight: 600;
            color: #111b21;
            padding: 0 8px;
            margin-bottom: 12px;
        }
    }
    
    dl {
        margin: 0;
        
        dt {
            font-size: 12px;
            font-weight: 600;
            color: #667781;
            margin-bottom: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        dd {
            font-size: 14px;
            color: #111b21;
            margin-bottom: 12px;
            word-break: break-word;
        }
    }
}

/* Labels */
.label-badge {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 4px;
    font-weight: 500;
    display: inline-block;
}

/* Search Field */
input[type="search"] {
    border: 1px solid #e9edef;
    border-radius: 20px;
    padding: 10px 16px;
    font-size: 14px;
    background: white;
    transition: border-color 0.15s ease;
}

input[type="search"]:focus {
    border-color: #00a884;
    box-shadow: 0 0 0 2px rgba(0, 168, 132, 0.2);
    outline: none;
}
/* Navigation Container */
.custom-nav {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  box-sizing: border-box;
}

.custom-nav-tabs {
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 1rem;
}

/* Navigation Links */
.custom-nav-link {
  display: inline-flex;
  align-items: center;
  padding: 10px 10px;
  color: #495057;
  text-decoration: none;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.375rem 0.375rem 0 0;
  transition: all 0.15s ease-in-out;
  white-space: nowrap;
  font-size: 0.9rem;
  font-weight: 500;
  position: relative;
}

.custom-nav-link:hover {
  color: #0056b3;
  background-color: #e9ecef;
  text-decoration: none;
}

.custom-nav-link.active {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
  border-bottom-color: transparent;
}

/* Navigation Item */
.custom-nav-item {
  position: relative;
  display: flex;
  align-items: center;
}

/* Badge Styles */
.custom-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.375rem;
  min-width: 1.5rem;
}

.custom-badge-pill {
  border-radius: 10rem;
}

.custom-bg-yellow {
  background-color: #ffc107 !important;
}

.custom-text-dark {
  color: #212529 !important;
}

.custom-badge-white {
  background-color: #fff;
  border: 1px solid #dee2e6;
}

.custom-bg-info-subtle {
  background-color: #cff4fc;
  color: #055160;
}

/* Dropdown Styles */
.custom-dropdown {
  position: relative;
  display: inline-block;
}

.custom-dropdown-toggle {
  cursor: pointer;
}

.custom-dropdown-toggle::after {
  content: "";
  display: inline-block;
  margin-left: 0.5rem;
  vertical-align: 0.125rem;
  border-top: 0.3rem solid;
  border-right: 0.3rem solid transparent;
  border-bottom: 0;
  border-left: 0.3rem solid transparent;
}

.custom-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  min-width: 12rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 0.875rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.375rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

.custom-dropdown-menu-right {
  right: -150px !important;
  left: auto !important;
  transform: translateX(0) !important;
}

.custom-dropdown:hover .custom-dropdown-menu,
.custom-dropdown-menu.show {
  display: block;
}

.custom-dropdown-item {
  display: block;
  width: 100%;
  padding: 0.375rem 1rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  transition: all 0.15s ease-in-out;
}

.custom-dropdown-item:hover,
.custom-dropdown-item:focus {
  color: #1e2125;
  background-color: #e9ecef;
  text-decoration: none;
}

.custom-dropdown-item.active {
  color: #fff;
  background-color: #0d6efd;
}

.custom-dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid rgba(0, 0, 0, 0.15);
}

.custom-dropdown-scroll {
  max-height: 200px;
  overflow-y: auto;
}

/* Form Controls */
.custom-form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

.custom-form-control:focus {
  color: #212529;
  background-color: #fff;
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.custom-form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

/* Utility Classes */
.custom-d-flex {
  display: flex !important;
}

.custom-justify-content-between {
  justify-content: space-between !important;
}

.custom-align-items-center {
  align-items: center !important;
}

.custom-text-muted {
  color: #6c757d !important;
}

.custom-small {
  font-size: 0.875em;
}

/* Spacing Utilities */
.custom-ml-1 {
  margin-left: 0.25rem !important;
}

.custom-ml-2 {
  margin-left: 0.5rem !important;
}

.custom-mb-2 {
  margin-bottom: 0.5rem !important;
}

.custom-px-3 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.custom-py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-nav {
    flex-direction: column;
    align-items: stretch;
    gap: 0.25rem;
  }
  
  .custom-nav-link {
    justify-content: center;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 0.25rem;
    margin-bottom: 0.25rem;
  }
  
  .custom-nav-link.active {
    border-radius: 0.25rem;
    border: 1px solid #0d6efd;
    background-color: #0d6efd;
    color: #fff;
  }
  
  .custom-dropdown-menu {
    position: absolute !important;
    display: none;
    width: 250px !important;
    margin-top: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175) !important;
    border: 1px solid #dee2e6;
    right: 0 !important;
    left: auto !important;
  }

  .custom-dropdown-menu-right {
    right: 0 !important;
    left: auto !important;
  }

  .custom-dropdown:hover .custom-dropdown-menu,
  .custom-dropdown-menu.show {
    display: block !important;
  }
  
  .custom-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 576px) {
  .custom-nav {
    padding: 0.25rem;
  }
  
  .custom-nav-link {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }
  
  .custom-dropdown-item {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .custom-form-control {
    font-size: 0.875rem;
  }
  
  .custom-px-3 {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
}

@media (max-width: 480px) {
  .custom-nav-link {
    font-size: 0.7rem;
    padding: 0.25rem 0.375rem;
  }
  
  .custom-badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
    min-width: 1.2rem;
  }
  
  .custom-dropdown-menu {
    font-size: 0.8rem;
  }
  
  .custom-form-control-sm {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
  }
}

/* Container Adaptability */
.custom-nav-container {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* Ensure navigation fits within any parent container */
.custom-nav,
.custom-nav * {
  box-sizing: border-box;
}

/* Hide elements that might cause overflow */
@media (max-width: 320px) {
  .custom-nav-link span:not(.custom-badge) {
    display: none;
  }
  
  .custom-nav-link::before {
    content: attr(data-short-text);
  }
  
  .custom-dropdown-toggle {
    font-size: 0.65rem;
  }
}
</style>
<?php $__env->stopPush(); ?>
<div x-data="initialMessageData">

<div class="container-fluid" x-data="{myAssignedUnreadMessagesCount:null,myUnassignedUnreadMessagesCount:null,showUnreadContactsOnly:false}">
    <div class="">
        <div class="card lw-whatsapp-chat-block-container">
            <?php if(!getVendorSettings('current_phone_number_number')): ?>
            <div class="card-header">
            <div class="text-danger">
                <?php echo e(__tr('Phone number does not configured yet.')); ?>

            </div>
            </div>
            <?php endif; ?>
            <div id="lwWhatsAppChatWindow"
                class="card-body lw-whatsapp-chat-window p-sm-4" x-init="$watch('messagePaginatePage', function(value) {window.messagePaginatePage = value;});$watch('contactsPaginatePage', function(value) {window.contactsPaginatePage = value; });" :data-paginate-page="messagePaginatePage" :data-unread-only="showUnreadContactsOnly" :data-search-value="search" :data-contact-uid="contact?._uid">
                <div class="row" x-cloak x-data="{isContactListOpened:false,isContactCrmBlockOpened:false}">
                    <div class="col-sm-12 col-md-3 col-lg-3 col-xl-3 mb-4 lw-contact-list-block" x-show="isContactListOpened">
                        <h1><?php echo e(__tr('WhatsApp Chat')); ?></h1>
                        <hr class="my-2">
                        <h2 class="lw-contacts-header"> <span class="btn btn-light btn-sm float-right d-md-none" @click.prevent="isContactListOpened = false"><i class="fa fa-arrow-left"></i></span>  <abbr class="float-right" title="<?php echo e(__tr('Once you get the response by the contact, they will be come in the chat list of this chat window, alternatively you can click on chat button of the contact list to chat with the contact.')); ?>">?</abbr></h2>
                        <div class="form-group m-0"><label for="lwShowUnreadOnlyContacts"><input data-lw-plugin="lwSwitchery" data-color="orange" data-size="small" x-model="showUnreadContactsOnly" x-init="$watch('showUnreadContactsOnly', function(value) {
                            window.showUnreadContactsOnly = value;
                            _.defer(function() {
                                window.searchContacts();
                            });
                        })" class="custom-checkbox" id="lwShowUnreadOnlyContacts" type="checkbox" name="unread_only_contacts" id=""> <span x-show="!showUnreadContactsOnly"><?php echo e(__tr('Show all')); ?></span><span x-show="showUnreadContactsOnly"><?php echo e(__tr('Show unread only')); ?></span></label>
                        
                    </div>
                    <div>
                        <a href="<?php echo e(route('vendor.chat_message.export' 
             )); ?>" data-method="post" class="btn btn-dark btn-sm mt-3"><i class="fa fa-download"></i> <?php echo e(__tr('Report')); ?></a>
                    </div>
<nav>
    <div class="custom-nav custom-nav-tabs" id="nav-tab" role="tablist">
        <?php if(isVendorAdmin(getVendorId()) or !hasVendorAccess('assigned_chats_only')): ?>
            <a class="custom-nav-link <?php echo e(($assigned ?? null) ? '' : 'active'); ?>" href="<?php echo e(route('vendor.chat_message.contact.view')); ?>" id="lw-all-contacts-tab" data-target="#lwAllContactsTab" type="button" role="tab" aria-controls="lwAllContactsTab" aria-selected="true"><?php echo e(__tr('All')); ?> <span x-cloak x-show="unreadContactsCount()" class="custom-badge custom-bg-yellow custom-text-dark custom-badge-white custom-rounded-pill custom-ml-2" x-text="unreadContactsCount()"></span></a>
        <?php endif; ?>
        <a href="<?php echo e(route('vendor.chat_message.contact.view', ['assigned' => 'to-me'])); ?>" class="custom-nav-link <?php echo e((($assigned ?? null) == 'to-me') ? 'active' : ''); ?>" id="lw-to-me-tab" data-target="#lwAssignedToMeTab" type="button" role="tab" aria-controls="lwAssignedToMeTab" aria-selected="false"><?php echo e(__tr('Mine')); ?> <span x-cloak x-show="myAssignedUnreadContactsCount()" class="custom-badge custom-bg-yellow custom-text-dark custom-badge-white custom-rounded-pill custom-ml-2" x-text="myAssignedUnreadContactsCount()"></span></a>
        
        <?php if(isVendorAdmin(getVendorId()) or !hasVendorAccess('assigned_chats_only')): ?>
            <a href="<?php echo e(route('vendor.chat_message.contact.view', ['assigned' => 'unassigned'])); ?>" class="custom-nav-link <?php echo e(($assigned ?? null) == 'unassigned' ? 'active' : ''); ?>" id="lw-unassigned-tab" data-target="#lwUnassignedTab" type="button" role="tab" aria-controls="lwUnassignedTab" aria-selected="false"><?php echo e(__tr('Unassigned')); ?> <span x-cloak x-show="myUnassignedUnreadContactsCount()" class="custom-badge custom-bg-yellow custom-text-dark custom-badge-white custom-rounded-pill custom-ml-2" x-text="myUnassignedUnreadContactsCount()"></span></a>
        <?php endif; ?>
        
        <!-- Label Filter Dropdown -->
        <div class="custom-nav-item custom-dropdown">
            <a class="custom-nav-link custom-dropdown-toggle" href="#" id="labelFilterDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fa fa-tags"></i> 
                <span x-show="selectedLabelTitle" class="custom-badge custom-bg-info-subtle custom-rounded-pill custom-ml-1" x-text="selectedLabelTitle"></span>
            </a>
            <div class="custom-dropdown-menu custom-dropdown-menu-right" aria-labelledby="labelFilterDropdown">
                <div class="custom-px-3 custom-py-2">
                    <input type="text" x-model="labelSearch" @input="filterLabels()" class="custom-form-control custom-form-control-sm custom-mb-2" placeholder="<?php echo e(__tr('Search labels...')); ?>">
                </div>
                <div class="custom-dropdown-divider"></div>
                <div class="custom-dropdown-scroll">
                    <a class="custom-dropdown-item" href="#" @click.prevent="selectedLabel = ''; filterByLabel('');" :class="{'active': !selectedLabel}">
                        <?php echo e(__tr('All Labels')); ?>

                    </a>
                    <template x-for="label in filteredLabels" :key="label._id">
                        <a class="custom-dropdown-item custom-d-flex custom-justify-content-between custom-align-items-center" 
                           href="#" 
                           @click.prevent="selectedLabel = label._id; selectedLabelTitle = label.title; filterByLabel(label._id);"
                           :class="{'active': selectedLabel === label._id}">
                            <span x-text="label.title"></span>
                            <span class="custom-badge custom-badge-pill custom-ml-2" :style="'background-color: ' + label.color + '; color: ' + getContrastColor(label.color)" x-text="label.contacts_count"></span>
                        </a>
                    </template>
                    <div x-show="filteredLabels.length === 0" class="custom-px-3 custom-py-2 custom-text-muted custom-small">
                        <?php echo e(__tr('No labels found')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>
                          <div class="tab-content" id="nav-tabContent" x-cloak>
                            <div class="tab-pane fade show active" id="lwAllContactsTab" role="tabpanel" aria-labelledby="lw-all-contacts-tab">
                            <div class="form-group">
                                <input x-model="search" x-on:keyup.debounce.500ms="function(value) {
                                    window.searchValue = this.search;
                                    window.searchContacts();
                                }" x-ref="searchField" placeholder="<?php echo e(__tr('type to search')); ?>" type="search" class="form-control">
                            </div>
                            <div class="list-group lw-contact-list shadow-lg list-group-flush" >
                            <template x-for="contactItem in filteredContacts">
                                <?php if(($assigned ?? null)): ?>
                                
                                <?php endif; ?>
                                <a x-show="shouldShowContact(contactItem)"
                                    x-effect="() => {
                                        if (selectedLabel) {
                                            return contactItem.labels && 
                                                   Array.isArray(contactItem.labels) && 
                                                   contactItem.labels.some(label => label._id === selectedLabel);
                                        }
                                        return true;
                                    }"
                                   @click.prevent="isContactListOpened = false; contact = {}; whatsappMessageLogs = []; messagePaginatePage = 0; contact.__data = {}" 
                                   :data-messaged-at="contactItem.last_message?.messaged_at" 
                                   :data-has-labels="contactItem.labels && contactItem.labels.length > 0"
                                   :data-labels="JSON.stringify(contactItem.labels || [])"
                                   :class="[(contact && (contact._uid == contactItem._uid)) ? 'list-group-item-light' : '']"
                                   :href="__Utils.apiURL('<?php echo e(route('vendor.chat_message.contact.view', ['contactUid', 'assigned' => ($assigned ?? '')])); ?>',{'contactUid': contactItem._uid})"
                                   class="list-group-item list-group-item-action lw-contact lw-ajax-link-action" 
                                   data-callback="updateContactInfo">
                                    
                                    <div class="d-flex align-items-center ">
                                        <!-- Avatar -->
                                        <div class="lw-contact-avatar bg-success text-white text-center d-flex align-items-center justify-content-center" style="width: 48px; height: 48px; border-radius: 50%; margin-right: 15px !important; flex-shrink: 0;">
                                            <span x-text="contactItem.name_initials" style="font-size: 18px;"></span>
                                        </div>
                                        <!-- Contact Info -->
                                        <div class="lw-contact-info flex-grow-1 " style="min-width: 0;">
                                            <!-- Name -->
                                            <div class="lw-contact-name" style="font-size: 16px; font-weight: 500; margin-bottom: 2px; color: #111b21;">
                                                <span x-show="contactItem.full_name" x-text="contactItem.full_name"></span>
                                                <span x-show="!contactItem.full_name" x-text="contactItem.wa_id"></span>
                                            </div>
                                            <!-- Phone Number -->
                                            <div x-show="contactItem.full_name" class="lw-contact-phone" style="font-size: 14px; color: #667781;">
                                                <span x-text="contactItem.wa_id"></span>
                                            </div>
                                            <!-- Labels -->
                                            <div class="lw-contact-labels" style="margin: 2px 0; display: flex; flex-wrap: wrap; gap: 4px;">
                                                <template x-for="contactLabel in contactItem.labels || []" :key="contactLabel._id">
                                                    <span x-bind:style="'color:' + (contactLabel.text_color || '#ffffff') + ';background-color:' + (contactLabel.bg_color || '#00a884') + ';'"
                                                          class="badge label-badge"
                                                          x-text="contactLabel.title"
                                                          style="font-size: 10px; padding: 2px 6px; border-radius: 12px; font-weight: 500;">
                                                    </span>
                                                </template>
                                            </div>
                                            <!-- Timestamp -->
                                            <div class="lw-last-message-at" x-text="contactItem.last_message?.formatted_message_ago_time" style="font-size: 12px; color: #667781;"></div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-right w-100 mt-3">
                                        <span x-show="contactItem.unread_messages_count"
                                            class="badge bg-success rounded-pill"
                                            x-text="contactItem.unread_messages_count"></span>
                                    </div>
                                </a>
                                <?php if(($assigned ?? null)): ?>
                                
                                <?php endif; ?>
                            </template>
                        </div>
                    </div>
                    </div>
                    </div>
                    <div class="page col-sm-12 col-md-6 col-lg-6 col-xl-6 mb-4" :class="(!contact) ? 'lw-disabled-block-content' : ''" class="chat-container" x-cloak>
                        
                        <div class="marvel-device nexus5">
                            <div class="screen">
                                <div class="screen-container">
                                    <div class="chat" id="lwChatWindowBox">
                                        
                                            <div>
                                                <template x-if="contact">
                                                <div class="user-bar">
                                                    <div class="back d-md-none" @click.prevent="isContactListOpened = true">
                                                        <i class="fa fa-users"></i>
                                                    </div>
                                                    <div class="avatar d-none d-md-inline bg-success text-white text-center align-content-center">
                                                        <span x-text="contact.name_initials"></span>
                                                    </div>
                                                    <div class="name">
                                                        <span><span x-text="contact.full_name"></span><small> - <a target="_blank" x-bind:href="'https://api.whatsapp.com/send?phone=' + contact.wa_id" x-text="contact.wa_id"></a></small></span>
                                                        <template x-if="isDirectMessageDeliveryWindowOpened">
                                                            <span class="status text-success " x-text="directMessageDeliveryWindowOpenedTillMessage"></span>
                                                        </template>
                                                            <template x-if="!isDirectMessageDeliveryWindowOpened">
                                                            <span class="status text-yellow " title="<?php echo e(__tr("As you may not received any response in last 24 hours, your direct message may not get delivered. However you can send template messages.")); ?>"><?php echo e(__tr('You can\'t reply, they needs to reply back to start conversion.')); ?></span>
                                                             </template>
                                                    </div>
                                                    <template x-if="contact">
                                                    <div class="actions more lw-user-new-actions" x-data="{isAiChatBotEnabled:!contact.disable_ai_bot}" x-cloak>
                                                        <?php if(isAiBotAvailable()): ?>
                                                        <a :title="isAiChatBotEnabled ? '<?php echo e(__tr('Enable AI Bot')); ?>' : '<?php echo e(__tr('Disable AI Bot')); ?>'" x-bind:href="__Utils.apiURL('<?php echo e(route('vendor.contact.write.toggle_ai_bot', [ 'contactIdOrUid'])); ?>', {'contactIdOrUid': contact._uid})" :class="isAiChatBotEnabled ? 'text-yellow' : 'text-white'" class="lw-whatsapp-bar-icon-btn mr-3 lw-ajax-link-action" data-method="post">
                                                           <i class="fa fa-robot"></i>
                                                        </a>
                                                        <?php endif; ?>
                                                        <a href="#" class="lw-whatsapp-bar-icon-btn" data-toggle="dropdown" aria-expanded="false">
                                                            <i class="fas fa-ellipsis-v text-white"></i>
                                                        </a>
                                                        <div class="dropdown-menu dropdown-menu-right">
                                                        <a x-bind:href="__Utils.apiURL('<?php echo e(route('vendor.template_message.contact.view', [ 'contactIdOrUid'])); ?>', {'contactIdOrUid': contact._uid})" class="dropdown-item"><i class="fas fa-paper-plane"></i> <?php echo e(__tr('Send Template Message')); ?></a>
                                                        <a x-cloak
                                                            :class="whatsappMessageLogs.length <= 0 ? 'disabled' : ''"
                                                            data-method="post" data-confirm="#lwClearChatHistoryWarning" x-bind:href="__Utils.apiURL('<?php echo e(route('vendor.chat_message.delete.process', [ 'contactIdOrUid'])); ?>', {'contactIdOrUid': contact._uid})"
                                                            class="dropdown-item text-danger lw-ajax-link-action"><i class="fas fa-eraser"></i> <?php echo e(__tr('Clear Chat History')); ?></a>
                                                        <script type="text/template" id="lwClearChatHistoryWarning">
                                                            <h3><?php echo e(__tr('Are you sure you want to clear chat history for this contact?')); ?></h3>
                                                                <p class="text-warning"><?php echo e(__tr('Only chat history will be deleted permanently, it won\'t delete campaign messages.')); ?></p>
                                                            </script>
                                                        </div>
                                                        <span class="lw-whatsapp-bar-icon-btn ml-3 d-md-none" @click.prevent="isContactCrmBlockOpened = true"><i class="fa fa-user-tie"></i></span>
                                                    </div>
                                                    </template>
                                                </div>
                                                </template>
                                                <div class="conversation">
                                                    <div class="conversation-container" id="lwConversionChatContainer">
                                                            <div class="w-100" id="lwEndOfChats">&shy;</div>
                                                            <template x-for="whatsappMessageLogItem in whatsappMessageLogs">
                                                                <div class="lw-chat-message-item"
                                                                    :id="whatsappMessageLogItem._uid">
                                                                    <template
                                                                        x-if="whatsappMessageLogItem.is_incoming_message">
                                                                        <div class="message received">
                                                                            <template
                                                                                x-if="whatsappMessageLogItem.replied_to_whatsapp_message_logs__uid">
                                                                                <a href="#"
                                                                                    @click.prevent="lwScrollTo('#'+whatsappMessageLogItem.replied_to_whatsapp_message_logs__uid)"
                                                                                    class="badge d-flex text-muted justify-content-end"><i
                                                                                        class="fa fa-link"></i> <?php echo e(__tr('Replied to')); ?></a>
                                                                            </template>
                                                                            <template
                                                                                x-if="whatsappMessageLogItem.template_message">
                                                                                <div class="lw-template-message"
                                                                                    x-show="whatsappMessageLogItem.template_message"
                                                                                    x-html="whatsappMessageLogItem.template_message">
                                                                                </div>
                                                                            </template>
                                                                            <div x-show="whatsappMessageLogItem.message && !whatsappMessageLogItem.__data?.interaction_message_data"><span class="lw-plain-message-text" x-html="whatsappMessageLogItem.message"></span></div>
                                                                            <template
                                                                                x-if="(whatsappMessageLogItem.whatsapp_message_error)">
                                                                                <div class="p-1 mt-2">
                                                                                    <small class="text-danger"> <i
                                                                                            class="fas fa-exclamation-circle text-danger text-shadow"></i>
                                                                                        <em
                                                                                            x-text="whatsappMessageLogItem.whatsapp_message_error"></em></small>
                                                                                </div>
                                                                            </template>
                                                                            <span class="metadata"><span class="time"
                                                                                    x-text="whatsappMessageLogItem.formatted_message_time"></span></span>
                                                                        </div>
                                                                    </template>
                                                                    <template
                                                                        x-if="!whatsappMessageLogItem.is_incoming_message">
                                                                        <div class="message sent">
                                                                            <template
                                                                                x-if="whatsappMessageLogItem.__data?.options?.bot_reply">
                                                                                <span class="badge d-flex text-muted justify-content-end"
                                                                                    :title="whatsappMessageLogItem.__data?.options?.ai_bot_reply ? '<?php echo e(__tr('AI Bot Reply')); ?>' : '<?php echo e(__tr('Bot Reply')); ?>'">
                                                                                    <template x-if="whatsappMessageLogItem.__data?.options?.ai_bot_reply">
                                                                                        <span class="mr-1 text-warning">AI</span>
                                                                                    </template>
                                                                                    <i class="fas fa-robot text-muted"></i>
                                                                                </span>
                                                                            </template>
                                                                            <template
                                                                                x-if="whatsappMessageLogItem.campaigns__id">
                                                                                <span class="badge d-flex justify-content-end" title="<?php echo e(__tr('Campaign Message')); ?>">
                                                                                    <i class="fas fa-bullhorn text-info"></i>
                                                                                </span>
                                                                            </template>
                                                                            <template
                                                                                x-if="whatsappMessageLogItem.template_message">
                                                                                <div class="lw-template-message"
                                                                                    x-show="whatsappMessageLogItem.template_message"
                                                                                    x-html="whatsappMessageLogItem.template_message">
                                                                                </div>
                                                                            </template>
                                                                            <template x-if="whatsappMessageLogItem.message && !whatsappMessageLogItem.__data?.interaction_message_data">
                                                                                <div class="lw-template-message" x-show="whatsappMessageLogItem.message"><span class="lw-plain-message-text" x-html="whatsappMessageLogItem.message"></span>
                                                                                </div>
                                                                            </template>
                                                                            <template
                                                                                x-if="(whatsappMessageLogItem.whatsapp_message_error)">
                                                                                <div class="p-1 mt-2">
                                                                                    <small class="text-danger"> <i
                                                                                            class="fas fa-exclamation-circle text-danger text-shadow"></i>
                                                                                        <em
                                                                                            x-text="whatsappMessageLogItem.whatsapp_message_error"></em></small>
                                                                                </div>
                                                                            </template>
                                                                            <span class="metadata">
                                                                                <span class="time"
                                                                                    x-text="whatsappMessageLogItem.formatted_message_time"></span>
                                                                                <span class="tick">
                                                                                    <template
                                                                                        x-if="whatsappMessageLogItem.status == 'read'">
                                                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                                                            width="16" height="15"
                                                                                            id="msg-dblcheck-ack" x="2063"
                                                                                            y="2076">
                                                                                            <path
                                                                                                d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.88a.32.32 0 0 1-.484.032l-.358-.325a.32.32 0 0 0-.484.032l-.378.48a.418.418 0 0 0 .036.54l1.32 1.267a.32.32 0 0 0 .484-.034l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.88a.32.32 0 0 1-.484.032L1.892 7.77a.366.366 0 0 0-.516.005l-.423.433a.364.364 0 0 0 .006.514l3.255 3.185a.32.32 0 0 0 .484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"
                                                                                                fill="#4fc3f7" />
                                                                                        </svg>
                                                                                    </template>
                                                                                    <template
                                                                                        x-if="whatsappMessageLogItem.status == 'delivered'">
                                                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                                                            width="16" height="15"
                                                                                            id="msg-dblcheck" x="2047"
                                                                                            y="2061">
                                                                                            <path
                                                                                                d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.88a.32.32 0 0 1-.484.032l-.358-.325a.32.32 0 0 0-.484.032l-.378.48a.418.418 0 0 0 .036.54l1.32 1.267a.32.32 0 0 0 .484-.034l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.88a.32.32 0 0 1-.484.032L1.892 7.77a.366.366 0 0 0-.516.005l-.423.433a.364.364 0 0 0 .006.514l3.255 3.185a.32.32 0 0 0 .484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"
                                                                                                fill="#92a58c" />
                                                                                        </svg>
                                                                                    </template>
                                                                                    <template
                                                                                        x-if="whatsappMessageLogItem.status == 'sent'">
                                                                                        <svg width="16" height="16"
                                                                                            viewBox="0 0 24 24" fill="none"
                                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                                            <path
                                                                                                d="M4 12.6111L8.92308 17.5L20 6.5"
                                                                                                stroke="#92a58c"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round" />
                                                                                        </svg>
                                                                                    </template>
                                                                                    <template
                                                                                        x-if="whatsappMessageLogItem.status == 'failed'">
                                                                                        <i
                                                                                            class="fas fa-exclamation-circle text-danger"></i>
                                                                                    </template>
                                                                                    <template
                                                                                        x-if="(whatsappMessageLogItem.status == 'accepted')">
                                                                                        <i
                                                                                            class="far fa-clock text-muted"></i>
                                                                                    </template>
                                                                                </span>
                                                                            </span>
                                                                        </div>
                                                                    </template>
                                                                </div>
                                                            </template>
                                                            <div class="w-100 px-4" id="lwEndOfChats">&shy; <button x-cloak x-show="messagePaginatePage" class="btn btn-sm btn-block btn-secondary" @click="loadEarlierMessages" ><i class="fa fa-download"></i> <?php echo e(__tr('Load earlier messages')); ?></button></div>
                                                    </div>
                                                    <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['dataEventStreamUpdate' => 'true','dataCallback' => 'appFuncs.resetForm','id' => 'whatsAppMessengerForm','class' => 'conversation-compose','dataShowProcessing' => 'false','action' => route('vendor.chat_message.send.process')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['data-event-stream-update' => 'true','data-callback' => 'appFuncs.resetForm','id' => 'whatsAppMessengerForm','class' => 'conversation-compose','data-show-processing' => 'false','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('vendor.chat_message.send.process'))]); ?>
                                                        <input type="hidden" name="contact_uid" x-bind:value="contact?._uid">
                                                        
                                                        
                                                        <textarea name="message_body" required class="input-msg lw-input-emoji"
                                                            name="input" placeholder="<?php echo e(__tr(' Type a message')); ?>" autocomplete="off" autofocus></textarea>
                                                            <div class="photo dropup">
                                                                <!-- Default dropup button -->
                                                                <a href="#" class="lw-whatsapp-bar-icon-btn" data-toggle="dropdown" aria-expanded="false">
                                                                    <i class=" fa fa-paperclip text-muted"></i>
                                                                </a>
                                                                <div class="dropdown-menu dropdown-menu-right">
                                                                    <a title="<?php echo e(__tr('Send Document')); ?>"
                                                                class="lw-ajax-link-action dropdown-item" data-toggle="modal"
                                                                data-response-template="#lwWhatsappAttachment"
                                                                data-target="#lwMediaUploadAndSend"
                                                                data-callback="appFuncs.prepareUpload" href="<?php echo e(route('vendor.chat_message_media.upload.prepare', [
                                                                'mediaType' => 'document'
                                                            ])); ?>"><i class="fa fa-file text-muted"></i> <?php echo e(__tr('Send Document')); ?></a>
                                                            <a title="<?php echo e(__tr('Send Image')); ?>" class="lw-ajax-link-action dropdown-item"
                                                            data-toggle="modal"
                                                            data-response-template="#lwWhatsappAttachment"
                                                            data-target="#lwMediaUploadAndSend"
                                                            data-callback="appFuncs.prepareUpload" href="<?php echo e(route('vendor.chat_message_media.upload.prepare', [
                                                            'mediaType' => 'image'
                                                        ])); ?>"><i class="fa fa-image text-muted"></i> <?php echo e(__tr('Send Image')); ?></a>
                                                        <a title="<?php echo e(__tr('Send Video')); ?>" class="lw-ajax-link-action dropdown-item"
                                                        data-toggle="modal"
                                                        data-response-template="#lwWhatsappAttachment"
                                                        data-target="#lwMediaUploadAndSend"
                                                        data-callback="appFuncs.prepareUpload" href="<?php echo e(route('vendor.chat_message_media.upload.prepare', [
                                                        'mediaType' => 'video'
                                                    ])); ?>"><i class="fa fa-video text-muted"></i> <?php echo e(__tr('Send Video')); ?></a>
                                                    <a title="<?php echo e(__tr('Send Audio')); ?>" class="lw-ajax-link-action dropdown-item"
                                                    data-toggle="modal"
                                                    data-response-template="#lwWhatsappAttachment"
                                                    data-target="#lwMediaUploadAndSend"
                                                    data-callback="appFuncs.prepareUpload" href="<?php echo e(route('vendor.chat_message_media.upload.prepare', [
                                                    'mediaType' => 'audio'
                                                ])); ?>"><i class="fa fa-headphones text-muted"></i> <?php echo e(__tr('Send Audio')); ?></a>
                                                                </div>
                                                            </div>
                                                        <button class="send" type="submit">
                                                            <div class="circle pl-2">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="1.5em"
                                                                    height="1.5em" viewBox="0 0 24 24">
                                                                    <path fill="currentColor"
                                                                        d="M2.01 21L23 12L2.01 3L2 10l15 2l-15 2z" />
                                                                </svg>
                                                            </div>
                                                        </button>
                                                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
                                                    
                                                    <div data-form-id="#whatsAppMessengerForm"
                                                        class="lw-error-container-message_body p-2">
                                                    </div>
                                                </div>
                                            </div>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-3 col-lg-3 col-xl-3 mb-4 lw-contact-crm-block" :class="(!contact) ? 'lw-disabled-block-content' : ''" x-show="isContactCrmBlockOpened">
                            <div class="row">
                                <div class="col-12 text-right">
                                    <span class="btn btn-light btn-sm float-right d-md-none" @click.prevent="isContactCrmBlockOpened = false"><i class="fa fa-arrow-left"></i></span>
                                </div>
                                <template x-if="contact">
                                    <fieldset class="col-12 p-2 mt-0">
                                        <legend><?php echo e(__tr('Contact Info')); ?></legend>
                                        <?php if(hasVendorAccess('manage_contacts')): ?>
                                        <div class="text-right mt--3">
                                            <a data-pre-callback="appFuncs.clearContainer" title="<?php echo e(__tr('Edit')); ?>" class="lw-btn btn btn-sm btn-light lw-ajax-link-action" data-response-template="#lwEditContactBody" x-bind:href="__Utils.apiURL('<?php echo e(route('vendor.contact.read.update.data', [ 'contactIdOrUid'])); ?>', {'contactIdOrUid': contact._uid})"  data-toggle="modal" data-target="#lwEditContact"><i class="fa fa-user-edit"></i> <?php echo e(__tr('Edit Contact')); ?></a>
                                        </div>
                                        <?php endif; ?>
                                        <dl class="px-2">
                                            <dt><?php echo e(__tr('Name')); ?></dt>
                                            <dd x-text="contact.full_name"></dd>
                                            <dt><?php echo e(__tr('Phone')); ?></dt>
                                            <dd x-text="contact.wa_id"></dd>
                                            <dt><?php echo e(__tr('Email')); ?></dt>
                                            <dd x-text="contact.email ? contact.email : '-'"></dd>
                                            <dt><?php echo e(__tr('Language')); ?></dt>
                                            <dd x-text="contact.language_code ? contact.language_code : '-'"></dd>
                                        </dl>
                                    </fieldset>
                                </template>
                                <div class="col-12 p-0">
                                    <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['id' => 'lwAssignSystemUserForm','action' => route('vendor.chat.assign_user.process')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwAssignSystemUserForm','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('vendor.chat.assign_user.process'))]); ?>
                                        <input type="hidden" name="contactIdOrUid" :value="contact?._uid">
                                        
                                        <fieldset class="col-12 p-2">
                                            <legend><?php echo e(__tr('Assign Team Member')); ?></legend>
                                            <?php if (isset($component)) { $__componentOriginalc48421d24039f3f08f3bb9a3b9254b69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.input-field','data' => ['id' => 'lwCurrentlyAssignedUserUid','type' => 'selectize','dataFormGroupClass' => 'mt--4','name' => 'assigned_users_uid','class' => 'custom-select','dataSelected' => ''.e($currentlyAssignedUserUid).'','xModel' => 'currentlyAssignedUserUid']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.input-field'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwCurrentlyAssignedUserUid','type' => 'selectize','data-form-group-class' => 'mt--4','name' => 'assigned_users_uid','class' => 'custom-select','data-selected' => ''.e($currentlyAssignedUserUid).'','x-model' => 'currentlyAssignedUserUid']); ?>
                                             <?php $__env->slot('selectOptions', null, []); ?> 
                                                <option value=""><?php echo e(__tr('Not Assigned')); ?></option>
                                                <option value="no_one"><?php echo e(__tr('Not Assigned')); ?></option>
                                                <?php $__currentLoopData = $vendorMessagingUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendorMessagingUser): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($vendorMessagingUser->_uid); ?>"><?php echo e($vendorMessagingUser->first_name . ' ' . $vendorMessagingUser->last_name); ?> <?php if($vendorMessagingUser->_uid == getUserUID()): ?> (<?php echo e(__tr('You')); ?>) <?php endif; ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                             <?php $__env->endSlot(); ?>
                                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69)): ?>
<?php $attributes = $__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69; ?>
<?php unset($__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc48421d24039f3f08f3bb9a3b9254b69)): ?>
<?php $component = $__componentOriginalc48421d24039f3f08f3bb9a3b9254b69; ?>
<?php unset($__componentOriginalc48421d24039f3f08f3bb9a3b9254b69); ?>
<?php endif; ?>
                                            <div class="">
                                                <button type="submit" class="btn btn-dark btn-sm mt--1 float-right"><?php echo e(__tr('Save')); ?></button>
                                            </div>
                                        </fieldset>
                                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
                                </div>
                                <template x-if="contact">
                                    
                                    <fieldset class="col-12 p-2">
                                        
                                        <legend class="pb-0 pt-1"><?php echo e(__tr('Labels/Tags')); ?> <a data-pre-callback="appFuncs.clearContainer" title="<?php echo e(__tr('Manage Labels')); ?>" class="lw-btn btn btn-sm btn-link lw-ajax-link-action float-right pt-1" data-response-template="#lwManageContactLabelsBody" x-bind:href="__Utils.apiURL('<?php echo e(route('vendor.chat.contact_labels.read', [ 'contactUid'])); ?>', {'contactUid': contact._uid})"  data-toggle="modal" data-target="#lwManageContactLabels"><i class="fa fa-cog"></i></a></legend>

                                        <!-- Display Current Labels -->
                                        <div class="mb-3" x-show="contact.labels && contact.labels.length > 0">
                                            <div class="d-flex flex-wrap gap-2">
                                                <template x-for="label in contact.labels || []" :key="label._id">
                                                    <span x-bind:style="'color:' + (label.text_color || '#ffffff') + ';background-color:' + (label.bg_color || '#00a884') + ';'"
                                                          class="badge label-badge"
                                                          x-text="label.title"
                                                          style="font-size: 11px; padding: 4px 8px; border-radius: 12px; font-weight: 500; margin-right: 4px; margin-bottom: 4px;">
                                                    </span>
                                                </template>
                                            </div>
                                        </div>
                                        <div x-show="!contact.labels || contact.labels.length === 0" class="text-muted mb-3" style="font-size: 13px;">
                                            <?php echo e(__tr('No labels assigned')); ?>

                                        </div>

                                        <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['dataCallback' => 'onUpdateContactDetails','id' => 'lwAssignContactLabelsForm','action' => route('vendor.chat.assign_labels.process')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['data-callback' => 'onUpdateContactDetails','id' => 'lwAssignContactLabelsForm','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('vendor.chat.assign_labels.process'))]); ?>
                                                <input type="hidden" name="contactUid" x-bind:value="contact._uid" />
                                                <div x-show="labelsElement"></div>
                                                <select class="border-0 lw-borderers-selectize" id="lwAssignLabelsField" data-form-group-class="" x-bind:data-selected="assignedLabelIds" name="contact_labels[]" multiple >
                                                    <option value=""><?php echo e(__tr('Select Labels')); ?></option>
                                                        <?php $__currentLoopData = $allLabels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($label['_id']); ?>"><?php echo e($label['title']); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                <button type="submit" class="btn btn-dark btn-sm float-right"><?php echo e(__tr('Update')); ?></button>
                                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
                                    </fieldset>
                                </template>
                                <template x-if="contact">
                                    
                                    <fieldset class="col-12 p-2" x-data="{openNotesEdit:false,contactNotes:contact.__data?.contact_notes}">
                                        
                                        <legend class="pb-0 pt-1" for="lwContactNotes"><?php echo e(__tr('Notes')); ?> <button class="btn btn-link btn-sm float-right pt-1" @click="openNotesEdit = true"><i class="fas fa-edit"></i></button></legend>
                                        <div x-show="!openNotesEdit" class="lw-ws-pre-line px-2 pb-4" x-text="contact.__data?.contact_notes"></div>
                                        <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['xShow' => 'openNotesEdit','id' => 'lwNotesForm','action' => route('vendor.chat.update_notes.process')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['x-show' => 'openNotesEdit','id' => 'lwNotesForm','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('vendor.chat.update_notes.process'))]); ?>
                                            <input type="hidden" name="contactIdOrUid" :value="contact?._uid">
                                            <div class="form-group">
                                                <textarea name="contact_notes" id="lwContactNotes" class="form-control" x-bind:value="contact.__data?.contact_notes" x-model="contactNotes" rows="5"></textarea>
                                            </div>
                                            <div class="form-group">
                                                <button type="submit" class="btn btn-dark btn-sm mt--3" @click="openNotesEdit = false; if(!contact['__data']) { contact['__data'] = {}} contact['__data']['contact_notes'] = contactNotes;"><?php echo __tr('Save & Close'); ?></button>
                                            </div>
                                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
                                    </fieldset>
                                </template>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>
<?php if (isset($component)) { $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.modal','data' => ['id' => 'lwMediaUploadAndSend','header' => __tr('Send Media'),'hasForm' => true,'dataPreCallback' => 'clearModelContainer']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwMediaUploadAndSend','header' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__tr('Send Media')),'hasForm' => true,'data-pre-callback' => 'clearModelContainer']); ?>
    <!--  document form -->
    <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['id' => 'lwMediaUploadAndSendForm','action' => route('vendor.chat_message_media.send.process'),'dataCallback' => 'appFuncs.modelSuccessCallback','dataCallbackParams' => ['modalId' => '#lwMediaUploadAndSend']]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwMediaUploadAndSendForm','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('vendor.chat_message_media.send.process')),'data-callback' => 'appFuncs.modelSuccessCallback','data-callback-params' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['modalId' => '#lwMediaUploadAndSend'])]); ?>
        <!-- form body -->
        <input type="hidden" name="contact_uid" x-bind:value="contact?._uid">
        <div id="lwWhatsappAttachment" class="lw-form-modal-body"></div>
        <script type="text/template" id="lwWhatsappAttachment-template">
            <% if(__tData.mediaType == 'document') { %>
            <div class="form-group col-sm-12">
                <input id="lwDocumentMediaFilepond" type="file" data-allow-revert="true"
                    data-label-idle="<?php echo e(__tr('Select Document')); ?>" class="lw-file-uploader" data-instant-upload="true"
                    data-action="<?= route('media.upload_temp_media', 'whatsapp_document') ?>" id="lwDocumentField" data-file-input-element="#lwDocumentMedia" data-raw-upload-data-element="#lwRawDocumentMedia" data-allowed-media='<?= getMediaRestriction('whatsapp_document') ?>' />
                <input id="lwDocumentMedia" type="hidden" value="" name="uploaded_media_file_name" />
                <input type="hidden" value="document" name="media_type" />
            </div>
            <% } else if(__tData.mediaType == 'image') { %>
                <div class="form-group col-sm-12">
                    <input id="lwImageMediaFilepond" type="file" data-allow-revert="true"
                        data-label-idle="<?php echo e(__tr('Select Image')); ?>" class="lw-file-uploader" data-instant-upload="true"
                        data-action="<?= route('media.upload_temp_media', 'whatsapp_image') ?>" id="lwImageField" data-file-input-element="#lwImageMedia" data-raw-upload-data-element="#lwRawDocumentMedia" data-allowed-media='<?= getMediaRestriction('whatsapp_image') ?>' />
                    <input id="lwImageMedia" type="hidden" value="" name="uploaded_media_file_name" />
                    <input type="hidden" value="image" name="media_type" />
                </div>
                <% } else if(__tData.mediaType == 'video') { %>
                    <div class="form-group col-sm-12">
                        <input id="lwVideoMediaFilepond" type="file" data-allow-revert="true"
                            data-label-idle="<?php echo e(__tr('Select Video')); ?>" class="lw-file-uploader" data-instant-upload="true"
                            data-action="<?= route('media.upload_temp_media', 'whatsapp_video') ?>" id="lwVideoField" data-file-input-element="#lwVideoMedia" data-raw-upload-data-element="#lwRawDocumentMedia" data-allowed-media='<?= getMediaRestriction('whatsapp_video') ?>' />
                        <input id="lwVideoMedia" type="hidden" value="" name="uploaded_media_file_name" />
                        <input type="hidden" value="video" name="media_type" />
                    </div>
                <% } else if(__tData.mediaType == 'audio') { %>
                    <div class="form-group col-sm-12">
                        <input id="lwAudioMediaFilepond" type="file" data-allow-revert="true"
                            data-label-idle="<?php echo e(__tr('Select Audio')); ?>" class="lw-file-uploader" data-instant-upload="true"
                            data-action="<?= route('media.upload_temp_media', 'whatsapp_audio') ?>" id="lwAudioField" data-file-input-element="#lwAudioMedia" data-raw-upload-data-element="#lwRawDocumentMedia" data-allowed-media='<?= getMediaRestriction('whatsapp_audio') ?>' />
                        <input id="lwAudioMedia" type="hidden" value="" name="uploaded_media_file_name" />
                        <input type="hidden" value="audio" name="media_type" />
                    </div>
                <% } %>
                <input id="lwRawDocumentMedia" type="hidden" value="" name="raw_upload_data"/>
                <% if(__tData.mediaType != 'audio') { %>
                <div>
                    <label for="lwMediaCaptionText"><?php echo e(__tr('Caption/Text')); ?></label>
                    <textarea name="caption" id="lwCaptionField" class="form-control" rows="2"></textarea>
                </div>
                <% } %>
        </script>
        <!-- form footer -->
        <div class="modal-footer">
            <!-- Submit Button -->
            <button type="submit" class="btn btn-primary"><?php echo e(__('Send')); ?></button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__tr('Cancel')); ?></button>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
    <!--/  document form -->
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $attributes = $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $component = $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
 <!-- Edit Contact Modal -->
 <?php echo $__env->make('contact.contact-edit-modal-partial', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
 <!--/ Edit Contact Modal -->
 
 <?php if (isset($component)) { $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.modal','data' => ['id' => 'lwManageContactLabels','header' => __tr('Manage Labels'),'hasForm' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwManageContactLabels','header' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__tr('Manage Labels')),'hasForm' => true]); ?>
        <!-- form body -->
        <div id="lwManageContactLabelsBody" class="lw-form-modal-body"></div>
        <script type="text/template" id="lwManageContactLabelsBody-template">
            <fieldset class="pb-4 my-4">
                
                <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['dataCallback' => 'onNewLabelCreated','id' => 'lwManageContactLabelsForm','action' => route('vendor.chat.label.create.write')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['data-callback' => 'onNewLabelCreated','id' => 'lwManageContactLabelsForm','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('vendor.chat.label.create.write'))]); ?>
                    <div class="row">
                        <?php if (isset($component)) { $__componentOriginalc48421d24039f3f08f3bb9a3b9254b69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.input-field','data' => ['type' => 'text','id' => 'lwLabelFieldTitle','dataFormGroupClass' => 'col-12','label' => __tr('New Label'),'name' => 'title','required' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.input-field'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','id' => 'lwLabelFieldTitle','data-form-group-class' => 'col-12','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__tr('New Label')),'name' => 'title','required' => 'true']); ?>
                             <?php $__env->slot('append', null, []); ?> 
                            <input type="color" name="text_color" value="#ffffff" style="height: 50px;" title="<?php echo e(__tr('Label Text Color')); ?>" class="lw-color-field">
                            <input type="color" name="bg_color" value="#000000" style="height: 50px;" title="<?php echo e(__tr('Label BG Color')); ?>" class="lw-color-field">
                            <button type="submit" class="btn btn-primary"><?php echo e(__('Create')); ?></button>
                             <?php $__env->endSlot(); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69)): ?>
<?php $attributes = $__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69; ?>
<?php unset($__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc48421d24039f3f08f3bb9a3b9254b69)): ?>
<?php $component = $__componentOriginalc48421d24039f3f08f3bb9a3b9254b69; ?>
<?php unset($__componentOriginalc48421d24039f3f08f3bb9a3b9254b69); ?>
<?php endif; ?>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
            </fieldset>
            <fieldset>
                <legend><?php echo e(__tr('Labels')); ?></legend>
                    <ul class="list-group">
                        <template x-for="labelItem in allLabels">
                            <li x-bind:class="'lw-contact-label-'+labelItem._uid" class="list-group-item" >
                                <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['dataCallback' => 'onUpdateContactDetails','class' => 'w-100','action' => route('vendor.chat.label.update.write')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['data-callback' => 'onUpdateContactDetails','class' => 'w-100','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('vendor.chat.label.update.write'))]); ?>
                                    <div class="row">
                                        <input type="hidden" name="labelUid" x-bind:value="labelItem._uid" />
                                        <?php if (isset($component)) { $__componentOriginalc48421d24039f3f08f3bb9a3b9254b69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.input-field','data' => ['type' => 'text','dataFormGroupClass' => 'col-12','label' => __tr('Edit Label'),'name' => 'title','xBind:value' => 'labelItem.title','required' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.input-field'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','data-form-group-class' => 'col-12','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__tr('Edit Label')),'name' => 'title','x-bind:value' => 'labelItem.title','required' => 'true']); ?>
                                             <?php $__env->slot('append', null, []); ?> 
                                            <input type="color" name="text_color" x-bind:value="labelItem.text_color" style="height: 50px;" title="<?php echo e(__tr('Label Text Color')); ?>" class="lw-color-field">
                                            <input type="color" name="bg_color" x-bind:value="labelItem.bg_color" style="height: 50px;" title="<?php echo e(__tr('Label BG Color')); ?>" class="lw-color-field">
                                            <button type="submit" class="btn btn-primary"><?php echo e(__('Save')); ?></button>
                                            <a class="btn btn-outline-danger lw-ajax-link-action" data-confirm="<?php echo e(__tr('Are you sure you want to delete this label?')); ?>"  data-callback="updateManageLabelsList" data-method="post" x-bind:href="__Utils.apiURL('<?php echo e(route('vendor.chat.label.delete.write', ['labelUid'])); ?>',{'labelUid': labelItem._uid})"><i class="fa fa-trash"></i></a>
                                             <?php $__env->endSlot(); ?>
                                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69)): ?>
<?php $attributes = $__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69; ?>
<?php unset($__attributesOriginalc48421d24039f3f08f3bb9a3b9254b69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc48421d24039f3f08f3bb9a3b9254b69)): ?>
<?php $component = $__componentOriginalc48421d24039f3f08f3bb9a3b9254b69; ?>
<?php unset($__componentOriginalc48421d24039f3f08f3bb9a3b9254b69); ?>
<?php endif; ?>
                                    </div>
                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
                            </li>
                            </template>
                    </ul>
            </fieldset>
    </script>
        <!-- form footer -->
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__tr('Close')); ?></button>
        </div>
    <!--/  Edit Contact Form -->
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $attributes = $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $component = $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
 
</div>
<script>
     (function() {
        'use strict';
     document.addEventListener('alpine:init', () => {
        Alpine.data('initialMessageData', () => ({
            // Label filtering
            selectedLabel: '',
            labelSearch: '',
            allLabels: <?php echo json_encode($allLabels ?? [], 15, 512) ?>,
            filteredLabels: <?php echo json_encode($allLabels ?? [], 15, 512) ?>,
            
            // Initialize filtered labels
            initLabels() {
                this.filteredLabels = [...this.allLabels];
            },
            
            // Filter labels based on search input
            filterLabels() {
                if (!this.labelSearch.trim()) {
                    this.filteredLabels = [...this.allLabels];
                    return;
                }
                const searchTerm = this.labelSearch.toLowerCase();
                this.filteredLabels = this.allLabels.filter(label => 
                    label.title.toLowerCase().includes(searchTerm)
                );
            },
            
            // Filter contacts by label
            filterByLabel(labelId = '') {
                console.log('Filtering by label:', labelId);
                this.selectedLabel = labelId;
                this.selectedLabelTitle = labelId ? this.allLabels.find(l => l._id === labelId)?.title || '' : '';

                // Update URL with the selected label
                const url = new URL(window.location.href);
                if (labelId) {
                    url.searchParams.set('label', labelId);
                } else {
                    url.searchParams.delete('label');
                }
                window.history.pushState({}, '', url);

                // Apply the filter
                window.searchContacts();

                // Debug: Log filtered contacts count
                setTimeout(() => {
                    const visibleContacts = document.querySelectorAll('.lw-contact:not([style*="display: none"])').length;
                    const totalContacts = document.querySelectorAll('.lw-contact').length;
                    console.log(`Label filter applied: ${visibleContacts}/${totalContacts} contacts visible`);
                }, 100);
            },
            
            // Helper function to get contrast color for label text
            getContrastColor(hexColor) {
                // If the color is not valid, return black
                if (!hexColor) return '#000000';
                
                // Convert hex to RGB
                const r = parseInt(hexColor.substr(1, 2), 16);
                const g = parseInt(hexColor.substr(3, 2), 16);
                const b = parseInt(hexColor.substr(5, 2), 16);
                
                // Calculate luminance (perceived brightness)
                const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
                
                // Return black for light colors, white for dark colors
                return luminance > 0.5 ? '#000000' : '#ffffff';
            },
            // whatsappMessageLogs: <?php echo json_encode($whatsappMessageLogs, 15, 512) ?>,
            whatsappMessageLogs: [],
            messagePaginatePage: 0,
            contactsPaginatePage: 0,
            isDirectMessageDeliveryWindowOpened: <?php echo e($isDirectMessageDeliveryWindowOpened ?: 0); ?>,
            directMessageDeliveryWindowOpenedTillMessage: '<?php echo e($directMessageDeliveryWindowOpenedTillMessage); ?>',
            contact:<?php echo json_encode($contact, 15, 512) ?>,
            isContactDetailsUpdated: false,
            currentlyAssignedUserUid:'<?php echo e($currentlyAssignedUserUid); ?>',
            search: "",
            contacts: {},
            assignedLabelIds: [],
            unreadContactsCount() {
        return Object.values(this.contacts).filter(contact => contact.unread_messages_count > 0).length;
    },
    myAssignedUnreadContactsCount() {
        return Object.values(this.contacts).filter(contact => 
            contact.unread_messages_count > 0 && 
            contact.assigned_users__id == '<?php echo e(getUserId()); ?>'
        ).length;
    },
    myUnassignedUnreadContactsCount() {
        return Object.values(this.contacts).filter(contact => 
            contact.unread_messages_count > 0 && 
            !contact.assigned_users__id
        ).length;
    },
            filteredContacts: function () {
                return _.reverse(_.sortBy(this.contacts, [function(o) { return o.last_message?.messaged_at; }]));
            },
            
            shouldShowContact(contactItem) {
                // First check if contact matches basic visibility rules
                const basicVisibility = (
                    (this.contact && this.contact._uid == contactItem._uid) || 
                    (this.showUnreadContactsOnly && contactItem.unread_messages_count) || 
                    !this.showUnreadContactsOnly
                );
                
                // Then check if contact has the selected label
                const hasSelectedLabel = !this.selectedLabel || (
                    contactItem.labels && 
                    Array.isArray(contactItem.labels) && 
                    contactItem.labels.some(label => label._id === this.selectedLabel)
                );
                
                return basicVisibility && hasSelectedLabel;
            },
            labelsElement : function() {
                // reset the selectize
               var $labelsElement =  $('#lwAssignLabelsField').selectize({
                    maxItems: null,
                    items: _.values(this.assignedLabelIds),
                    valueField: '_id',
                    labelField: 'title',
                    searchField: 'title',
                    options: this.allLabels,
                    create: false,
                    closeAfterSelect: true,
                    render: {
                        item: function (item, escape) {
                            return (
                            '<div class="" style="color:'+item.text_color+';background-color:'+item.bg_color+';" >' +
                            (item.title
                                ? '<span>' + escape(item.title) + "</span>"
                                : "") +
                            "</div>"
                            );
                        },
                        option: function (item, escape) {
                            return (
                            '<div class="p-1 rounded m-2" style="color:'+item.text_color+';background-color:'+item.bg_color+';">' +
                            '<span>' +
                            escape(item.title) +
                            "</span>" +
                            "</div>"
                            );
                        },
                    }
                });
                $labelsElement[0].selectize.clear(true);
                $labelsElement[0].selectize.setValue(['']);
                $labelsElement[0].selectize.setValue(_.values(this.assignedLabelIds));
            }
        }));
    });
})();
</script>
<?php $__env->startPush('head'); ?>
    <?php echo __yesset('dist/emojionearea/emojionearea.min.css', true); ?>

<?php $__env->stopPush(); ?>
<?php $__env->startPush('appScripts'); ?>
<?php echo __yesset('dist/emojionearea/emojionearea.min.js', true); ?>

<script>
(function($) {
    'use strict';
    window.messagePaginatePage = 1;
    window.contactsPaginatePage = 1;
    window.searchValue = '';
        // Initialize from URL parameters
        document.addEventListener('alpine:initialized', () => {
            const component = document.querySelector('[x-data]').__x.$data;
            const url = new URL(window.location.href);
            const labelId = url.searchParams.get('label');
            const searchValue = url.searchParams.get('search') || '';
            
            // Set the search value if present
            if (searchValue) {
                window.searchValue = searchValue;
                const searchInput = document.querySelector('input[type="search"]');
                if (searchInput) {
                    searchInput.value = searchValue;
                }
            }
            
            // Set the label filter if present
            if (labelId) {
                const label = component.allLabels.find(l => l._id === labelId);
                if (label) {
                    component.selectedLabel = labelId;
                    component.selectedLabelTitle = label.title;
                }
            }
            
            // Apply filters after a short delay to ensure DOM is ready
            setTimeout(() => {
                window.searchContacts();
            }, 100);
        });
    window.showUnreadContactsOnly = 0;
    window.isLoadingMoreContacts = false;
    $(document).ready(function() {
    const contactListContainer = $('.lw-contact-list');
    
    contactListContainer.on('scroll', function() {
        // Check if we're near the bottom (within 200px of bottom)
        if (this.scrollHeight - this.scrollTop - this.clientHeight < 200) {
            // Only load more if there are more pages to load
            if (window.contactsPaginatePage > 0) {
                // Prevent multiple simultaneous requests
                if (!window.isLoadingMoreContacts) {
                    window.isLoadingMoreContacts = true;
                    window.loadMoreContacts();
                    
                    // Reset the loading flag after a short delay
                    setTimeout(function() {
                        window.isLoadingMoreContacts = false;
                    }, 1000);
                }
            }
        }
    });
});
    window.loadEarlierMessages = function(responseData, callbackParams) {
        __DataRequest.get(__Utils.apiURL('<?php echo route('vendor.chat_message.contact.view', ['contactUid', 'way' => 'prepend', 'page', 'assigned' => ($assigned ?? '')]); ?>',{'contactUid': $('#lwWhatsAppChatWindow').attr('data-contact-uid'),'page':'page='+ window.messagePaginatePage}),{}, function() {});
        if(callbackParams) {
            appFuncs.modelSuccessCallback(responseData, callbackParams);
        }
    };
    window.onUpdateContactDetails = function(responseData, callbackParams) {
        __DataRequest.get(__Utils.apiURL('<?php echo route('vendor.chat_message.contact.view', ['contactUid', 'current_page', 'assigned' => ($assigned ?? '')]); ?>',{'contactUid': $('#lwWhatsAppChatWindow').attr('data-contact-uid'),'current_page':'current_page='+ window.messagePaginatePage}),{}, function() {});
        if(callbackParams) {
            appFuncs.modelSuccessCallback(responseData, callbackParams);
        }
    };
    window.contactsPaginatePage = 1;
    window.loadMoreContacts = function(responseData, callbackParams) {
    // If responseData is provided and indicates no more pages, set contactsPaginatePage to 0
    if (responseData && responseData.data && responseData.data.hasMorePages === false) {
        window.contactsPaginatePage = 0;
        __DataRequest.updateModels({
            contactsPaginatePage: 0,
        });
        return;
    }
    
    __DataRequest.get(__Utils.apiURL("<?php echo route('vendor.contacts.data.read', ['contactUid', 'page' => '', 'way' => 'append', 'search' => '', 'unread_only' => '', 'assigned' => ($assigned ?? '')]); ?>", {'contactUid': $('#lwWhatsAppChatWindow').attr('data-contact-uid'),'page':'page='+ window.contactsPaginatePage + '&', 'search':'search='+ window.searchValue + '&', 'unread_only':'unread_only='+ window.showUnreadContactsOnly + '&'}),{}, function() {});
};
    window.searchContacts = function(responseData, callbackParams) {
        const url = new URL(window.location.href);
        const searchValue = window.searchValue || '';
        
        // Get the component instance
        const component = document.querySelector('[x-data]')?.__x?.$data;
        const labelId = component?.selectedLabel || '';
        
        // Update URL with search and label parameters
        if (searchValue) {
            url.searchParams.set('search', searchValue);
        } else {
            url.searchParams.delete('search');
        }
        
        if (labelId) {
            url.searchParams.set('label', labelId);
        } else {
            url.searchParams.delete('label');
        }
        
        window.history.pushState({}, '', url);
        
        // Get the contacts container and all contacts
        const contactsContainer = document.querySelector('.lw-contact-list');
        const contacts = contactsContainer?.querySelectorAll('.lw-contact') || [];
        
        // Apply both search and label filters
        contacts.forEach(contact => {
            const contactName = contact.textContent?.toLowerCase() || '';
            let labels = [];
            try {
                labels = JSON.parse(contact.getAttribute('data-labels') || '[]');
            } catch (e) {
                console.warn('Failed to parse labels for contact:', e);
                labels = [];
            }

            const hasLabel = !labelId || (Array.isArray(labels) && labels.some(label => label._id === labelId));
            const matchesSearch = !searchValue || contactName.includes(searchValue.toLowerCase());

            // Show contact if it matches both filters
            const shouldShow = hasLabel && matchesSearch;
            contact.style.display = shouldShow ? '' : 'none';

            // Debug logging for label filtering
            if (labelId && !hasLabel) {
                console.log('Contact filtered out by label:', {
                    contactName: contactName.substring(0, 20),
                    labelId,
                    contactLabels: labels.map(l => l._id),
                    hasLabel
                });
            }
        });
        
        // If this was called from a data request, update the contacts
        if (responseData) {
            updateContacts(responseData, callbackParams);
        }
        
        __DataRequest.get(__Utils.apiURL('<?php echo route('vendor.chat_message.contact.view', ['page' => 1, 'assigned' => ($assigned ?? '')]); ?>', {
            'search': window.searchValue || '',
            'unread_only': window.showUnreadContactsOnly || 0,
            'label_id': labelId,
            'page': 1
        }), {}, function(response) {});
    };
    window.updateContactList = function(respodata) {
        __DataRequest.get(__Utils.apiURL("<?php echo route('vendor.contacts.data.read', ['contactUid', 'page' => '', 'assigned' => ($assigned ?? '')]); ?>", {'contactUid': $('#lwWhatsAppChatWindow').attr('data-contact-uid'),'page':'page='+ window.contactsPaginatePage + '&'}),{}, function() {});
    };
    window.updateContactInfo = function(responseData) {
        $('#lwCurrentlyAssignedUserUid')[0].selectize.setValue(responseData.data.currentlyAssignedUserUid);
    };
    window.onNewLabelCreated = function(responseData) {
        $('#lwLabelFieldTitle').val('');
    };
    window.updateManageLabelsList = function(responseData) {
        if(responseData.reaction == 1) {
            window.onUpdateContactDetails();
        }
    };
    window.updateContactList();
    window.onUpdateContactDetails();
    // Initialize emoji area with proper configuration
    window.lwMessengerEmojiArea = $(".lw-input-emoji").emojioneArea({
        useInternalCDN: true,
        pickerPosition: "top",
        searchPlaceholder: "<?php echo e(__tr('Search')); ?>",
        buttonTitle: "<?php echo e(__tr('Use the TAB key to insert emoji faster')); ?>",
        placeholder: "<?php echo e(__tr('Type a message')); ?>",
        autocomplete: false,
        shortnames: false,
        sprite: true,
        hidePickerOnBlur: true,
        events: {
            'emojibtn.click': function (editor, event) {
                // Toggle picker instead of just hiding
                if (this.picker.is(':visible')) {
                    this.hidePicker();
                } else {
                    this.showPicker();
                }
            },
            'picker.show': function (editor, event) {
                // Ensure proper positioning
                var picker = this.picker;
                var button = this.button;

                // Position picker above the input
                picker.css({
                    'position': 'absolute',
                    'bottom': '100%',
                    'left': '0',
                    'margin-bottom': '8px',
                    'z-index': '1000'
                });
            },
            keyUp: function (editor, event) {
                if (event && event.which == 13 && !event.shiftKey && $.trim(this.getText())) { // On Enter
                    $('.lw-input-emoji').val(this.getText());
                    $('#whatsAppMessengerForm').submit();
                    this.hidePicker();
                    appFuncs.resetForm();
                }
            },
            ready: function() {
                // Ensure the emoji button is properly styled
                var button = this.button;
                button.css({
                    'background': 'none',
                    'border': 'none',
                    'color': '#54656f',
                    'font-size': '20px'
                });
            }
    }
});

// Fallback emoji button functionality
$(document).ready(function() {
    // If EmojiOneArea doesn't initialize properly, provide fallback
    setTimeout(function() {
        if (!$('.emojionearea').length) {
            console.log('EmojiOneArea not initialized, setting up fallback');

            // Create a simple emoji button
            $('#emoji-trigger').on('click', function(e) {
                e.preventDefault();

                // Try to initialize EmojiOneArea again
                if (typeof $.fn.emojioneArea !== 'undefined') {
                    $(".lw-input-emoji").emojioneArea({
                        useInternalCDN: true,
                        pickerPosition: "top",
                        searchPlaceholder: "<?php echo e(__tr('Search')); ?>",
                        buttonTitle: "<?php echo e(__tr('Use the TAB key to insert emoji faster')); ?>"
                    });
                } else {
                    alert('<?php echo e(__tr("Emoji functionality is loading...")); ?>');
                }
            });
        }
    }, 2000);

    // Ensure emoji picker closes when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.emojionearea').length) {
            $('.emojionearea-picker').hide();
        }
    });
});

})(jQuery);
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', ['title' => __tr('WhatsApp Chat')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\uniformat-app\omx-flow-new\resources\views/whatsapp/chat.blade.php ENDPATH**/ ?>