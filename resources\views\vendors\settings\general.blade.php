<!-- Page Heading -->
<h2>
    <i class="fas fa-cog"></i><?= __tr(' General Settings') ?>
</h2>
<!-- Page Heading -->
<!-- General setting form -->
<div class="row">
</div>
<fieldset style="background: #f8fcf9; border: 1px solid #d4edda; border-radius: 12px; padding: 24px; box-shadow: 0 4px 12px rgba(0,0,0,0.04);">
    <legend class="text-success font-weight-bold mb-3 text-center" style="color: #28a745; font-size: 1.2rem; font-weight: 600;">
        {{ __tr('Basic Settings') }}
    </legend>

    <form class="lw-ajax-form lw-form" data-show-processing="true" method="post"
        action="<?= route('vendor.settings_basic.write.update') ?>">
        
        <!-- Vendor Name -->
        <div class="form-group">
            <label for="lwVendorName" class="font-weight-bold text-dark">
                <?= __tr('Vendor Title') ?>
            </label>
            <input type="text" class="form-control border-success shadow-sm" 
                   style="border-radius: 8px;" 
                   name="store_name" id="lwVendorName"
                   value="{{ $basicSettings['title'] }}" required>
        </div>
        <!-- /Vendor Name -->

        <div class="text-right mt-4">
            <!-- Update Button -->
            <button type="submit" class="btn btn-success px-4 py-2 shadow-sm"
                style="border-radius: 8px; transition: 0.3s;">
                <i class="fas fa-save mr-1"></i> <?= __tr('Save') ?>
            </button>
            <!-- /Update Button -->
        </div>
    </form>
</fieldset>

<fieldset class="p-4 rounded shadow-sm" style="background-color: #f9fdfb; border: 1px solid #d4edda;">
    <legend class="font-weight-bold text-success mb-3" style="font-size: 1.25rem;">
        {{ __tr('Business Information') }}
    </legend>

    <form class="lw-ajax-form lw-form" data-show-processing="true" method="post" action="<?= route('vendor.settings.write.update') ?>">
        <input type="hidden" name="pageType" value="{{ $pageType }}">

        {{-- Address Section --}}
        <fieldset 
            class="mt-4 p-4 rounded" 
            style="border: 0.5px solid #28a745; background-color: #f5fdf8;"
        >
            <legend class="text-success font-weight-bold mb-3 text-center" style="font-size: 1.1rem;">
                {!! __tr('Address & Contact') !!}
            </legend>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <x-lw.input-field 
                        name="address" 
                        :label="__tr('Address line')" 
                        value="{{ $configurationData['address'] }}" 
                        required 
                        data-label-class="font-weight-bold text-dark"
                        data-input-class="form-control shadow-sm border-success"
                    />
                </div>
                <div class="col-md-4 mb-3">
                    <x-lw.input-field 
                        name="postal_code" 
                        :label="__tr('Postal Code')" 
                        value="{{ $configurationData['postal_code'] }}" 
                        required 
                        data-label-class="font-weight-bold text-dark"
                        data-input-class="form-control shadow-sm border-success"
                    />
                </div>
                <div class="col-md-4 mb-3">
                    <x-lw.input-field 
                        name="city" 
                        :label="__tr('City')" 
                        value="{{ $configurationData['city'] }}" 
                        required 
                        data-label-class="font-weight-bold text-dark"
                        data-input-class="form-control shadow-sm border-success"
                    />
                </div>
                <div class="col-md-4 mb-3">
                    <x-lw.input-field 
                        name="state" 
                        :label="__tr('State')" 
                        value="{{ $configurationData['state'] }}" 
                        required 
                        data-label-class="font-weight-bold text-dark"
                        data-input-class="form-control shadow-sm border-success"
                    />
                </div>
                <div class="col-md-4 mb-3">
                    <x-lw.input-field 
                        type="selectize" 
                        data-label-class="font-weight-bold text-dark"
                        data-lw-plugin="lwSelectize" 
                        name="country" 
                        :label="__tr('Select Country')" 
                        required
                    >
                        <x-slot name="selectOptions">
                            <option value="">{{ __tr('Select Country') }}</option>
                            @foreach ($configurationData['countries_list'] as $country)
                                <option value="{{ $country['id'] }}" {{ $configurationData['country'] == $country['id'] ? 'selected' : '' }}>
                                    {{ $country['name'] }}
                                </option>
                            @endforeach
                        </x-slot>
                    </x-lw.input-field>
                </div>
                <div class="col-md-4 mb-3">
                    <x-lw.input-field 
                        type="number" 
                        name="contact_phone" 
                        :label="__tr('Business Phone')" 
                        value="{{ $configurationData['contact_phone'] }}" 
                        required 
                        data-label-class="font-weight-bold text-dark"
                        data-input-class="form-control shadow-sm border-success"
                    />
                </div>
                <div class="col-md-4 mb-3">
                    <x-lw.input-field 
                        id="lwContactEmail" 
                        type="email" 
                        name="contact_email" 
                        :label="__tr('Contact Email')" 
                        value="{{ $configurationData['contact_email'] }}" 
                        required 
                        data-label-class="font-weight-bold text-dark"
                        data-input-class="form-control shadow-sm border-success"
                    />
                </div>
            </div>
        </fieldset>


        {{-- Other Settings --}}
        <fieldset class="mt-3 p-4 rounded" 
            style="border: 0.5px solid #28a745; background-color: #f5fdf8;">
            <legend class="text-success" style="font-size: 1rem; font-weight: 600;">{{ __tr('Other') }}</legend>
            <div class="row">
                <x-lw.input-field 
                    type="selectize" 
                    data-label-class="font-weight-bold text-dark"
                    data-form-group-class="col-md-4 mb-3" 
                    name="timezone" 
                    :label="__tr('Select Timezone')" 
                    data-selected="{{ getVendorSettings('timezone') }}" 
                    required
                >
                    <x-slot name="selectOptions">
                        @foreach ($configurationData['timezone_list'] as $timezone)
                            <option value="{{ $timezone['value'] }}">{{ $timezone['text'] }}</option>
                        @endforeach
                    </x-slot>
                </x-lw.input-field>

                <x-lw.input-field 
                    type="selectize" 
                    data-label-class="font-weight-bold text-dark"
                    data-form-group-class="col-md-4 mb-3" 
                    name="default_language" 
                    :label="__tr('Default Language')"  
                    data-selected="{{ getVendorSettings('default_language') }}" 
                    placeholder="{{ __tr('Select default language') }}" 
                    required
                >
                    <x-slot name="selectOptions">
                        @if (!__isEmpty($configurationData['languageList']))
                            @foreach ($configurationData['languageList'] as $language)
                                <option value="{{ $language['id'] }}">{{ $language['name'] }}</option>
                            @endforeach
                        @endif
                    </x-slot>
                </x-lw.input-field>
            </div>
        </fieldset>

        {{-- Save Button --}}
        <div class="row mt-4">
            <div class="col-12 text-right">
                <button type="submit" class="btn btn-success shadow-sm px-4 py-2" style="border-radius: 8px;">
                    <i class="fas fa-save mr-1"></i> {{ __tr('Save') }}
                </button>
            </div>
        </div>
    </form>
</fieldset>

@if (getAppSettings('pusher_by_vendor'))
<!--Pusher key -->
<fieldset id="pusherKeysConfiguration">
    <legend>{{ __tr('Pusher - required for realtime updates') }}</legend>
    <form class="lw-ajax-form lw-form" method="post" action="<?= route('vendor.settings.write.update') ?>" x-cloak
        x-data="{pusherSettingsExists: {{ getVendorSettings('pusher_app_id') ? 1 : 0 }}}">
        <input type="hidden" name="pageType" value="pusher">
        <div x-show="pusherSettingsExists"></div>
        <div class="form-group" x-cloak x-show="pusherSettingsExists">
            <div class="btn-group">
                <button type="button" disabled="true" class="btn btn-success lw-btn">
                    {{ __tr('Pusher Settings are exist') }}
                </button>
                <button type="button" @click="pusherSettingsExists = !pusherSettingsExists"
                    class="btn btn-light lw-btn">{{ __tr('Update') }}</button>
            </div>
        </div>
        <div x-show="!pusherSettingsExists" >
            <div class="col-sm-12 col-md-6 col-lg-4">
            <x-lw.input-field type="text" id="lwPusherAppId" data-form-group-class="" :label="__tr('App ID')"
                name="pusher_app_id" required="true" />
            <x-lw.input-field type="text" id="lwPusherKey" data-form-group-class="" :label="__tr('App Key')"
                name="pusher_app_key" required="true" />
            <x-lw.input-field type="text" id="lwPusherAppSecret" data-form-group-class="" :label="__tr('App Secret')"
                name="pusher_app_secret" required="true" />
            <x-lw.input-field type="text" id="lwPusherAppCluster" data-form-group-class="" :label="__tr('App Cluster')"
                name="pusher_app_cluster" required="true" />
            </div>
            <!--  Button -->
            <div class="col-sm-12 col-md-6 col-lg-4 mt-3">
                    <!-- Update Button -->
                    <button type="submit" class="btn btn-primary btn-user lw-btn-block-mobile">
                        <?= __tr('Save') ?>
                    </button>
                    <!-- /Update Button -->
            </div>
            <!-- / Button -->
        </div>
    </form>
</fieldset>
<!--/Pusher key -->
@endif

<!-- /General setting form -->
@push('appScripts')
<script>
    (function($) {
        'use strict';
        // After file successfully uploaded then this function is called
        window.afterUploadedFile = function (responseData) {
            var requestData = responseData.data;
            $('#lwUploadedLogo').attr('src', requestData.path);
        }
    })(jQuery);
</script>
@endpush