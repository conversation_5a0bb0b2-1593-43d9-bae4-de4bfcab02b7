<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Yantrana\Components\Auth\Models\AuthModel;
use App\Services\SSOApiLogger;

class SSOApiAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            // Get the Authorization header
            $authHeader = $request->header('Authorization');
            
            if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                return response()->json([
                    'reaction' => 0,
                    'message' => 'Authorization token required',
                    'messageType' => 1
                ], 401);
            }

            // Extract the token
            $token = substr($authHeader, 7); // Remove 'Bearer ' prefix
            
            if (!$token) {
                return response()->json([
                    'reaction' => 0,
                    'message' => 'Invalid authorization token',
                    'messageType' => 1
                ], 401);
            }

            // Decode JWT token
            $payload = JWT::decode($token, new Key(env('SSO_SECRET', 'your-secret-key'), 'HS256'));
            
            if (!isset($payload->email)) {
                return response()->json([
                    'reaction' => 0,
                    'message' => 'Invalid token payload',
                    'messageType' => 1
                ], 401);
            }

            // Find user by email using AuthModel
            $user = AuthModel::where('email', $payload->email)->first();
            
            if (!$user) {
                return response()->json([
                    'reaction' => 0,
                    'message' => 'User not found',
                    'messageType' => 1
                ], 401);
            }

            // Check user status
            if ($user->status != 1) {
                return response()->json([
                    'reaction' => 0,
                    'message' => 'User account is not active',
                    'messageType' => 1
                ], 403);
            }

            // Authenticate the user for this request
            Auth::loginUsingId($user->_id);
            
          
            return $next($request);

        } catch (\Firebase\JWT\ExpiredException $e) {
            return response()->json([
                'reaction' => 0,
                'message' => 'Token has expired',
                'messageType' => 1
            ], 401);
        } catch (\Firebase\JWT\InvalidTokenException $e) {
            return response()->json([
                'reaction' => 0,
                'message' => 'Invalid token',
                'messageType' => 1
            ], 401);
        } catch (\Exception $e) {
            Log::error('SSO API Auth Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_url' => $request->fullUrl(),
                'request_method' => $request->method()
            ]);
            
            return response()->json([
                'reaction' => 0,
                'message' => 'Authentication failed',
                'messageType' => 1
            ], 500);
        }
    }
}
