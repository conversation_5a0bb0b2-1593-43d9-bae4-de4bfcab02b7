@extends('layouts.app', ['title' => $contact ? __tr('Send WhatsApp Template Message') : __tr('Create New Campaign')])
@section('content')
@include('users.partials.header', [
'title' => $contact ? __tr('Send WhatsApp Template Message') : __tr(''),
'description' => '',
// 'class' => 'col-lg-7'
])

<div class="container-fluid mt-lg--6">
          <div class="row mt-3">
              <!-- button -->
            <div class="col-xl-12 mb-3 mt-5">
                @if ($contact)
                    <a class="lw-btn btn btn-secondary mb-2" href="{{ route('vendor.contact.read.list_view') }}">
                        {{ __tr('Back to Contacts') }}
                    </a>
                @endif

                <!-- Flex container for title and buttons -->
                <div class="d-flex justify-content-between align-items-center flex-wrap mb-3">
                    <!-- Left: Title -->
                    <h1 class="page-title mb-0" style="color: #22A755;">
                        <i class="fas fa-file-alt me-2"></i>{{ __tr(' Create New Campaign') }}
                    </h1>

                    <!-- Right: Buttons -->
                    <div class="d-flex gap-2 mt-2 mt-sm-0">
                        <a class="lw-btn btn btn-success btn-md text-white lw-ajax-link-action"
                            data-confirm="{{ __tr('On template sync page will be refreshed') }}"
                            data-callback="__Utils.viewReload"
                            data-method="post"
                            style="transition: background-color 0.3s ease; margin-right: 8px;" 
                            onmouseover="this.style.backgroundColor='#21B55F'"
                            onmouseout="this.style.backgroundColor='#22A755'"
                            href="{{ route('vendor.whatsapp_service.templates.write.sync') }}">
                                <i class="fas fa-sync-alt me-2"></i>{{ __tr(' Sync WhatsApp Templates') }}
                        </a>

                        <a class="lw-btn btn btn-seconday btn-md text-white"
                            style="background-color: #003366; transition: background-color 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#002855'" 
                            onmouseout="this.style.backgroundColor='#003366'" 
                            href="{{ route('vendor.campaign.read.list_view') }}">
                            {{ __tr('Manage Campaigns') }}
                        </a>
                    </div>
                </div>
            </div>

    <!--/ button -->
    <div class="col-12">
        <div class="card">
            @if ($contact)
            <div class="card-header">
                <div>{{  __tr('Name') }} : {{ $contact->full_name }}</div>
                <div>{{  __tr('Phone') }} : {{ $contact->wa_id }}</div>
                <div>{{  __tr('Country') }} : {{ $contact->country?->name }}</div>
            </div>
            @else
                @if(!getVendorSettings('test_recipient_contact'))
                <div class="card-body">
                    <div class="alert alert-danger">
                        {{  __tr('Test Contact missing, You need to set the Test Contact first, do it under the WhatsApp Settings') }}
                    </div>
                </div>
                @endif
            @endif
            <div class="card-body" x-data="{selectedTemplate:'' }">
                <div class="col-sm-12 col-md-8 col-lg-6">
                    @if (!$contact)
                    <h2 class="text-warning">{{  __tr('Step 1') }}</h2>
                    @endif
                    <x-lw.form lwSubmitOnChange data-event-callback="lwPrepareUploadPlugIn"
                        :action="route('vendor.request.template.view')" data-pre-callback="clearTemplateContainer">
                        <div x-cloak x-show="!selectedTemplate">
                            <x-lw.input-field x-model="selectedTemplate"
                                placeholder="{!! __tr('Select & Configure Template') !!}" type="selectize"
                                data-lw-plugin="lwSelectize" data-selected=" " type="select"
                                id="lwField_templateSelection" name="template_selection" data-form-group-class=""
                                class="custom-select" data-selected=" " :label="__tr('Select Template')">
                                <x-slot name="selectOptions">
                                    <option value="">{{ __tr('Select & Configure Template') }}</option>
                                    @foreach ($whatsAppTemplates as $whatsAppTemplate)
                                    <option value="{{ $whatsAppTemplate->_uid }}">{{ $whatsAppTemplate->template_name }}
                                        ({{ $whatsAppTemplate->language }})</option>
                                    @endforeach
                                </x-slot>
                            </x-lw.input-field>
                        </div>
                    </x-lw.form>
                </div>
                <div x-cloak class="col-12">
                        @if ($contact)
                        <x-lw.form x-show="selectedTemplate" :action="route('vendor.template_message.contact.process', [
                            'contactUid' => $contact->_uid
                        ])">
                            <input type="hidden" name="contact_uid" value="{{ $contact->_uid }}">
                            <div id="lwTemplateStructureContainer">
                                {!! $template !!}
                            </div>
                             @include('whatsapp.from-phone-number')
                            <button type="submit" class="btn btn-primary mt-4">{{ __('Send') }}</button>
                        </x-lw.form>
                        @else
                        {{-- Campaign Creation --}}
                        <x-lw.form x-show="selectedTemplate" 
                            :action="route('vendor.campaign.schedule.process')" 
                            data-confirm="#lwScheduleMessageConfirmation"
                            class="p-4 rounded shadow-sm animate__animated animate__fadeIn"
                            style="border: 1px solid #22A755; background-color: #ffffff;">
                            
                            <div id="lwTemplateStructureContainer">
                                {!! $template !!}
                            </div>

                            <h2 class="mt-5 text-warning">{{ __tr('Step 2') }}</h2>

                            <fieldset 
                                class="col-sm-12 col-md-8 col-lg-6 p-4 mb-4 rounded"
                                style="border: 1px solid #22A755; background-color: #f9fff9; transition: border-color 0.3s ease; margin-right: 10px;">
                                
                                <legend style="color: #22A755; font-weight: 600; font-size: 1.3rem;">
                                    {{ __tr('Contacts and Schedule') }}
                                </legend>

                                {{-- Campaign Title --}}
                                <x-lw.input-field type="text" id="lwCampaignTitle" :label="__tr('Campaign Title')" name="title" required />

                                {{-- Contact Selection Toggle --}}
                                <div class="form-group mb-4">
                                    <label class="d-block mb-2">{{ __('Select Contact Source') }}</label>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="contact_source" 
                                            id="contactSourceGroup" value="group" checked
                                            x-on:change="document.getElementById('groupSelection').classList.remove('d-none');
                                                      document.getElementById('phoneNumberSelection').classList.add('d-none');">
                                        <label class="form-check-label" for="contactSourceGroup">
                                            {{ __('Select from Groups/Contacts') }}
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="contact_source" 
                                            id="contactSourcePhone" value="phone"
                                            x-on:change="document.getElementById('phoneNumberSelection').classList.remove('d-none');
                                                      document.getElementById('groupSelection').classList.add('d-none');">
                                        <label class="form-check-label" for="contactSourcePhone">
                                            {{ __('Enter Phone Numbers') }}
                                        </label>
                                    </div>
                                </div>

                                {{-- Group Selection --}}
                                <div id="groupSelection">
                                    <x-lw.input-field type="selectize" data-lw-plugin="lwSelectize" id="lwSelectGroupsField"
                                        :label="__tr('Select Group/Contact')" name="contact_group">
                                        <x-slot name="selectOptions">
                                            <option value="" data-contact-count="0">{{ __tr('Select Contacts Group') }}</option>
                                            <option value="all_contacts" data-contact-count="{{ $totalContacts ?? 0 }}">
                                                {{ __tr('All Contacts') }}
                                            </option>
                                            @foreach($vendorContactGroups as $vendorContactGroup)
                                                @php
                                                    $contactCount = $vendorContactGroup->contacts_count ?? $vendorContactGroup->contact_count ?? 0;
                                                @endphp
                                                <option value="{{ $vendorContactGroup['_id'] }}" data-contact-count="{{ $contactCount }}">
                                                    {{ $vendorContactGroup['title'] }} ({{ $contactCount }})
                                                </option>
                                            @endforeach
                                        </x-slot>
                                    </x-lw.input-field>
                                </div>

                                {{-- Phone Numbers Input --}}
                                <div id="phoneNumberSelection" class="d-none">
                                    <div class="form-group">
                                        <label class="form-label" for="phoneNumbers">
                                            {{ __tr('Enter phone numbers (comma-separated)') }}
                                        </label>
                                        <textarea 
                                            class="form-control" 
                                            id="phoneNumbers" 
                                            name="phone_numbers" 
                                            rows="2" 
                                            placeholder="{{ __tr('e.g. 1234567890, 9876543210') }}"
                                        >{{ old('phone_numbers') }}</textarea>
                                        <small class="form-text text-muted">
                                            {{ __tr('Enter phone numbers separated by commas. Each number should be 10-15 digits.') }}
                                        </small>
                                    </div>
                                </div>

                                {{-- Restrict by Template Language --}}
                                <div class="form-group pt-3">
                                    <label for="lwOnlyForTemplateLanguageMatchingContact" class="text-muted">
                                        <input type="checkbox" id="lwOnlyForTemplateLanguageMatchingContact" 
                                            data-lw-plugin="lwSwitchery" data-color="#22A755" 
                                            name="restrict_by_templated_contact_language">
                                        {!! __tr('Restrict by Language Code - Send only to the contacts whose language code matches with template language code.') !!}
                                    </label>
                                </div>

                                {{-- Schedule Field --}}
                                <fieldset x-data="{scheduleNow:true}">
                                    <legend class="mt-4 text-success fw-bold">{{ __tr('Schedule') }}</legend>
                                    <div class="form-group pt-2">
                                        <label for="lwNowCampaign">
                                            <input x-model="scheduleNow" type="checkbox" id="lwNowCampaign" 
                                                data-lw-plugin="lwSwitchery" checked 
                                                data-color="#22A755" name="schedule_now">
                                            {{ __tr('Now') }}
                                        </label>
                                    </div>

                                    {{-- Timezone + Schedule At --}}
                                    <div x-show="!scheduleNow" class="animate__animated animate__fadeIn">
                                        <x-lw.input-field type="selectize" name="timezone" :label="__tr('Select your Timezone')" 
                                            data-selected="{{ getVendorSettings('timezone') }}">
                                            <x-slot name="selectOptions">
                                                @foreach (getTimezonesArray() as $timezone)
                                                    <option value="{{ $timezone['value'] }}">{{ $timezone['text'] }}</option>
                                                @endforeach
                                            </x-slot>
                                        </x-lw.input-field>

                                        <x-lw.input-field type="datetime-local" id="lwScheduleAt" 
                                            min="{{ formatDateTime(now(), 'Y-m-d\TH:i:s') }}"
                                            :label="__tr('Schedule At')" name="schedule_at" required />
                                    </div>
                                </fieldset>
                            </fieldset>

                            {{-- Phone Number Include --}}
                            @include('whatsapp.from-phone-number')

                            {{-- Submit Button --}}
                            <div class="my-4 text-center">
                                <button type="submit" 
                                    class="btn btn-success btn-md px-5 animate__animated animate__pulse animate__delay-1s"
                                    style="transition: background-color 0.3s ease;">
                                    {{ __('Schedule Campaign ') }}<i class="fas fa-paper-plane me-2"></i>
                                </button>
                            </div>
                        </x-lw.form>

                        <template type="text/template" id="lwScheduleMessageConfirmation">
                            <h3>{{  __tr('Are you sure?') }}</h3>
                            <p>{{  __tr('You want to schedule a WhatsApp Template Message. Test message will be sent to your selected test contact immediately and on success it will get scheduled for the selected group contacts ') }}</p>
                        </template>
                        @endif
                </div>
            </div>
        </div>
    </div>
          </div>
</div>
@endsection()
@push('appScripts')
<script>
    (function($){
        'use strict';
        window.clearTemplateContainer = function(inputData) {
            $('#lwTemplateStructureContainer').text('');
            return inputData;
        };
        
        // Form validation for contact source
        function validateContactSource() {
            const contactSource = document.querySelector('input[name="contact_source"]:checked').value;
            const groupSelect = document.getElementById('lwSelectGroupsField');
            const phoneNumbers = document.getElementById('phoneNumbers');
            
            if (contactSource === 'group') {
                // Validate group selection
                if (!groupSelect.value) {
                    alert('{{ __tr("Please select a contact group") }}');
                    return false;
                }
            } else {
                // Validate phone numbers
                const numbers = phoneNumbers.value.split(',').map(n => n.trim()).filter(n => n);
                if (numbers.length === 0) {
                    alert('{{ __tr("Please enter at least one phone number") }}');
                    return false;
                }
                
                // Validate phone number format
                const phoneRegex = /^[0-9]{10,15}$/;
                for (const number of numbers) {
                    if (!phoneRegex.test(number)) {
                        alert(`{{ __tr("Invalid phone number format: ") }}${number}`);
                        return false;
                    }
                }
            }
            return true;
        }
        
        // Add form submission handler
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form[data-confirm="#lwScheduleMessageConfirmation"]');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validateContactSource()) {
                        e.preventDefault();
                        return false;
                    }
                    return true;
                });
            }
            
            // Initialize the correct tab based on previous input or default
            const savedContactSource = '{{ old('contact_source', 'group') }}';
            if (savedContactSource === 'phone') {
                document.getElementById('contactSourcePhone').click();
            }
        });
        
        @if(request()->use_template)
        // Initial Change if required
        __DataRequest.post('{{ route('vendor.request.template.view') }}', {
            'template_selection': '{{ request()->use_template }}',
        }, function() {
            __DataRequest.updateModels({selectedTemplate: '{{ request()->use_template }}'});
            _.defer(function(){
                if ($('#lwTemplateStructureContainer').find('.lw-file-uploader').length) {
                    window.initUploader();
                }
            });
        }, {
            eventStreamUpdate: true
        });
        @endif
    })(jQuery);
    
    // Handle contact group selection changes
    document.addEventListener('DOMContentLoaded', function() {
        const groupSelect = document.getElementById('lwSelectGroupsField');
        if (groupSelect) {
            groupSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const contactCount = selectedOption ? selectedOption.getAttribute('data-contact-count') || '0' : '0';
                // You can add any additional handling here if needed
            });
        }
    });
                }
                
                // Update the data attribute to maintain the correct count
                $allContactsOption.data('contact-count', contactCount || totalContacts);
            }
        });
    </script>
@endpush