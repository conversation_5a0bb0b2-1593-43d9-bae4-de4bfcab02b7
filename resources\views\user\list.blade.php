@php
/**
* Component : User
* Controller : UserController
* File : User.list.blade.php
* ----------------------------------------------------------------------------- */
@endphp
@extends('layouts.app', ['title' => __tr('Agents')])
@section('content')
@include('users.partials.header', [
'title' => __tr(''),
'description' => '',
'class' => 'col-lg-7'
])
<div class="container-fluid mt-lg--6">
    <div class="row mt-3">
        <!-- button -->
        <div class="col-xl-12 mb-3 mt-5">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <!-- Left Side: Heading -->
                <h1 class="page-title mb-0" style="color: #22A755;">
                    <i class="fas fa-headset"></i> {{ __tr('Agents') }}
                </h1>

                <!-- Right Side: Button -->
                <button type="button"
                    class="btn rounded px-4 py-2 text-white"
                    style="background-color: #146c43; border: none; margin-right: 15px;"
                    data-toggle="modal"
                    data-target="#lwAddNewUser">
                    <i class="fa fa-plus-circle"></i>{{ __tr(' Add New Agent') }}
                </button>
            </div>
        </div>

        <!--/ button -->
        <!-- Add New User Modal -->
        <x-lw.modal id="lwAddNewUser" :header="__tr('Add New Agent')" :hasForm="true">
            <!--  Add New User Form -->
            <x-lw.form id="lwAddNewUserForm" :action="route('vendor.user.write.create')"
                :data-callback-params="['modalId' => '#lwAddNewUser', 'datatableId' => '#lwUserList']"
                data-callback="appFuncs.modelSuccessCallback">
                <!-- form body -->
                <div class="lw-form-modal-body">
                    <!-- form fields form fields -->
                    <!-- First_Name -->
                    <x-lw.input-field type="text" id="lwFirstNameField" data-form-group-class=""
                        :label="__tr('First Name')" name="first_name" required="true" />
                    <!-- /First_Name -->
                    <!-- Last_Name -->
                    <x-lw.input-field type="text" id="lwLastNameField" data-form-group-class=""
                        :label="__tr('Last Name')" name="last_name" required="true" />
                    <!-- /Last_Name -->
                    <x-lw.input-field type="number" id="lwMobileNumberField" data-form-group-class=""
                        :label="__tr('Mobile Number')" name="mobile_number" required="true" minlength="9" />
                        <h5><span class="text-muted">{{__tr("Mobile number should be with country code without 0 or +")}}</span></h5>

                    <!-- Username -->
                    <x-lw.input-field type="text" id="lwUsernameField" data-form-group-class=""
                        :label="__tr('Username')" name="username" required="true" minlength="3" />
                    <!-- /Username -->
                    <x-lw.input-field type="text" id="lwEmailField" data-form-group-class="" :label="__tr('Email')"
                        name="email" required="true" minlength="3" />
                    <!-- Password -->
                    <x-lw.input-field type="password" id="lwPasswordField" data-form-group-class=""
                        :label="__tr('Password')" name="password" required="true" minlength="6" />
                    <!-- /Password -->
                    <fieldset>
                        <legend>{{ __tr('Permissions') }}</legend>
                        @foreach (getListOfPermissions() as $permissionKey => $permission)
                        <div class="d-block my-3">
                            <x-lw.checkbox id="lw{{ $permissionKey }}Item" name="permissions[{{ $permissionKey }}]"
                                data-lw-plugin="lwSwitchery" :label="$permission['title']" />
                            @if (isset($permission['description']) and $permission['description'])
                            <p class="text-muted mt-1 fs-1">{{ $permission['description'] }}</p>
                            <hr class="my-1">
                            @endif
                        </div>
                        @endforeach
                    </fieldset>

                    @if(hasCentralAccess())
                    <fieldset>
                        <legend>{{ __tr('Module Access') }}</legend>
                        <div class="row">
                            @foreach (getAssignableModules() as $moduleKey => $moduleTitle)
                                <div class="col-md-6">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="module_permissions[]"
                                            value="{{ $moduleKey }}" id="module{{ ucfirst($moduleKey) }}">
                                        <label class="custom-control-label" for="module{{ ucfirst($moduleKey) }}">
                                            {{ $moduleTitle }}
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        <small class="text-muted">
                            {{ __tr('Select the modules that this user will have access to.') }}
                        </small>
                    </fieldset>
                    @endif
                 
                </div>
                <!-- form footer -->
                <div class="modal-footer">
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary">{{ __('Submit') }}</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
                </div>
            </x-lw.form>
            <!--/  Add New User Form -->
        </x-lw.modal>
        <!--/ Add New User Modal -->
        <!--/ Edit User Modal -->

        <!-- Edit User Modal -->
        <x-lw.modal id="lwEditUser" :header="__tr('Edit Agent & Permissions')" :hasForm="true">
            <!--  Edit User Form -->
            <x-lw.form id="lwEditUserForm" :action="route('vendor.user.write.update')"
                :data-callback-params="['modalId' => '#lwEditUser', 'datatableId' => '#lwUserList']"
                data-callback="appFuncs.modelSuccessCallback">
                <!-- form body -->
                <div id="lwEditUserBody" class="lw-form-modal-body"></div>
                <script type="text/template" id="lwEditUserBody-template">

                    <input type="hidden" name="userIdOrUid" value="<%- __tData._uid %>" />
                        <!-- form fields -->
                        <!-- First_Name -->
           <x-lw.input-field type="text" id="lwFirstNameEditField" data-form-group-class="" :label="__tr('First Name')" value="<%- __tData.first_name %>" name="first_name"  required="true"                 />
                <!-- /First_Name -->
                <!-- Last_Name -->
           <x-lw.input-field type="text" id="lwLastNameEditField" data-form-group-class="" :label="__tr('Last Name')" value="<%- __tData.last_name %>" name="last_name"  required="true"                 />
           <x-lw.input-field type="text" id="lwMobileNumberEditField" data-form-group-class="" :label="__tr('Mobile Number')" value="<%- __tData.mobile_number %>" name="mobile_number"  />
            <h5><span class="text-muted">{{__tr("Mobile number should be with country code without 0 or +")}}</span></h5>

           <x-lw.input-field type="text" id="lwEmailEditField" data-form-group-class="" :label="__tr('Email')" value="<%- __tData.email %>" name="email"  />
           <x-lw.input-field type="password" id="lwPasswordEditField" data-form-group-class="" :label="__tr('Password')"  name="password"  />
                <!-- /Last_Name -->
                <!-- STATUS -->
                <div class="form-group pt-3">
                    <label for="lwIsMemberActiveEditField">{{  __tr('Status') }}</label>
                    <input type="checkbox" id="lwIsMemberActiveEditField" <%- __tData.status == 1 ? 'checked' : '' %> data-lw-plugin="lwSwitchery" name="status">
                </div>
                <!-- /STATUS -->
                <fieldset>
                    <legend>{{  __tr('Permissions') }}</legend>
                    @foreach(getListOfPermissions() as $permissionKey => $permission)
                            <span class="d-block my-3">
                                <label for="lwEdit{{ $permissionKey }}Permission" class="flex items-center">
                                    <input id="lwEdit{{ $permissionKey }}Permission" type="checkbox" <%- (__tData.vendor_user_details?.__data?.permissions?.{{ $permissionKey }} == 'allow') ? 'checked' : '' %> name="permissions[{{ $permissionKey }}]" class="form-checkbox" data-lw-plugin="lwSwitchery">
                                    <span class="ml-2 text-gray-600">{{ $permission['title'] }}</span>
                                </label>
                                @if (isset($permission['description']) and $permission['description'])
                                <p class="text-muted mt-1 fs-1">{{ $permission['description'] }}</p>
                                <hr class="my-1">
                                @endif
                            </span>
                    @endforeach
                </fieldset>

                @if(hasCentralAccess())
                <fieldset>
                    <legend>{{ __tr('Module Access') }}</legend>
                    <div class="row">
                        @foreach (getAssignableModules() as $moduleKey => $moduleTitle)
                            <div class="col-md-6">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" name="module_permissions[]"
                                        value="{{ $moduleKey }}" id="moduleEdit{{ ucfirst($moduleKey) }}"
                                        <%- (__tData.module_permissions && __tData.module_permissions.includes('{{ $moduleKey }}')) ? 'checked' : '' %>>
                                    <label class="custom-control-label" for="moduleEdit{{ ucfirst($moduleKey) }}">
                                        {{ $moduleTitle }}
                                    </label>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <small class="text-muted">
                        {{ __tr('Select the modules that this user will have access to.') }}
                    </small>
                </fieldset>
                @endif
                  
                     </script>
                <!-- form footer -->
                <div class="modal-footer">
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary">{{ __('Submit') }}</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
                </div>
            </x-lw.form>
            <!--/  Edit User Form -->
        </x-lw.modal>
        <!--/ Edit User Modal -->
        <div class="col-xl-12">
            <x-lw.datatable id="lwUserList" class="table-striped table-hover table-bordered" :url="route('vendor.user.read.list')">
                <th data-orderable="true" data-name="first_name">{{ __tr('First Name') }}</th>
                <th data-orderable="true" data-name="last_name">{{ __tr('Last Name') }}</th>
                <th data-orderable="true" data-name="username">{{ __tr('Username') }}</th>
                <th data-orderable="true" data-name="email">{{ __tr('Email') }}</th>
                <th data-orderable="true" data-name="mobile_number">{{ __tr('Mobile Number') }}</th>
                <th data-orderable="true" data-name="created_at">{{ __tr('Created At') }}</th>
                <th data-orderable="true" data-name="status">{{ __tr('Status') }}</th>
                <th data-template="#userActionColumnTemplate" name="null">{{ __tr('Action') }}</th>
            </x-lw.datatable>
        </div>
        <!-- action template -->
        <script type="text/template" id="userActionColumnTemplate">

             <!-- Edit Button -->
            <a 
                data-pre-callback="appFuncs.clearContainer"
                data-tooltip="{!! __tr('Edit User & Permissions') !!}"
                class="btn lw-ajax-link-action custom-tooltip"
                style="
                    background-color: #f8f9fa;
                    color: #0d6efd;
                    border: 1px solid #0d6efd;
                    border-radius: 8px;
                    padding: 8px 14px;
                    margin-right: 6px;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                "
                onmouseover="
                    this.style.backgroundColor='#0d6efd';
                    this.style.color='#fff';
                    this.querySelector('i').style.color = '#fff';
                "
                onmouseout="
                    this.style.backgroundColor='#f8f9fa';
                    this.style.color='#0d6efd';
                    this.querySelector('i').style.color = '#0d6efd';
                "
                data-response-template="#lwEditUserBody"
                href="<%= __Utils.apiURL('{{ route('vendor.user.read.update.data', ['userIdOrUid']) }}', { 'userIdOrUid': __tData._uid }) %>"
                data-toggle="modal"
                data-target="#lwEditUser"
            >
                <i class="fa fa-edit" style="font-size: 14px; color: #0d6efd;"></i>
                {!! __tr('Edit User & Permissions') !!}
            </a>


            <!-- Delete Button -->
            <a 
                data-method="post"
                href="<%= __Utils.apiURL('{{ route('vendor.user.write.delete', [ 'userIdOrUid']) }}', {'userIdOrUid': __tData._uid}) %>"
                class="btn lw-ajax-link-action"
                style="
                background-color: #fff;
                color: #dc3545;
                border: 1px solid #dc3545;
                border-radius: 8px;
                padding: 12px 12px;
                margin-right: 6px;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                display: inline-flex;
                align-items: center;
                gap: 6px;"
                onmouseover="this.style.backgroundColor='#'; this.style.color='#fff'; this.style.transform='scale(1.05)'"
                onmouseout="this.style.backgroundColor='#fff'; this.style.color='#dc3545'; this.style.transform='scale(1)'"
                data-confirm="#lwDeleteUser-template"
                title="{{ __tr('Delete') }}"
                data-callback-params="{{ json_encode(['datatableId' => '#lwUserList']) }}"
                data-callback="appFuncs.modelSuccessCallback"
            >
                <i class="fa fa-trash" style="color: #dc3545;"></i> {{ __tr('') }}
            </a>

            <!-- Login As Button -->
            <% if(__tData.status=='Active') { %>
            <% if(__tData._uid != '{{ getUserUid() }}') { %>
            <a 
                data-method="post"
                href="<%= __Utils.apiURL('{{ route('vendor.user.write.login_as', ['userIdOrUid']) }}', { 'userIdOrUid': __tData._uid }) %>"
                class="btn lw-ajax-link-action"
                style="
                    background-color: #0B7753;
                    color: #ffffff;
                    border: none;
                    border-radius: 12px;
                    padding: 10px 18px;
                    font-weight: 500;
                    font-size: 14px;
                    margin-right: 8px;
                    transition: all 0.3s ease;
                    box-shadow: 0 4px 8px rgba(11, 119, 83, 0.25);
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                "
                onmouseover="
                    this.style.backgroundColor='#095c41';
                    this.style.transform='scale(1.06)';
                    this.style.boxShadow='0 6px 12px rgba(11, 119, 83, 0.35)';
                "
                onmouseout="
                    this.style.backgroundColor='#0B7753';
                    this.style.transform='scale(1)';
                    this.style.boxShadow='0 4px 8px rgba(11, 119, 83, 0.25)';
                "
                data-confirm="#lwLoginAs-template"
                title="{{ __tr('Login as') }}"
            >
                <span style="color: #fff;">
                    <i class="fa fa-sign-in-alt" style="font-size: 14px;"></i> {{ __tr('Login as') }}
                </span>
            </a>

            <% } %>
            <% } %>

        </script>

        <!-- /action template -->

        <!-- User delete template -->
        <script type="text/template" id="lwDeleteUser-template">
            <h2>{{ __tr('Are You Sure!') }}</h2>
            <p>{{ __tr('You want to delete this Agent?') }}</p>
    </script>
        <!-- /User delete template -->
        <script type="text/template" id="lwLoginAs-template">
            <h2>{{ __tr('Are You Sure!') }}</h2>
        <p>{{ __tr('You want login to this user account?') }}</p>
</script>
    </div>
</div>
<style>
    /* Tooltip styling */
    .custom-tooltip {
        position: relative;
        cursor: pointer;
    }

    .custom-tooltip:hover::after {
        content: attr(data-tooltip);
        position: absolute;
        top: -38px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #0d6efd;
        color: #fff;
        padding: 6px 10px;
        font-size: 12px;
        border-radius: 4px;
        white-space: nowrap;
        z-index: 9999;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    table.dataTable thead {
        background-color: #e9f2fd;
        color: #0d6efd;
        font-weight: bold;
    }

    /* DataTable Rows */
    table.dataTable tbody tr {
        background-color: #ffffff;
        transition: background-color 0.3s ease;
    }

    table.dataTable tbody tr:hover {
        background-color: #f1f9ff;
    }

    /* Table Border and Padding */
    table.dataTable {
        border: 1px solid #e3eaf5;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    table.dataTable td,
    table.dataTable th {
        padding: 12px 16px;
    }

    /* Pagination Buttons */
    .dataTables_paginate .paginate_button {
        background-color: #f4f9ff;
        border: 1px solid #d0e3ff;
        color: #0d6efd !important;
        margin: 0 2px;
        border-radius: 6px;
        padding: 6px 10px;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .dataTables_paginate .paginate_button:hover {
        background-color: #d0e3ff;
        color: #003e9b !important;
    }

    .dataTables_paginate .paginate_button.current {
        background-color: #0d6efd;
        color: #fff !important;
        border-color: #0d6efd;
    }

    /* Search Filter */
    .dataTables_filter input {
        border: 1px solid #cddfee;
        border-radius: 6px;
        padding: 6px 10px;
        font-size: 14px;
        width: 200px;
        transition: box-shadow 0.2s ease;
    }

    .dataTables_filter input:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
        border-color: #0d6efd;
    }

    /* Table Length Selector */
    .dataTables_length select {
        border: 1px solid #cddfee;
        border-radius: 6px;
        padding: 4px 8px;
    }

    /* Info Text */
    .dataTables_info {
        color: #6c757d;
        font-size: 14px;
        padding-top: 10px;
    }
</style>
@endsection()